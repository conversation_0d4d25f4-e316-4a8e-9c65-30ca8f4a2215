package main

//go:generate go run github.com/swaggo/swag/cmd/swag init -o ../../docs/ -g cmd/http/main.go -d ../../
import (
	"context"
	"embed"
	"io/fs"
	"mime"
	h "net/http"
	"os"
	"path/filepath"
	"time"

	v "github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	md "github.com/labstack/echo/v4/middleware"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	echoSwagger "github.com/swaggo/echo-swagger"
	"go.opentelemetry.io/contrib/instrumentation/github.com/labstack/echo/otelecho"

	_ "github.com/Lionparcel/pentools/docs"
	authHTTP "github.com/Lionparcel/pentools/internal/delivery/auth/http"
	engHTTP "github.com/Lionparcel/pentools/internal/delivery/eng/http"
	jiraHTTP "github.com/Lionparcel/pentools/internal/delivery/jira/http"
	releasesHTTP "github.com/Lionparcel/pentools/internal/delivery/releases/http"
	saHTTP "github.com/Lionparcel/pentools/internal/delivery/sa/http"
	sdetHTTP "github.com/Lionparcel/pentools/internal/delivery/sdet/http"
	authRepo "github.com/Lionparcel/pentools/internal/repository/auth"
	"github.com/Lionparcel/pentools/internal/repository/jira"
	opensearchRepo "github.com/Lionparcel/pentools/internal/repository/opensearch"
	"github.com/Lionparcel/pentools/internal/repository/sdet"
	authUsecase "github.com/Lionparcel/pentools/internal/usecase/auth"
	engUsecase "github.com/Lionparcel/pentools/internal/usecase/eng"
	jiraUsecase "github.com/Lionparcel/pentools/internal/usecase/jira"
	releasesUsecase "github.com/Lionparcel/pentools/internal/usecase/releases"
	saUsecase "github.com/Lionparcel/pentools/internal/usecase/sa"
	sdetUsecase "github.com/Lionparcel/pentools/internal/usecase/sdet"
	"github.com/Lionparcel/pentools/pkg/auth"
	"github.com/Lionparcel/pentools/pkg/config"
	"github.com/Lionparcel/pentools/pkg/database/postgresql"
	"github.com/Lionparcel/pentools/pkg/log"
	"github.com/Lionparcel/pentools/pkg/middleware"
	"github.com/Lionparcel/pentools/pkg/telemetry"
	"github.com/Lionparcel/pentools/pkg/webauthn"
)

//go:embed dist/*
var distFS embed.FS

// CustomValidator wraps the validator
type CustomValidator struct {
	validator *v.Validate
}

// Validate implements echo's Validator interface
func (cv *CustomValidator) Validate(i interface{}) error {
	if err := cv.validator.Struct(i); err != nil {
		return echo.NewHTTPError(h.StatusBadRequest, err.Error())
	}
	return nil
}

// @title Platform Engineering Tools
// @version 1.0
// @description Collection of Platform Engineering Tools
func main() {
	cfg, err := config.New("./config/app.toml")
	if err != nil {
		panic(err)
	}
	err = log.New(cfg)
	if err != nil {
		panic(err)
	}
	telemetryShutdown, err := telemetry.New(context.Background(), cfg)
	if err != nil {
		log.Fatal(context.Background(), "error while initial telemetry", err)
	}
	defer func() {
		if shutErr := telemetryShutdown(context.Background()); shutErr != nil {
			log.Error(context.Background(), "telemetry shutdown error", shutErr)
		}
	}()
	sdetDB, err := postgresql.New(cfg)
	if err != nil && cfg.App.Env == "production" {
		panic(err)
	}
	e := echo.New()
	
	// Set up validator
	e.Validator = &CustomValidator{validator: v.New()}
	
	setupEcho(cfg, e, sdetDB)
	e.Logger.Fatal(e.Start(":" + cfg.App.Port))
}

func setupEcho(cfg *config.Config, e *echo.Echo, sdetDB *postgresql.Connections) {
	e.GET("/swagger/*", echoSwagger.WrapHandler)
	useOS := len(os.Args) > 1 && os.Args[1] == "live"
	fsys := getFileSystem(useOS)

	const indexHTML = "index.html"
	e.GET("/*", func(c echo.Context) error {
		path := c.Param("*")
		if path == "" || path == "/" {
			path = indexHTML
		}

		// First try to serve .html file if path doesn't have .html extension
		if !hasHTMLExt(path) {
			htmlPath := path + ".html"
			f, err := fsys.Open(htmlPath)
			if err == nil {
				defer f.Close()
				return c.Stream(h.StatusOK, "text/html", f)
			}
		}

		// Then try the exact path
		f, err := fsys.Open(path)
		if err == nil {
			defer f.Close()
			contentType := detectContentType(path)
			// Force HTML content type for files without extension that should be HTML
			if contentType == "application/octet-stream" && (path == "index.html" || hasHTMLExt(path)) {
				contentType = "text/html"
			}
			return c.Stream(h.StatusOK, contentType, f)
		}

		// Fallback to index.html for SPA routing
		f3, err3 := fsys.Open(indexHTML)
		if err3 == nil {
			defer f3.Close()
			return c.Stream(h.StatusOK, "text/html", f3)
		}
		return c.NoContent(h.StatusNotFound)
	})
	e.GET("/metric", func(c echo.Context) error {
		promhttp.Handler().ServeHTTP(c.Response(), c.Request())
		return nil
	})
	e.Use(md.Recover())
	e.Use(otelecho.Middleware(cfg.App.Name))
	e.Use(middleware.Metric)
	e.Use(middleware.RequestID)
	e.Use(middleware.Logger)

	// CORS configuration for WebAuthn
	if cfg.App.Env != "production" {
		e.Use(md.CORS())
	}

	// Initialize WebAuthn authentication system
	if sdetDB != nil && sdetDB.Master != nil {
		// Initialize WebAuthn configuration
		webauthnConfig := &webauthn.Config{
			RPDisplayName:            cfg.WebAuthn.RPDisplayName,
			RPID:                     cfg.WebAuthn.RPID,
			RPOrigins:                cfg.WebAuthn.RPOrigins,
			AuthenticatorAttachment:  cfg.WebAuthn.AuthenticatorAttachment,
			UserVerification:         cfg.WebAuthn.UserVerification,
			ResidentKeyRequirement:   cfg.WebAuthn.ResidentKey,
			Timeout:                  time.Duration(cfg.WebAuthn.TimeoutMs) * time.Millisecond,
			AttestationPreference:    cfg.WebAuthn.AttestationPreference,
			RequireResidentKey:       cfg.WebAuthn.RequireResidentKey,
			Debug:                    cfg.WebAuthn.Debug,
		}

		webauthnService, err := webauthn.New(webauthnConfig)
		if err != nil {
			log.Fatal(context.Background(), "failed to initialize WebAuthn service", err)
		}

		// Initialize JWT manager
		jwtManager := auth.NewJWTManager(
			cfg.Auth.JWTSecret,
			time.Duration(cfg.Auth.JWTExpirationHours)*time.Hour,
			cfg.App.Name,
		)

		// Initialize auth repository
		authRepository := authRepo.New(sdetDB)

		// Initialize auth use case
		authUC := authUsecase.New(authRepository, jwtManager, webauthnService, cfg)

		// Initialize auth HTTP handlers
		authHTTP.New(e, authUC)

		log.Info(context.Background(), "WebAuthn authentication system initialized successfully")
	} else {
		log.Warn(context.Background(), "Database connection unavailable, skipping WebAuthn authentication system initialization")
	}

	jira := jira.NewAPI(cfg)
	jiraUsecase := jiraUsecase.New(cfg, jira)
	jiraHTTP.New(cfg, e, jiraUsecase)

	sdetDatabase := sdet.NewDatabase(cfg, sdetDB)
	sdetUsecase := sdetUsecase.New(cfg, sdetDatabase)
	sdetHTTP.New(cfg, e, sdetUsecase)

	saUsecase := saUsecase.New(cfg, jira)
	saHTTP.New(cfg, e, saUsecase)

	engJiraService := engUsecase.NewJiraService(jira)
	engUC := engUsecase.New(engJiraService)
	engHTTP.New(cfg, e, engUC)

	// releases (OpenSearch)
	if repo, err := opensearchRepo.NewReleasesRepository(cfg); err == nil {
		relUC := releasesUsecase.New(cfg, repo)
		releasesHTTP.New(cfg, e, relUC)
	}

}

func hasHTMLExt(path string) bool {
	n := len(path)
	return n > 5 && path[n-5:] == ".html"
}

func detectContentType(path string) string {
	ext := filepath.Ext(path)
	if ext != "" {
		if ctype := mime.TypeByExtension(ext); ctype != "" {
			return ctype
		}
	}
	// fallback for .html and unknown
	if hasHTMLExt(path) || path == "index.html" {
		return "text/html"
	}
	return "application/octet-stream"
}

func getFileSystem(useOS bool) h.FileSystem {
	if useOS {
		log.Info(context.Background(), "using live mode")
		return h.FS(os.DirFS("dist"))
	}

	log.Info(context.Background(), "using embed mode")
	fsys, err := fs.Sub(distFS, "dist")
	if err != nil {
		panic(err)
	}

	return h.FS(fsys)
}
