{"permissions": {"allow": ["Bash(find:*)", "Bash(pnpm add:*)", "Bash(cd:*)", "Bash(cd:*)", "Bash(cd:*)", "Bash(cd:*)", "Bash(cd:*)", "Bash(pnpm run lint:*)", "Bash(pnpm run:*)", "<PERSON><PERSON>(make:*)", "Bash(node:*)", "Bash(pnpm remove:*)", "WebSearch", "mcp__ide__executeCode", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "WebFetch(domain:ui.shadcn.com)", "<PERSON><PERSON>(timeout 10s pnpm run dev)", "<PERSON><PERSON>(python3:*)", "Bash(pip3 install:*)", "Bash(go mod:*)", "Bash(go get:*)", "Bash(go list:*)", "<PERSON><PERSON>(go doc:*)", "Bash(go build:*)"], "deny": []}}