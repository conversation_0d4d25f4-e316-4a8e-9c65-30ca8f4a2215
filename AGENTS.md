# AGENTS.md

This file provides guidance to AI coding agents working with the pentools repository.

## Project Overview

pentools is a comprehensive full-stack engineering analytics platform that integrates with Jira to provide performance metrics, bug tracking, team reporting, and task management tools. The application uses Go (Echo framework) for the backend and Next.js 15 for the frontend, following clean architecture principles with domain-driven design.

## Build and Test Commands

### Backend (Go)
- `make run`: Generate Swagger docs and run the Go server
- `make generate`: Generate Swagger documentation from code annotations  
- `make test-race`: Run tests with race condition detection
- `go run ./cmd/http/main.go`: Run HTTP server directly

### Frontend (Next.js)
- `make fe-install`: Install frontend dependencies using pnpm
- `make fe-run`: Run Next.js development server with Turbopack
- `make fe-build`: Build frontend and copy to `cmd/http/dist` for embedding
- `make fe-lint`: Run ESLint with auto-fix

### Full Stack
- `make run-all`: Complete build and run pipeline (frontend build + backend run)

## Architecture

### Backend Structure
- `cmd/http/main.go`: Entry point with embedded frontend files and API routing
- `internal/delivery/`: HTTP handlers by domain (eng, jira, sdet, sa)
- `internal/usecase/`: Business logic layer
- `internal/repository/`: Data access layer (Jira API, PostgreSQL)
- `internal/dto/`: Data transfer objects
- `pkg/`: Shared utilities (config, database, logging, telemetry)

### Frontend Structure (located in `/frontend/`)
- `src/app/`: App Router pages organized by domain (eng, pe, force, sdet, sre, jira-task-maker, json-formatter)
- `src/components/`: Comprehensive component library including:
  - `auth/`: Authentication components (auth-guard, login-form)
  - `performance-analysis/`: Team analytics and metrics components
  - `jira-task-maker/`: Multi-step task creation wizards
  - `json-formatter/`: JSON formatting and validation tools
  - `ui/`: Complete shadcn/ui component library (40+ components)
- `src/types/`: Domain-specific TypeScript definitions (eng, bug, epic)
- `src/hooks/`: Custom React hooks for common functionality
- `src/lib/`: Utility functions and navigation helpers
- Redux Toolkit + Redux Persist for authentication and filter state
- TanStack Query for server state and API calls
- CodeMirror for advanced JSON editing capabilities

## Code Style Guidelines

### Go
- Follow clean architecture patterns with clear separation of concerns
- Use testify framework for unit tests
- Generate Swagger docs from code annotations
- Implement proper error handling and logging
- Use OpenTelemetry for observability

### TypeScript/React
- Use TypeScript for all frontend code with strict type checking
- Follow Next.js 15 App Router conventions with domain-based routing
- Use shadcn/ui components for consistent, accessible UI design
- Implement proper state management with Redux Toolkit and persistence
- Use TanStack Query for optimized API calls and caching
- Follow React best practices with hooks, context, and component composition
- Implement responsive design with Tailwind CSS v4
- Use CodeMirror for syntax highlighting and JSON editing

## Testing Instructions

- Go tests are located in `internal/usecase/eng/*_test.go`
- Run `make test-race` for race condition testing with `GOMAXPROCS=4`
- Test suites provide comprehensive coverage
- Use testify framework for assertions and mocking

## Configuration

- Configuration files are TOML format in `config/` directory
- Environment-specific configs supported (local, production)
- Configure database connections, Jira credentials, and app settings
- Configuration is loaded at application startup

## Domain Areas

- **eng/**: Engineering team performance analytics (bugs, epics, leaderboards, analytics)
- **pe/**: Product Engineering team analysis with team overview and member tracking
- **force/**: Force team performance analysis with detailed member metrics
- **sdet/**: Software Development Engineer in Test metrics, sprint analysis, and task reporting
- **sre/**: Site Reliability Engineering team performance analysis
- **jira/**: Direct Jira integration, task creation wizards, and reporting capabilities
- **sa/**: System Administrator tools and metrics
- **json-formatter/**: JSON formatting, validation, and syntax highlighting utilities
- **login/**: User authentication and session management

## Key Integrations

- **Jira API**: Primary data source for engineering metrics, work logs, and task creation
- **PostgreSQL**: Database for SDET-specific data with GORM ORM integration
- **OpenTelemetry**: Comprehensive observability with Prometheus metrics, logging, and distributed tracing
- **Swagger**: Auto-generated API documentation from code annotations using swag
- **Echo Framework**: High-performance Go web framework with middleware support
- **Go-Cache**: In-memory caching layer for performance optimization
- **Tailwind CSS v4**: Modern utility-first CSS framework with PostCSS
- **CodeMirror**: Advanced code editor with JSON syntax highlighting

## Security Considerations

- Jira credentials should be configured via TOML config files (see `config/app.toml.example`)
- Database connections use proper connection pooling with GORM
- API endpoints implement authentication middleware and request validation
- Sensitive configuration data should not be committed to repository
- Frontend implements auth guards and protected routes
- All API requests include proper error handling and validation
- OpenTelemetry traces exclude sensitive data from logs
- CORS configuration restricts cross-origin requests appropriately