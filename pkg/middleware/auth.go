package middleware

import (
	"context"
	"fmt"
	"net/http"
	"slices"
	"strings"

	"github.com/Lionparcel/pentools/pkg/auth"
	"github.com/labstack/echo/v4"
)

// AuthContextKey is the key for storing user info in context
type AuthContextKey string

const (
	UserContextKey AuthContextKey = "user"
)

// UserContext represents authenticated user information in context
type UserContext struct {
	ID       uint   `json:"id"`
	Username string `json:"username"`
	Role     string `json:"role"`
}

// JWTAuthMiddleware returns middleware that validates JWT tokens
func JWTAuthMiddleware(jwtManager *auth.JWTManager) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Get Authorization header
			authHeader := c.Request().Header.Get("Authorization")
			if authHeader == "" {
				return echo.NewHTTPError(http.StatusUnauthorized, "missing authorization header")
			}

			// Extract token from Bearer header
			token, err := auth.ExtractTokenFromBearer(authHeader)
			if err != nil {
				return echo.NewHTTPError(http.StatusUnauthorized, fmt.Sprintf("invalid authorization header: %v", err))
			}

			// Validate token
			claims, err := jwtManager.ValidateToken(token)
			if err != nil {
				if auth.IsTokenExpiredError(err) {
					return echo.NewHTTPError(http.StatusUnauthorized, "token expired")
				}
				if auth.IsTokenInvalidError(err) {
					return echo.NewHTTPError(http.StatusUnauthorized, "invalid token")
				}
				return echo.NewHTTPError(http.StatusUnauthorized, fmt.Sprintf("token validation failed: %v", err))
			}

			// Store user info in context
			userCtx := &UserContext{
				ID:       claims.UserID,
				Username: claims.Username,
				Role:     claims.Role,
			}

			c.Set(string(UserContextKey), userCtx)

			// Also set in request context for use in other parts of the application
			ctx := context.WithValue(c.Request().Context(), UserContextKey, userCtx)
			c.SetRequest(c.Request().WithContext(ctx))

			return next(c)
		}
	}
}

// RequireRoleMiddleware returns middleware that requires specific roles
func RequireRoleMiddleware(allowedRoles ...string) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Get user from context
			userCtx, err := GetUserFromContext(c)
			if err != nil {
				return echo.NewHTTPError(http.StatusUnauthorized, "user not found in context")
			}

			// Check if user has required role
			if !hasRole(userCtx.Role, allowedRoles) {
				return echo.NewHTTPError(http.StatusForbidden, fmt.Sprintf("insufficient permissions: requires one of %v", allowedRoles))
			}

			return next(c)
		}
	}
}

// RequireAdminMiddleware returns middleware that requires admin role
func RequireAdminMiddleware() echo.MiddlewareFunc {
	return RequireRoleMiddleware("admin")
}

// RequireUserMiddleware returns middleware that requires user or admin role
func RequireUserMiddleware() echo.MiddlewareFunc {
	return RequireRoleMiddleware("user", "admin")
}

// GetUserFromContext extracts user information from Echo context
func GetUserFromContext(c echo.Context) (*UserContext, error) {
	user := c.Get(string(UserContextKey))
	if user == nil {
		return nil, fmt.Errorf("user not found in context")
	}

	userCtx, ok := user.(*UserContext)
	if !ok {
		return nil, fmt.Errorf("invalid user type in context")
	}

	return userCtx, nil
}

// GetUserFromRequestContext extracts user information from request context
func GetUserFromRequestContext(ctx context.Context) (*UserContext, error) {
	user := ctx.Value(UserContextKey)
	if user == nil {
		return nil, fmt.Errorf("user not found in context")
	}

	userCtx, ok := user.(*UserContext)
	if !ok {
		return nil, fmt.Errorf("invalid user type in context")
	}

	return userCtx, nil
}

// OptionalAuthMiddleware provides optional authentication - doesn't fail if no auth provided
func OptionalAuthMiddleware(jwtManager *auth.JWTManager) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Get Authorization header
			authHeader := c.Request().Header.Get("Authorization")
			if authHeader == "" {
				// No auth header - continue without user context
				return next(c)
			}

			// Extract token from Bearer header
			token, err := auth.ExtractTokenFromBearer(authHeader)
			if err != nil {
				// Invalid format - continue without user context
				return next(c)
			}

			// Validate token
			claims, err := jwtManager.ValidateToken(token)
			if err != nil {
				// Invalid token - continue without user context
				return next(c)
			}

			// Store valid user info in context
			userCtx := &UserContext{
				ID:       claims.UserID,
				Username: claims.Username,
				Role:     claims.Role,
			}

			c.Set(string(UserContextKey), userCtx)

			ctx := context.WithValue(c.Request().Context(), UserContextKey, userCtx)
			c.SetRequest(c.Request().WithContext(ctx))

			return next(c)
		}
	}
}

// CORSWithAuthMiddleware adds CORS headers that work with authentication
func CORSWithAuthMiddleware() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			req := c.Request()
			res := c.Response()

			// Set CORS headers
			origin := req.Header.Get("Origin")
			allowedOrigins := []string{
				"http://localhost:3000",
				"https://localhost:3000",
				"http://127.0.0.1:3000",
				"https://127.0.0.1:3000",
			}

			// Check if origin is allowed
			originAllowed := slices.Contains(allowedOrigins, origin)
			if originAllowed {
				res.Header().Set("Access-Control-Allow-Origin", origin)
			}

			res.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, PATCH, OPTIONS")
			res.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
			res.Header().Set("Access-Control-Allow-Credentials", "true")
			res.Header().Set("Access-Control-Max-Age", "3600")

			// Handle preflight requests
			if req.Method == "OPTIONS" {
				return c.NoContent(http.StatusNoContent)
			}

			return next(c)
		}
	}
}

// hasRole checks if a user has one of the allowed roles
func hasRole(userRole string, allowedRoles []string) bool {
	userRole = strings.ToLower(strings.TrimSpace(userRole))

	for _, role := range allowedRoles {
		if userRole == strings.ToLower(strings.TrimSpace(role)) {
			return true
		}
	}
	return false
}

// IsAdmin checks if the user has admin role
func IsAdmin(userCtx *UserContext) bool {
	return userCtx != nil && strings.ToLower(strings.TrimSpace(userCtx.Role)) == "admin"
}

// IsUser checks if the user has user or admin role
func IsUser(userCtx *UserContext) bool {
	if userCtx == nil {
		return false
	}

	role := strings.ToLower(strings.TrimSpace(userCtx.Role))
	return role == "user" || role == "admin"
}

// AuthorizeUserOrAdmin middleware that allows access to own resources or admin
func AuthorizeUserOrAdmin(getUserID func(c echo.Context) (uint, error)) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Get current user from context
			userCtx, err := GetUserFromContext(c)
			if err != nil {
				return echo.NewHTTPError(http.StatusUnauthorized, "authentication required")
			}

			// Admin can access everything
			if IsAdmin(userCtx) {
				return next(c)
			}

			// Regular user can only access their own resources
			targetUserID, err := getUserID(c)
			if err != nil {
				return echo.NewHTTPError(http.StatusBadRequest, "invalid user ID")
			}

			if userCtx.ID != targetUserID {
				return echo.NewHTTPError(http.StatusForbidden, "access denied: can only access own resources")
			}

			return next(c)
		}
	}
}
