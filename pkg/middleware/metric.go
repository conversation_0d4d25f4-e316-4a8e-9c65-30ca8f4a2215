package middleware

import (
	"fmt"
	"reflect"
	"time"

	"github.com/Lionparcel/pentools/pkg/telemetry"
	"github.com/labstack/echo/v4"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/metric"
	semconv "go.opentelemetry.io/otel/semconv/v1.24.0"
)

func Metric(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		req := c.Request()
		res := c.Response()
		start := time.Now()
		err := next(c)
		stop := time.Now()
		meter := telemetry.GetMeter()

		serverRequestHistogram, _ := meter.Float64Histogram(semconv.HTTPServerRequestDurationName, metric.WithUnit(semconv.HTTPServerRequestDurationUnit), metric.WithDescription(semconv.HTTPServerRequestDurationDescription))
		serverRequestBodySizeHistogram, _ := meter.Int64Histogram(semconv.HTTPServerRequestBodySizeName, metric.WithUnit(semconv.HTTPServerRequestBodySizeUnit), metric.WithDescription(semconv.HTTPServerRequestBodySizeDescription))
		serverResponseBodySizeHistogram, _ := meter.Int64Histogram(semconv.HTTPServerResponseBodySizeName, metric.WithUnit(semconv.HTTPServerRequestBodySizeUnit), metric.WithDescription(semconv.HTTPServerResponseBodySizeDescription))

		attr := []attribute.KeyValue{
			semconv.HTTPMethodKey.String(req.Method),
			semconv.URLScheme(req.URL.Scheme),
			semconv.HTTPRouteKey.String(c.Path()),
		}
		if err != nil {
			c.Error(err)
			attr = append(attr, semconv.ErrorTypeKey.String(fmt.Sprint(reflect.TypeOf(err))))
		}
		attr = append(attr, semconv.HTTPStatusCodeKey.Int(res.Status))
		serverRequestHistogram.Record(req.Context(), stop.Sub(start).Seconds(), metric.WithAttributes(attr...))
		serverRequestBodySizeHistogram.Record(req.Context(), req.ContentLength, metric.WithAttributes(attr...))
		serverResponseBodySizeHistogram.Record(req.Context(), res.Size, metric.WithAttributes(attr...))

		return err
	}
}
