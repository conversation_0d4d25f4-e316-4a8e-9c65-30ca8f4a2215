package middleware

import (
	"bytes"
	"io"
	"strconv"
	"time"

	"github.com/Lionparcel/pentools/pkg/log"
	"github.com/labstack/echo/v4"
)

const (
	maxLogSize = 128 * 100 // 128KB
)

// CustomResponseWriter is a wrapper for http.ResponseWriter to capture the response body.
type CustomResponseWriter struct {
	echo.Response
	body *bytes.Buffer
}

// Write captures the response body.
func (crw *CustomResponseWriter) Write(b []byte) (int, error) {
	crw.body.Write(b)
	return crw.Response.Writer.Write(b)
}

func Logger(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		req := c.Request()
		res := c.Response()

		// only log when content type is json and body is not nil
		// since we don't want to log the request body for large files
		var reqBody []byte
		reqBodySize, _ := strconv.ParseInt(req.Header.Get(echo.HeaderContentLength), 10, 64)

		if reqBodySize < maxLogSize && req.Body != nil {
			reqBody, _ = io.ReadAll(req.Body)
			// Restore the request body for the next handler
			req.Body = io.NopCloser(bytes.NewBuffer(reqBody))
		}

		// Create custom response writer to capture response body
		resBody := new(bytes.Buffer)
		customWriter := &CustomResponseWriter{
			Response: *res,
			body:     resBody,
		}
		c.Response().Writer = customWriter

		start := time.Now()
		err := next(c)
		stop := time.Now()

		p := req.URL.Path
		if p == "" {
			p = "/"
		}

		// Get request headers
		headers := make(map[string]string)
		for k, v := range req.Header {
			if len(v) > 0 {
				headers[k] = v[0]
			}
		}
		if headers["Authorization"] != "" {
			headers["Authorization"] = "Bearer ***"
		}

		kv := []log.KeyValue{
			{Key: "remote_ip", Value: c.RealIP()},
			{Key: "host", Value: req.Host},
			{Key: "uri", Value: req.RequestURI},
			{Key: "method", Value: req.Method},
			{Key: "path", Value: p},
			{Key: "route", Value: c.Path()},
			{Key: "protocol", Value: req.Proto},
			{Key: "referer", Value: req.Referer()},
			{Key: "user_agent", Value: req.UserAgent()},
			{Key: "latency", Value: strconv.FormatInt(int64(stop.Sub(start)), 10)},
			{Key: "latency_human", Value: stop.Sub(start).String()},
			{Key: "bytes_in", Value: req.Header.Get(echo.HeaderContentLength)},
			{Key: "bytes_out", Value: res.Size},
			{Key: "request_headers", Value: headers},
			{Key: "request_body", Value: string(reqBody)},
		}

		if err != nil {
			c.Error(err)
			kv = append(kv, log.KeyValue{Key: "response_body", Value: resBody.String()}, log.KeyValue{Key: "status", Value: c.Response().Status})
			log.Error(req.Context(), c.Path(), err, kv...)
		} else {
			kv = append(kv, log.KeyValue{Key: "status", Value: c.Response().Status})
			log.Info(req.Context(), c.Path(), kv...)
		}

		return err
	}
}
