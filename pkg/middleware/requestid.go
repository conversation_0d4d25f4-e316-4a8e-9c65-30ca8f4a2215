package middleware

import (
	"context"

	"github.com/Lionparcel/pentools/internal/model"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
)

func RequestID(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		reqId := uuid.NewString()
		c.SetRequest(c.Request().WithContext(context.WithValue(c.Request().Context(), model.ContextKeyRequestID, reqId)))
		return next(c)
	}
}
