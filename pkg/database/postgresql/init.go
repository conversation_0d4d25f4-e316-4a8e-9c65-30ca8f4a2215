package postgresql

import (
	"fmt"
	"time"

	"github.com/Lionparcel/pentools/pkg/config"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/plugin/opentelemetry/tracing"
)

type Connections struct {
	master *gorm.DB
	Slave  *gorm.DB
}

func New(cfg *config.Config) (*Connections, error) {
	masterDSN := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%d sslmode=disable",
		cfg.SDET.DatabaseMaster.Host,
		cfg.SDET.DatabaseMaster.User,
		cfg.SDET.DatabaseMaster.Password,
		cfg.SDET.DatabaseMaster.Database,
		cfg.SDET.DatabaseMaster.Port)

	master, err := newConnection(masterDSN)
	if err != nil {
		return nil, err
	}
	masterDB, err := master.DB()
	if err != nil {
		return nil, err
	}
	masterDB.SetConnMaxIdleTime(time.Duration(cfg.SDET.DatabaseMaster.ConnectionLifeTimeInSecond) * time.Second)
	masterDB.SetMaxIdleConns(cfg.SDET.DatabaseMaster.MaxIdleConnection)
	masterDB.SetMaxOpenConns(cfg.SDET.DatabaseMaster.MaxOpenConnection)

	connections := &Connections{}
	connections.master = master

	slaveDSN := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%d sslmode=disable",
		cfg.SDET.DatabaseSlave.Host,
		cfg.SDET.DatabaseSlave.User,
		cfg.SDET.DatabaseSlave.Password,
		cfg.SDET.DatabaseSlave.Database,
		cfg.SDET.DatabaseSlave.Port)

	slave, err := newConnection(slaveDSN)
	if err != nil {
		return nil, err
	}
	slaveDB, err := slave.DB()
	if err != nil {
		return nil, err
	}
	slaveDB.SetConnMaxIdleTime(time.Duration(cfg.SDET.DatabaseSlave.ConnectionLifeTimeInSecond) * time.Second)
	slaveDB.SetMaxIdleConns(cfg.SDET.DatabaseSlave.MaxIdleConnection)
	slaveDB.SetMaxOpenConns(cfg.SDET.DatabaseSlave.MaxOpenConnection)

	connections.Slave = slave

	return connections, nil
}

func newConnection(dsn string) (*gorm.DB, error) {
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}
	if plugErr := db.Use(tracing.NewPlugin()); plugErr != nil {
		return nil, fmt.Errorf("failed to add tracing to database: %w", plugErr)
	}
	return db, nil
}
