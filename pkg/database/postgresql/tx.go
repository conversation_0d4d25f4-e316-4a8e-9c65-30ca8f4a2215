package postgresql

import (
	"context"

	"gorm.io/gorm"
)

type ctxKey string

const txKey ctxKey = "tx"

func (c *Connections) Transaction(ctx context.Context, fn func(ctx context.Context) error) error {
	tx := c.master.WithContext(ctx).Begin()
	ctx = context.WithValue(ctx, txKey, tx)
	err := fn(ctx)
	if err != nil {
		tx.Rollback()
		return err
	}
	return tx.Commit().Error
}
func (c *Connections) Master(ctx context.Context) *gorm.DB {
	tx, ok := ctx.Value(txKey).(*gorm.DB)
	if !ok {
		return c.master
	}
	return tx
}
