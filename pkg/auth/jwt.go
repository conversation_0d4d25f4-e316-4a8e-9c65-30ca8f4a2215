package auth

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// JWTManager handles JWT token generation and validation
type J<PERSON>TManager struct {
	secretKey    []byte
	tokenExpiry  time.Duration
	issuer       string
}

// JWTClaims represents the JWT claims structure
type JWTClaims struct {
	UserID   uint   `json:"user_id"`
	Username string `json:"username"`
	Role     string `json:"role"`
	jwt.RegisteredClaims
}

// NewJWTManager creates a new JWT manager
func NewJWTManager(secretKey string, tokenExpiry time.Duration, issuer string) *JWTManager {
	return &JWTManager{
		secretKey:   []byte(secretKey),
		tokenExpiry: tokenExpiry,
		issuer:      issuer,
	}
}

// GenerateToken generates a JWT token for a user
func (j *JWTManager) GenerateToken(userID uint, username, role string) (string, error) {
	now := time.Now()
	claims := JWTClaims{
		UserID:   userID,
		Username: username,
		Role:     role,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    j.issuer,
			Subject:   fmt.Sprintf("%d", userID),
			Audience:  []string{j.issuer},
			ExpiresAt: jwt.NewNumericDate(now.Add(j.tokenExpiry)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(j.secretKey)
}

// GenerateAccessToken generates an access token with standard expiry
func (j *JWTManager) GenerateAccessToken(claims JWTClaims) (string, error) {
	now := time.Now()
	claims.RegisteredClaims = jwt.RegisteredClaims{
		Issuer:    j.issuer,
		Subject:   fmt.Sprintf("%d", claims.UserID),
		Audience:  []string{j.issuer},
		ExpiresAt: jwt.NewNumericDate(now.Add(j.tokenExpiry)),
		IssuedAt:  jwt.NewNumericDate(now),
		NotBefore: jwt.NewNumericDate(now),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(j.secretKey)
}

// GenerateRefreshToken generates a refresh token with longer expiry
func (j *JWTManager) GenerateRefreshToken(claims JWTClaims) (string, error) {
	now := time.Now()
	refreshExpiry := j.tokenExpiry * 7 // 7x longer than access token
	claims.RegisteredClaims = jwt.RegisteredClaims{
		Issuer:    j.issuer,
		Subject:   fmt.Sprintf("%d", claims.UserID),
		Audience:  []string{j.issuer + "-refresh"},
		ExpiresAt: jwt.NewNumericDate(now.Add(refreshExpiry)),
		IssuedAt:  jwt.NewNumericDate(now),
		NotBefore: jwt.NewNumericDate(now),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(j.secretKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %w", err)
	}

	return tokenString, nil
}

// ValidateToken validates and parses a JWT token
func (j *JWTManager) ValidateToken(tokenString string) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Verify signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return j.secretKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("invalid token: %w", err)
	}

	claims, ok := token.Claims.(*JWTClaims)
	if !ok || !token.Valid {
		return nil, fmt.Errorf("invalid token claims")
	}

	// Additional validation
	if claims.ExpiresAt != nil && claims.ExpiresAt.Time.Before(time.Now()) {
		return nil, fmt.Errorf("token has expired")
	}

	if claims.IssuedAt != nil && claims.IssuedAt.Time.After(time.Now()) {
		return nil, fmt.Errorf("token used before issued")
	}

	if claims.NotBefore != nil && claims.NotBefore.Time.After(time.Now()) {
		return nil, fmt.Errorf("token not valid yet")
	}

	return claims, nil
}

// RefreshToken generates a new token with extended expiry for an existing valid token
func (j *JWTManager) RefreshToken(tokenString string) (string, error) {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return "", fmt.Errorf("cannot refresh invalid token: %w", err)
	}

	// Check if token is close to expiry (within 30 minutes)
	if claims.ExpiresAt != nil && time.Until(claims.ExpiresAt.Time) > 30*time.Minute {
		return "", fmt.Errorf("token is not close to expiry, refresh not needed")
	}

	// Generate new token with same user info
	return j.GenerateToken(claims.UserID, claims.Username, claims.Role)
}

// GetTokenExpiry returns the token expiry duration
func (j *JWTManager) GetTokenExpiry() time.Duration {
	return j.tokenExpiry
}

// ExtractTokenFromBearer extracts JWT token from Authorization Bearer header
func ExtractTokenFromBearer(authHeader string) (string, error) {
	const bearerPrefix = "Bearer "
	if len(authHeader) < len(bearerPrefix) {
		return "", fmt.Errorf("invalid authorization header format")
	}

	if authHeader[:len(bearerPrefix)] != bearerPrefix {
		return "", fmt.Errorf("authorization header must start with Bearer")
	}

	token := authHeader[len(bearerPrefix):]
	if token == "" {
		return "", fmt.Errorf("empty token")
	}

	return token, nil
}

// IsTokenExpiredError checks if an error is due to token expiration
func IsTokenExpiredError(err error) bool {
	return err != nil && (jwt.ErrTokenExpired.Error() == err.Error() || 
		                 err.Error() == "token has expired")
}

// IsTokenInvalidError checks if an error is due to invalid token
func IsTokenInvalidError(err error) bool {
	return err != nil && (jwt.ErrTokenMalformed.Error() == err.Error() || 
		                 jwt.ErrTokenNotValidYet.Error() == err.Error() || 
		                 jwt.ErrSignatureInvalid.Error() == err.Error())
}