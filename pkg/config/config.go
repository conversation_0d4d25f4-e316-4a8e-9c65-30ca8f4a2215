package config

import (
	"github.com/spf13/viper"
)

type Config struct {
	App        App        `mapstructure:"app"`
	Auth       Auth       `mapstructure:"auth"`
	WebAuthn   WebAuthn   `mapstructure:"webauthn"`
	<PERSON>ra       Jira       `mapstructure:"jira"`
	SDET       SDET       `mapstructure:"sdet"`
	OpenSearch OpenSearch `mapstructure:"opensearch"`
}
type App struct {
	Name string `mapstructure:"name"`
	Port string `mapstructure:"port"`
	Env  string `mapstructure:"env"`
}

type Auth struct {
	JWTSecret                string `mapstructure:"jwt_secret"`
	JWTExpirationHours       int    `mapstructure:"jwt_expiration_hours"`
	JWTRefreshExpirationHours int   `mapstructure:"jwt_refresh_expiration_hours"`
}

type WebAuthn struct {
	RPDisplayName           string   `mapstructure:"rp_display_name"`
	RPID                    string   `mapstructure:"rp_id"`
	RPOrigins               []string `mapstructure:"rp_origins"`
	AuthenticatorAttachment string   `mapstructure:"authenticator_attachment"`
	UserVerification        string   `mapstructure:"user_verification"`
	ResidentKey             string   `mapstructure:"resident_key"`
	TimeoutMs               int      `mapstructure:"timeout_ms"`
	RegistrationTimeoutMs   int      `mapstructure:"registration_timeout_ms"`
	AuthenticationTimeoutMs int      `mapstructure:"authentication_timeout_ms"`
	AttestationPreference   string   `mapstructure:"attestation_preference"`
	TransportTypes          []string `mapstructure:"transport_types"`
	RequireResidentKey      bool     `mapstructure:"require_resident_key"`
	Debug                   bool     `mapstructure:"debug"`
}
type Jira struct {
	SecretKey              string `mapstructure:"secret_key"`
	DefaultJQLGetReporting string `mapstructure:"default_jql_get_reporting"`
	DefaultField           string `mapstructure:"default_field"`
}
type Database struct {
	Host                       string `mapstructure:"host"`
	User                       string `mapstructure:"user"`
	Password                   string `mapstructure:"password"`
	Database                   string `mapstructure:"database"`
	Port                       int    `mapstructure:"port"`
	MaxIdleConnection          int    `mapstructure:"max_idle_connection"`
	MaxOpenConnection          int    `mapstructure:"max_open_connection"`
	ConnectionLifeTimeInSecond int    `mapstructure:"connection_life_time_in_second"`
}
type SDET struct {
	DatabaseMaster Database `mapstructure:"database_master"`
	DatabaseSlave  Database `mapstructure:"database_slave"`
}

type OpenSearch struct {
	Addresses []string `mapstructure:"addresses"`
	Username  string   `mapstructure:"username"`
	Password  string   `mapstructure:"password"`
	Index     string   `mapstructure:"index"`
}

func New(configFile string) (*Config, error) {
	cfg := &Config{}
	viper.SetConfigFile(configFile)
	err := viper.ReadInConfig()
	if err != nil {
		return nil, err
	}
	return cfg, viper.Unmarshal(&cfg)
}
