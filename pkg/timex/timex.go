package timex

import (
	"fmt"
	"time"
)

const (
	JiraDateTimeLayout = "2006-01-02T15:04:05.000+0700"
)

func GetWeekBounds() (startOfWeek time.Time, endOfWeek time.Time) {
	now := time.Now()
	weekday := int(now.Weekday())
	if weekday == 0 {
		weekday = 7 // make Sunday = 7
	}
	// Start of week (Monday at 00:00:00)
	startOfWeek = StartOfDay(now).AddDate(0, 0, -weekday+1)
	additionToSunday := 6
	// End of week (Sunday at 23:59:59)
	endOfWeek = EndOfDay(startOfWeek).AddDate(0, 0, additionToSunday)

	return startOfWeek, endOfWeek
}

func StartOfDay(t time.Time) time.Time {
	return time.Date(
		t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location(),
	)
}
func EndOfDay(t time.Time) time.Time {
	return time.Date(
		t.Year(), t.Month(), t.Day(), 23, 59, 59, 0, t.Location(),
	)
}

func GetStartDateEndDateOrDefault(reqStartDate, reqEndDate string) (startDate, endDate string) {
	startOfWeek, endOfWeek := GetWeekBounds()
	startDate = startOfWeek.Format(time.DateOnly)
	endDate = endOfWeek.Format(time.DateOnly)
	if reqStartDate != "" {
		startDate = reqStartDate
	}
	if reqEndDate != "" {
		endDate = reqEndDate
	}
	return startDate, endDate
}

// GetStartDateAndPlusOne returns the start date and one day after the end date.
// If reqStartDate or reqEndDate are not provided or are invalid, it defaults to the current week's bounds.
// All returned dates are in "YYYY-MM-DD" format.
//
// The second return value (endDatePlusOne) is useful for exclusive end date filtering scenarios,
// such as using "created < endDatePlusOne" instead of "created <= endDate".
func GetStartDateAndPlusOne(reqStartDate, reqEndDate string) (startDate, endDatePlusOne string) {
	startOfWeek, endOfWeek := GetWeekBounds()

	// Default values
	start := startOfWeek
	end := endOfWeek

	// Parse request start date if provided
	if reqStartDate != "" {
		if parsed, err := time.Parse(time.DateOnly, reqStartDate); err == nil {
			start = parsed
		}
	}

	// Parse request end date if provided
	if reqEndDate != "" {
		if parsed, err := time.Parse(time.DateOnly, reqEndDate); err == nil {
			end = parsed
		}
	}

	endPlusOne := end.AddDate(0, 0, 1)

	return start.Format(time.DateOnly), endPlusOne.Format(time.DateOnly)
}

func FormatDurationHuman(seconds int64) string {
	duration := time.Duration(seconds) * time.Second
	hours := int(duration.Hours())
	const minutesInHour = 60
	minutes := int(duration.Minutes()) % minutesInHour

	if hours > 0 {
		return fmt.Sprintf("%dh %dm", hours, minutes)
	}
	return fmt.Sprintf("%dm", minutes)
}
