package webauthn

import (
	"fmt"
	"net/url"
	"time"

	"github.com/go-webauthn/webauthn/protocol"
	"github.com/go-webauthn/webauthn/webauthn"
)

// Config holds the WebAuthn configuration
type Config struct {
	RPDisplayName            string        `mapstructure:"rp_display_name" json:"rp_display_name"`
	RPID                     string        `mapstructure:"rp_id" json:"rp_id"`
	RPOrigins                []string      `mapstructure:"rp_origins" json:"rp_origins"`
	RPTopOrigins             []string      `mapstructure:"rp_top_origins" json:"rp_top_origins"`
	Timeout                  time.Duration `mapstructure:"timeout" json:"timeout"`
	UserVerification         string        `mapstructure:"user_verification" json:"user_verification"`
	AttestationPreference    string        `mapstructure:"attestation_preference" json:"attestation_preference"`
	AuthenticatorAttachment  string        `mapstructure:"authenticator_attachment" json:"authenticator_attachment"`
	ResidentKeyRequirement   string        `mapstructure:"resident_key_requirement" json:"resident_key_requirement"`
	RequireResident<PERSON>ey       bool          `mapstructure:"require_resident_key" json:"require_resident_key"`
	Debug                    bool          `mapstructure:"debug" json:"debug"`
	EncodeUserIDAsString     bool          `mapstructure:"encode_user_id_as_string" json:"encode_user_id_as_string"`
}

// DefaultConfig returns a WebAuthn configuration with sensible defaults
func DefaultConfig() *Config {
	return &Config{
		RPDisplayName:            "Platform Engineering Tools",
		RPID:                     "localhost",
		RPOrigins:                []string{"http://localhost:3000"},
		RPTopOrigins:             []string{},
		Timeout:                  60 * time.Second,
		UserVerification:         "preferred",
		AttestationPreference:    "direct",
		AuthenticatorAttachment:  "",
		ResidentKeyRequirement:   "preferred",
		RequireResidentKey:       false,
		Debug:                    false,
		EncodeUserIDAsString:     false,
	}
}

// Validate validates the WebAuthn configuration
func (c *Config) Validate() error {
	if c.RPDisplayName == "" {
		return fmt.Errorf("rp_display_name is required")
	}
	
	if c.RPID == "" {
		return fmt.Errorf("rp_id is required")
	}
	
	if len(c.RPOrigins) == 0 {
		return fmt.Errorf("at least one rp_origin is required")
	}
	
	// Validate origin URLs format
	for _, origin := range c.RPOrigins {
		if _, err := url.Parse(origin); err != nil {
			return fmt.Errorf("invalid rp_origin URL %s: %w", origin, err)
		}
	}
	
	// Validate top origins if specified
	for _, origin := range c.RPTopOrigins {
		if _, err := url.Parse(origin); err != nil {
			return fmt.Errorf("invalid rp_top_origin URL %s: %w", origin, err)
		}
	}
	
	// Validate user verification
	validUserVerification := []string{"required", "preferred", "discouraged"}
	if !contains(validUserVerification, c.UserVerification) {
		return fmt.Errorf("invalid user_verification: %s (must be one of: %v)", c.UserVerification, validUserVerification)
	}
	
	// Validate attestation preference
	validAttestationPreference := []string{"none", "indirect", "direct", "enterprise"}
	if !contains(validAttestationPreference, c.AttestationPreference) {
		return fmt.Errorf("invalid attestation_preference: %s (must be one of: %v)", c.AttestationPreference, validAttestationPreference)
	}
	
	// Validate authenticator attachment
	if c.AuthenticatorAttachment != "" {
		validAttachment := []string{"platform", "cross-platform"}
		if !contains(validAttachment, c.AuthenticatorAttachment) {
			return fmt.Errorf("invalid authenticator_attachment: %s (must be one of: %v)", c.AuthenticatorAttachment, validAttachment)
		}
	}
	
	// Validate resident key requirement
	validResidentKey := []string{"discouraged", "preferred", "required"}
	if !contains(validResidentKey, c.ResidentKeyRequirement) {
		return fmt.Errorf("invalid resident_key_requirement: %s (must be one of: %v)", c.ResidentKeyRequirement, validResidentKey)
	}
	
	// Validate timeout
	if c.Timeout <= 0 {
		return fmt.Errorf("timeout must be positive")
	}
	
	if c.Timeout > 5*time.Minute {
		return fmt.Errorf("timeout should not exceed 5 minutes")
	}
	
	return nil
}

// ToWebAuthnConfig converts the config to a webauthn.Config
func (c *Config) ToWebAuthnConfig() *webauthn.Config {
	config := &webauthn.Config{
		RPID:          c.RPID,
		RPDisplayName: c.RPDisplayName,
		RPOrigins:     c.RPOrigins,
		RPTopOrigins:  c.RPTopOrigins,
		Debug:         c.Debug,
		EncodeUserIDAsString: c.EncodeUserIDAsString,
	}
	
	// Set attestation preference
	config.AttestationPreference = c.GetAttestationConveyancePreference()
	
	// Set authenticator selection
	config.AuthenticatorSelection = c.GetAuthenticatorSelection()
	
	// Set timeouts
	config.Timeouts.Registration.Timeout = c.Timeout
	config.Timeouts.Login.Timeout = c.Timeout
	
	return config
}

// GetUserVerificationRequirement returns the protocol user verification requirement
func (c *Config) GetUserVerificationRequirement() protocol.UserVerificationRequirement {
	switch c.UserVerification {
	case "required":
		return protocol.VerificationRequired
	case "preferred":
		return protocol.VerificationPreferred
	case "discouraged":
		return protocol.VerificationDiscouraged
	default:
		return protocol.VerificationPreferred
	}
}

// GetAttestationConveyancePreference returns the protocol attestation conveyance preference
func (c *Config) GetAttestationConveyancePreference() protocol.ConveyancePreference {
	switch c.AttestationPreference {
	case "none":
		return protocol.PreferNoAttestation
	case "indirect":
		return protocol.PreferIndirectAttestation
	case "direct":
		return protocol.PreferDirectAttestation
	case "enterprise":
		return protocol.PreferEnterpriseAttestation
	default:
		return protocol.PreferDirectAttestation
	}
}

// GetAuthenticatorAttachment returns the protocol authenticator attachment
func (c *Config) GetAuthenticatorAttachment() *protocol.AuthenticatorAttachment {
	switch c.AuthenticatorAttachment {
	case "platform":
		attachment := protocol.Platform
		return &attachment
	case "cross-platform":
		attachment := protocol.CrossPlatform
		return &attachment
	default:
		return nil // No preference
	}
}

// GetResidentKeyRequirement returns the protocol resident key requirement
func (c *Config) GetResidentKeyRequirement() protocol.ResidentKeyRequirement {
	switch c.ResidentKeyRequirement {
	case "discouraged":
		return protocol.ResidentKeyRequirementDiscouraged
	case "preferred":
		return protocol.ResidentKeyRequirementPreferred
	case "required":
		return protocol.ResidentKeyRequirementRequired
	default:
		return protocol.ResidentKeyRequirementPreferred
	}
}

// GetAuthenticatorSelection returns the complete authenticator selection
func (c *Config) GetAuthenticatorSelection() protocol.AuthenticatorSelection {
	selection := protocol.AuthenticatorSelection{
		UserVerification: c.GetUserVerificationRequirement(),
		ResidentKey:      c.GetResidentKeyRequirement(),
	}
	
	// Set authenticator attachment if specified
	if attachment := c.GetAuthenticatorAttachment(); attachment != nil {
		selection.AuthenticatorAttachment = *attachment
	}
	
	// Set require resident key (deprecated but still used by some)
	selection.RequireResidentKey = &c.RequireResidentKey
	
	return selection
}

// contains checks if a slice contains a string
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// GetDefaultRegistrationOptions returns default registration options
func (c *Config) GetDefaultRegistrationOptions() []webauthn.RegistrationOption {
	return []webauthn.RegistrationOption{
		webauthn.WithConveyancePreference(c.GetAttestationConveyancePreference()),
		webauthn.WithAuthenticatorSelection(c.GetAuthenticatorSelection()),
	}
}

// GetDefaultLoginOptions returns default login options
func (c *Config) GetDefaultLoginOptions() []webauthn.LoginOption {
	return []webauthn.LoginOption{
		webauthn.WithUserVerification(c.GetUserVerificationRequirement()),
	}
}