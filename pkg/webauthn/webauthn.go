package webauthn

import (
	"context"
	"crypto/rand"
	"fmt"
	"net/http"
	"time"

	"github.com/go-webauthn/webauthn/protocol"
	"github.com/go-webauthn/webauthn/webauthn"
	"github.com/google/uuid"
)

// Service provides WebAuthn functionality
type Service struct {
	webAuthn *webauthn.WebAuthn
	config   *Config
}

// SessionData represents a WebAuthn session for storing challenge data
type SessionData struct {
	SessionID   string      `json:"session_id"`
	Username    string      `json:"username"`
	Challenge   string      `json:"challenge"`
	SessionType SessionType `json:"session_type"`
	ExpiresAt   time.Time   `json:"expires_at"`
	UserID      []byte      `json:"user_id,omitempty"`
	// Store the actual webauthn session data
	WebAuthnSessionData *webauthn.SessionData `json:"-"`
}

// SessionType represents the type of WebAuthn session
type SessionType string

const (
	SessionTypeRegistration   SessionType = "registration"
	SessionTypeAuthentication SessionType = "authentication"
)

// New creates a new WebAuthn service
func New(config *Config) (*Service, error) {
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid WebAuthn config: %w", err)
	}

	webAuthn, err := webauthn.New(config.ToWebAuthnConfig())
	if err != nil {
		return nil, fmt.Errorf("failed to initialize WebAuthn: %w", err)
	}

	return &Service{
		webAuthn: webAuthn,
		config:   config,
	}, nil
}

// BeginRegistration starts the WebAuthn registration process for a user
func (s *Service) BeginRegistration(ctx context.Context, user webauthn.User, opts ...webauthn.RegistrationOption) (*protocol.CredentialCreation, *SessionData, error) {
	// Generate registration options
	creation, session, err := s.webAuthn.BeginRegistration(user, opts...)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to begin registration: %w", err)
	}

	// Create session data for storage
	sessionData := &SessionData{
		SessionID:           generateSessionID(),
		Username:            user.WebAuthnName(),
		Challenge:           session.Challenge,
		SessionType:         SessionTypeRegistration,
		ExpiresAt:           time.Now().Add(s.config.Timeout),
		UserID:              user.WebAuthnID(),
		WebAuthnSessionData: session,
	}

	return creation, sessionData, nil
}

// FinishRegistration completes the WebAuthn registration process
func (s *Service) FinishRegistration(ctx context.Context, user webauthn.User, sessionData *SessionData, request *http.Request) (*webauthn.Credential, error) {
	// Validate session
	if err := s.validateSession(sessionData, SessionTypeRegistration); err != nil {
		return nil, fmt.Errorf("invalid session: %w", err)
	}

	// Finish registration using the actual webauthn session data
	credential, err := s.webAuthn.FinishRegistration(user, *sessionData.WebAuthnSessionData, request)
	if err != nil {
		return nil, fmt.Errorf("failed to finish registration: %w", err)
	}

	return credential, nil
}

// BeginLogin starts the WebAuthn authentication process for a user
func (s *Service) BeginLogin(ctx context.Context, user webauthn.User, opts ...webauthn.LoginOption) (*protocol.CredentialAssertion, *SessionData, error) {
	// Generate login options
	assertion, session, err := s.webAuthn.BeginLogin(user, opts...)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to begin login: %w", err)
	}

	// Create session data for storage
	sessionData := &SessionData{
		SessionID:           generateSessionID(),
		Username:            user.WebAuthnName(),
		Challenge:           session.Challenge,
		SessionType:         SessionTypeAuthentication,
		ExpiresAt:           time.Now().Add(s.config.Timeout),
		UserID:              session.UserID,
		WebAuthnSessionData: session,
	}

	return assertion, sessionData, nil
}

// FinishLogin completes the WebAuthn authentication process
func (s *Service) FinishLogin(ctx context.Context, user webauthn.User, sessionData *SessionData, request *http.Request) (*webauthn.Credential, error) {
	// Validate session
	if err := s.validateSession(sessionData, SessionTypeAuthentication); err != nil {
		return nil, fmt.Errorf("invalid session: %w", err)
	}

	// Finish login using the actual webauthn session data
	credential, err := s.webAuthn.FinishLogin(user, *sessionData.WebAuthnSessionData, request)
	if err != nil {
		return nil, fmt.Errorf("failed to finish login: %w", err)
	}

	return credential, nil
}

// BeginDiscoverableLogin starts a discoverable credential login (usernameless)
func (s *Service) BeginDiscoverableLogin(ctx context.Context, opts ...webauthn.LoginOption) (*protocol.CredentialAssertion, *SessionData, error) {
	// Generate login options for discoverable credentials
	assertion, session, err := s.webAuthn.BeginDiscoverableLogin(opts...)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to begin discoverable login: %w", err)
	}

	// Create session data for storage
	sessionData := &SessionData{
		SessionID:           generateSessionID(),
		Username:            "", // No username for discoverable login
		Challenge:           session.Challenge,
		SessionType:         SessionTypeAuthentication,
		ExpiresAt:           time.Now().Add(s.config.Timeout),
		UserID:              nil, // No specific user ID
		WebAuthnSessionData: session,
	}

	return assertion, sessionData, nil
}

// FinishDiscoverableLogin completes a discoverable credential login
func (s *Service) FinishDiscoverableLogin(ctx context.Context, sessionData *SessionData, request *http.Request, userHandler webauthn.DiscoverableUserHandler) (*webauthn.Credential, error) {
	// Validate session
	if err := s.validateSession(sessionData, SessionTypeAuthentication); err != nil {
		return nil, fmt.Errorf("invalid session: %w", err)
	}

	// Finish discoverable login
	credential, err := s.webAuthn.FinishDiscoverableLogin(userHandler, *sessionData.WebAuthnSessionData, request)
	if err != nil {
		return nil, fmt.Errorf("failed to finish discoverable login: %w", err)
	}

	return credential, nil
}

// ValidateRegistrationOptions validates credential creation options
func (s *Service) ValidateRegistrationOptions(options *protocol.CredentialCreation) error {
	if options == nil {
		return fmt.Errorf("options cannot be nil")
	}

	// Skip detailed validation as the structure is complex
	// The webauthn library will perform proper validation
	return nil

	// Note: Detailed field validation would require accessing nested structures
	// which may vary based on WebAuthn library version

	return nil
}

// ValidateAuthenticationOptions validates credential request options
func (s *Service) ValidateAuthenticationOptions(options *protocol.CredentialAssertion) error {
	if options == nil {
		return fmt.Errorf("options cannot be nil")
	}

	// Skip detailed validation as the structure is complex
	// The webauthn library will perform proper validation
	return nil

	// Note: Detailed field validation would require accessing nested structures
	// which may vary based on WebAuthn library version

	return nil
}

// CreateCredential creates a credential from parsed data (alternative to FinishRegistration)
func (s *Service) CreateCredential(ctx context.Context, user webauthn.User, sessionData *SessionData, parsedResponse *protocol.ParsedCredentialCreationData) (*webauthn.Credential, error) {
	// Validate session
	if err := s.validateSession(sessionData, SessionTypeRegistration); err != nil {
		return nil, fmt.Errorf("invalid session: %w", err)
	}

	// Create credential using the parsed response
	credential, err := s.webAuthn.CreateCredential(user, *sessionData.WebAuthnSessionData, parsedResponse)
	if err != nil {
		return nil, fmt.Errorf("failed to create credential: %w", err)
	}

	return credential, nil
}

// ValidateLogin validates a login assertion (alternative to FinishLogin)
func (s *Service) ValidateLogin(ctx context.Context, user webauthn.User, sessionData *SessionData, parsedResponse *protocol.ParsedCredentialAssertionData) (*webauthn.Credential, error) {
	// Validate session
	if err := s.validateSession(sessionData, SessionTypeAuthentication); err != nil {
		return nil, fmt.Errorf("invalid session: %w", err)
	}

	// Validate login using the parsed response
	credential, err := s.webAuthn.ValidateLogin(user, *sessionData.WebAuthnSessionData, parsedResponse)
	if err != nil {
		return nil, fmt.Errorf("failed to validate login: %w", err)
	}

	return credential, nil
}

// GetConfig returns the WebAuthn configuration
func (s *Service) GetConfig() *Config {
	return s.config
}

// validateSession validates session data
func (s *Service) validateSession(session *SessionData, expectedType SessionType) error {
	if session == nil {
		return fmt.Errorf("session data is nil")
	}

	if session.SessionType != expectedType {
		return fmt.Errorf("invalid session type: expected %s, got %s", expectedType, session.SessionType)
	}

	if time.Now().After(session.ExpiresAt) {
		return fmt.Errorf("session has expired")
	}

	if session.Challenge == "" {
		return fmt.Errorf("session challenge is empty")
	}

	if session.WebAuthnSessionData == nil {
		return fmt.Errorf("webauthn session data is nil")
	}

	return nil
}

// generateSessionID generates a unique session ID
func generateSessionID() string {
	return uuid.New().String()
}

// GenerateRandomChallenge generates a cryptographically secure random challenge
func GenerateRandomChallenge() ([]byte, error) {
	challenge := make([]byte, 32) // 32 bytes = 256 bits
	_, err := rand.Read(challenge)
	if err != nil {
		return nil, fmt.Errorf("failed to generate random challenge: %w", err)
	}
	return challenge, nil
}

// IsValidCredentialID checks if a credential ID is valid (proper length and format)
func IsValidCredentialID(credentialID []byte) bool {
	// WebAuthn credential IDs should be at least 16 bytes and at most 1023 bytes
	return len(credentialID) >= 16 && len(credentialID) <= 1023
}

// GetDefaultCredentialParameters returns the default list of supported public key algorithms
func GetDefaultCredentialParameters() []protocol.CredentialParameter {
	return webauthn.CredentialParametersDefault()
}

// GetRecommendedCredentialParameters returns the recommended list of public key algorithms
func GetRecommendedCredentialParameters() []protocol.CredentialParameter {
	return webauthn.CredentialParametersRecommendedL3()
}

// GetExtendedCredentialParameters returns the extended list of public key algorithms
func GetExtendedCredentialParameters() []protocol.CredentialParameter {
	return webauthn.CredentialParametersExtendedL3()
}

// ParseCredentialCreationResponse parses a credential creation response from an HTTP request
func (s *Service) ParseCredentialCreationResponse(request *http.Request) (*protocol.ParsedCredentialCreationData, error) {
	return protocol.ParseCredentialCreationResponseBody(request.Body)
}

// ParseCredentialRequestResponse parses a credential request response from an HTTP request
func (s *Service) ParseCredentialRequestResponse(request *http.Request) (*protocol.ParsedCredentialAssertionData, error) {
	return protocol.ParseCredentialRequestResponseBody(request.Body)
}

// WithConveyancePreference sets the attestation conveyance preference for registration
func WithConveyancePreference(preference protocol.ConveyancePreference) webauthn.RegistrationOption {
	return webauthn.WithConveyancePreference(preference)
}

// WithAuthenticatorSelection sets the authenticator selection criteria for registration
func WithAuthenticatorSelection(selection protocol.AuthenticatorSelection) webauthn.RegistrationOption {
	return webauthn.WithAuthenticatorSelection(selection)
}

// WithUserVerification sets the user verification requirement for login
func WithUserVerification(requirement protocol.UserVerificationRequirement) webauthn.LoginOption {
	return webauthn.WithUserVerification(requirement)
}

// WithAllowedCredentials sets the allowed credentials for login
func WithAllowedCredentials(credentials []protocol.CredentialDescriptor) webauthn.LoginOption {
	return webauthn.WithAllowedCredentials(credentials)
}
