package log

import (
	"github.com/Lionparcel/pentools/pkg/config"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

type KeyValue struct {
	Key   string
	Value any
}

var logger *zap.Logger

func New(config *config.Config) error {
	var zapCfg zap.Config
	if config.App.Env == "local" {
		zapCfg = zap.NewDevelopmentConfig()
		zapCfg.EncoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	} else {
		zapCfg = zap.NewProductionConfig()
		zapCfg.EncoderConfig.MessageKey = "message"
		zapCfg.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	}

	var err error
	logger, err = zapCfg.Build(zap.AddCaller(), zap.AddCallerSkip(1), zap.AddStacktrace(zap.PanicLevel), zap.Fields(
		zap.String("service", config.App.Name), zap.String("environment", config.App.Env),
	))
	return err
}
