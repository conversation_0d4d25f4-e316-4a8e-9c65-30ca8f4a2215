package log

import (
	"context"

	"github.com/Lionparcel/pentools/internal/model"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

func traceContext(ctx context.Context) []zapcore.Field {
	fields := []zapcore.Field{}
	requestID, ok := ctx.Value(model.ContextKeyRequestID).(string)
	if ok && requestID != "" {
		fields = append(fields, zap.String("request_id", requestID))
	}
	span := trace.SpanContextFromContext(ctx)
	if span.HasTraceID() {
		fields = append(fields, zap.String("trace.id", span.TraceID().String()))
	}
	if span.HasSpanID() {
		fields = append(fields, zap.String("span.id", span.SpanID().String()))
	}
	return fields
}

func Info(ctx context.Context, message string, kv ...KeyValue) {
	f := traceContext(ctx)
	for _, v := range kv {
		f = append(f, zap.Any(v.Key, v.Value))
	}
	logger.Info(message, f...)
}
func Debug(ctx context.Context, message string, kv ...KeyValue) {
	f := traceContext(ctx)
	for _, v := range kv {
		f = append(f, zap.Any(v.Key, v.Value))
	}
	logger.Debug(message, f...)
}
func Error(ctx context.Context, message string, err error, kv ...KeyValue) {
	f := traceContext(ctx)
	f = append(f, zap.Error(err))
	for _, v := range kv {
		f = append(f, zap.Any(v.Key, v.Value))
	}
	logger.Error(message, f...)
}
func Warn(ctx context.Context, message string, kv ...KeyValue) {
	f := traceContext(ctx)
	for _, v := range kv {
		f = append(f, zap.Any(v.Key, v.Value))
	}
	logger.Warn(message, f...)
}
func Fatal(ctx context.Context, message string, err error, kv ...KeyValue) {
	f := traceContext(ctx)
	f = append(f, zap.Error(err))
	for _, v := range kv {
		f = append(f, zap.Any(v.Key, v.Value))
	}
	logger.Fatal(message, f...)
}
func Panic(ctx context.Context, message string, kv ...KeyValue) {
	f := traceContext(ctx)
	for _, v := range kv {
		f = append(f, zap.Any(v.Key, v.Value))
	}
	logger.WithOptions(zap.AddStacktrace(zap.ErrorLevel)).Error("[PANIC RECOVERED] "+message, f...)
}
