.PHONY: run generate

generate:
	go generate ./...

run: generate
	go run ./cmd/http/main.go

fe-install:
	cd frontend && pnpm install

fe-build:
	cd frontend && pnpm build && rm -rf ../cmd/http/dist && cp -r out ../cmd/http/dist

fe-run:
	cd frontend && pnpm run dev

fe-lint:
	cd frontend && pnpm run lint

run-all: fe-build run

# Race condition checker
test-race:
	GOMAXPROCS=4 go test -v -race -run=TestEngSuite ./internal/usecase/eng