package dto

// Response DTOs used by delivery layer

// ReleaseDoc represents a release document returned by API.
type ReleaseDoc struct {
	ID             string           `json:"id"` // OpenSearch document ID
	Title          string           `json:"title"`
	ReleaseDate    string           `json:"release_date"`
	ReleaseType    string           `json:"release_type"`
	Squads         []ReleaseSquad   `json:"squads"`
	ReleaseSummary []ReleaseSummary `json:"release_summary"`
	Repo           []ReleaseRepo    `json:"repo"`
	Todos          []ReleaseTodo    `json:"todos"`
}

// ReleaseSquad describes a squad and its release version.
type ReleaseSquad struct {
	Name           string `json:"name"`
	ReleaseVersion string `json:"release_version"`
}

// ReleaseSummary holds a single summary item.
type ReleaseSummary struct {
	Type     string `json:"type"`
	TaskLink string `json:"task_link"`
	Title    string `json:"title"`
}

// ReleaseRepo contains repository info for a release.
type ReleaseRepo struct {
	RepoName         string `json:"repo_name"`
	ReleaseVersion   string `json:"release_version"`
	PR               string `json:"pr"`
	ConfigManagement string `json:"config_management"`
}

// ReleaseTodo captures TODO items for the release.
type ReleaseTodo struct {
	Task      string `json:"task"`
	TaskURL   string `json:"task_url"`
	Priority  int    `json:"priority"`
	Owner     string `json:"owner"`
	Status    string `json:"status"`
	StartDate string `json:"start_date"`
	EndDate   string `json:"end_date"`
	Notes     string `json:"notes"`
}

// CreateReleaseRequest is the payload to create a release.
type CreateReleaseRequest struct {
	Title          string           `json:"title"`
	ReleaseDate    string           `json:"release_date"`
	ReleaseType    string           `json:"release_type"`
	Squads         []ReleaseSquad   `json:"squads"`
	ReleaseSummary []ReleaseSummary `json:"release_summary"`
	Repo           []ReleaseRepo    `json:"repo"`
	Todos          []ReleaseTodo    `json:"todos"`
}

// UpdateReleaseRequest is the payload to update a release.
type UpdateReleaseRequest struct {
	ID             string            `param:"id"`
	Title          *string           `json:"title"`
	ReleaseDate    *string           `json:"release_date"`
	ReleaseType    *string           `json:"release_type"`
	Squads         *[]ReleaseSquad   `json:"squads"`
	ReleaseSummary *[]ReleaseSummary `json:"release_summary"`
	Repo           *[]ReleaseRepo    `json:"repo"`
	Todos          *[]ReleaseTodo    `json:"todos"`
}

// GetReleaseRequest identifies a document by id.
type GetReleaseRequest struct {
	ID string `param:"id"`
}

// ListReleasesRequest holds search parameters.
type ListReleasesRequest struct {
	Query  string `query:"q"`
	Limit  int    `query:"limit"`
	Offset int    `query:"offset"`
}

// DeleteReleaseRequest identifies a document for deletion.
type DeleteReleaseRequest struct {
	ID string `param:"id"`
}

// CreateReleaseResponse contains created id.
type CreateReleaseResponse struct {
	ID string `json:"id"`
}

// DeleteReleaseResponse indicates deletion result.
type DeleteReleaseResponse struct {
	Deleted bool `json:"deleted"`
}
