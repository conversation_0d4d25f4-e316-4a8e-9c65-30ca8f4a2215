package dto

import (
	"slices"
	"sort"
	"strings"
	"time"

	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/timex"
)

type Worklog struct {
	Name            string  `json:"name"`
	UserID          string  `json:"user_id"`
	Type            string  `json:"type"`
	Description     string  `json:"description"`
	TimeSpentHuman  string  `json:"time_spent_human"`
	TimeSpentHour   float64 `json:"time_spent_hour"`
	TimeSpentSecond int64   `json:"time_spent_second"`
	CreatedAt       string  `json:"created_at"`
}
type changelogStatus struct {
	StartAt   []time.Time
	EndAt     time.Time
	TimeSpent time.Duration
}

func NewJiraWorklogs(jiraWorklogs []model.WorklogElement, userID string, endDate time.Time) []Worklog {
	worklogs := []Worklog{}
	for _, wl := range jiraWorklogs {
		if (wl.Author.AccountID != userID && userID != "") || wl.StartedTime().After(endDate) {
			continue
		}
		comment := wl.FormattedComment()
		i := strings.Index(comment, "]")
		typeStr := strings.TrimPrefix(strings.TrimSuffix(comment[:i+1], "]"), "[")
		description := comment[i+1:]
		worklogs = append(worklogs, Worklog{
			Name:            wl.Author.DisplayName,
			UserID:          wl.Author.AccountID,
			Type:            typeStr,
			Description:     description,
			TimeSpentHuman:  timex.FormatDurationHuman(wl.TimeSpentSeconds),
			TimeSpentHour:   (time.Duration(wl.TimeSpentSeconds) * time.Second).Hours(),
			TimeSpentSecond: wl.TimeSpentSeconds,
			CreatedAt:       wl.Started,
		})
	}
	return worklogs
}

func NewJiraWorklogsFromChangelog(changelog *model.Changelog) []Worklog {
	if changelog == nil {
		return nil
	}
	histories := changelog.Histories
	slices.Reverse(histories)
	worklogs := []Worklog{}
	mapStatus := map[string]changelogStatus{}
	for _, history := range histories {
		fromStatus := ""
		toStatus := ""
		for _, item := range history.Items {
			if item.FieldID != "status" {
				continue
			}
			if item.FromString != nil && *item.FromString != "" {
				fromStatus = *item.FromString
			}
			if item.ToString != nil && *item.ToString != "" {
				toStatus = *item.ToString
			}
		}
		createdAt, _ := time.Parse(timex.JiraDateTimeLayout, history.Created)
		mapStatus = assignMapStatus(mapStatus, fromStatus, createdAt, true)
		mapStatus = assignMapStatus(mapStatus, toStatus, createdAt, false)
	}
	for status, s := range mapStatus {
		worklogs = append(worklogs, Worklog{
			Type:            status,
			CreatedAt:       s.StartAt[0].Format(timex.JiraDateTimeLayout),
			TimeSpentHuman:  timex.FormatDurationHuman(int64(s.TimeSpent.Seconds())),
			TimeSpentHour:   s.TimeSpent.Hours(),
			TimeSpentSecond: int64(s.TimeSpent.Seconds()),
		})
	}
	sort.Slice(worklogs, func(i, j int) bool {
		return worklogs[i].CreatedAt < worklogs[j].CreatedAt
	})
	return worklogs
}
func assignMapStatus(mapStatus map[string]changelogStatus, status string, createdAt time.Time, isFrom bool) map[string]changelogStatus {
	if status == "" {
		return mapStatus
	}
	s, ok := mapStatus[status]
	if !ok {
		mapStatus[status] = changelogStatus{
			StartAt:   []time.Time{createdAt},
			EndAt:     createdAt,
			TimeSpent: 0,
		}
		return mapStatus
	}
	s.EndAt = createdAt
	if isFrom {
		s.TimeSpent += createdAt.Sub(s.StartAt[len(s.StartAt)-1])
	} else {
		s.StartAt = append(s.StartAt, createdAt)
	}
	mapStatus[status] = s
	return mapStatus
}
