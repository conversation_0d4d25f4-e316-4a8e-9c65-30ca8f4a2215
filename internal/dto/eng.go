package dto

import "time"

type GetEngRequest struct {
	StartDate string `query:"start_date" example:"2023-01-01"`
	EndDate   string `query:"end_date" example:"2023-02-01"`
}

type GetEngEpicRequest struct {
	GetEngRequest
	IsActiveOnly bool `query:"is_active_only" example:"true"`
}

type GetEngSummaryResponse struct {
	ProductionBug ProductionBug `json:"production_bug"`
	Epic          EpicSummary   `json:"epic"`
	Member        Member        `json:"member"`
}

type GetEngEpicResponse struct {
	TotalTicket     int     `json:"total_ticket"`
	TotalSP         float64 `json:"total_sp"`
	TotalSPEngineer float64 `json:"total_sp_engineer"`
	TotalSPQA       float64 `json:"total_sp_qa"`
	OntimePercent   float64 `json:"ontime_percent"`
	AvgRetestHour   float64 `json:"avg_retest_hour"`
	Epics           []Epic  `json:"epics"`
}

type GetEngLeaderboardResponse struct {
	Engineers []EngLeaderBoard    `json:"engineers"`
	QAs       []MemberLeaderBoard `json:"qas"`
}

type MemberLeaderBoard struct {
	Name       string            `json:"name"`
	SP         float64           `json:"sp"`
	Hours      float64           `json:"hours"`
	HoursPerSP float64           `json:"hours_per_sp"`
	Tasks      []TaskLeaderBoard `json:"tasks"`
}

type TaskLeaderBoard struct {
	Summary           string     `json:"summary"`
	SP                float64    `json:"sp"`
	Hours             float64    `json:"hours"`
	HoursPerSP        float64    `json:"hours_per_sp"`
	URL               string     `json:"url"`
	ExpectedStartDate time.Time  `json:"expected_start_date"`
	ActualStartDate   *time.Time `json:"actual_start_date"`
	ExpectedEndDate   time.Time  `json:"expected_end_date"`
	ActualEndDate     *time.Time `json:"actual_end_date"`
	Status            string     `json:"status"`
	PEName            string     `json:"pe_name"`
}

type EngLeaderBoard struct {
	MemberLeaderBoard
	Bug int `json:"bug"`
}

type Epic struct {
	Name                string    `json:"name"`
	Reporter            string    `json:"reporter"`
	GroomingDate        time.Time `json:"grooming_date"`
	ExpectedReleaseDate time.Time `json:"expected_release_date"`
	TotalTicket         int       `json:"total_ticket"`
	AddFromProduct      int       `json:"add_from_product"`
	AddFromSA           int       `json:"add_from_sa"`
	TotalSP             float64   `json:"total_sp"`
	TotalHours          float64   `json:"total_hours"`
	HoursPerSP          float64   `json:"hours_per_sp"`
	TotalBug            int       `json:"total_bug"`
	RetestHours         float64   `json:"retest_hours"`
	TestingHours        float64   `json:"-"`
	URL                 string    `json:"url"`
	SPEngineer          float64   `json:"-"`
	SPQA                float64   `json:"-"`
	OntimeTask          int       `json:"-"`
	Status              string    `json:"status"`
	FixVersion          string    `json:"fix_version"`
	Tasks               []Task    `json:"tasks"`
}

type Task struct {
	Key               string     `json:"key"`
	Summary           string     `json:"summary"`
	Status            string     `json:"status"`
	Assignee          string     `json:"assignee"`
	SP                float64    `json:"sp"`
	ExpectedStartDate time.Time  `json:"expected_start_date"`
	ActualStartDate   *time.Time `json:"actual_start_date"`
	ExpectedEndDate   time.Time  `json:"expected_end_date"`
	ActualEndDate     *time.Time `json:"actual_end_date"`
	Ontime            string     `json:"ontime"`
	Hours             float64    `json:"hours"`
	HoursPerSP        float64    `json:"hour_per_sp"`
	Additional        string     `json:"additional"`
	Bug               int        `json:"bug"`
	URL               string     `json:"url"`
}

type Member struct {
	Engineer MemberMetrics `json:"engineer"`
	QA       MemberMetrics `json:"qa"`
}

type MemberMetrics struct {
	Total     int     `json:"total"`
	Idle      int     `json:"idle"`
	HourPerSP float64 `json:"hour_per_sp"`
}

type EpicSummary struct {
	Total          int `json:"total"`
	InDevelopment  int `json:"in_development"`
	InTesting      int `json:"in_testing"`
	ReadyToRelease int `json:"ready_to_release"`
}

type ProductionBug struct {
	Total        int `json:"total"`
	FromProduct  int `json:"from_product"`
	FromSA       int `json:"from_sa"`
	FromEngineer int `json:"from_engineer"`
	Others       int `json:"others"`
	NotDefined   int `json:"not_defined"`
}

type GetEngBugResponse struct {
	ProdBug ProdBugResponse `json:"prod_bug"`
	DevBug  DevBugResponse  `json:"dev_bug"`
}

type BugResponse struct {
	Total        int            `json:"total"`
	Severity     map[string]int `json:"severity"`
	Squad        map[string]int `json:"squad"`
	AccidentType map[string]int `json:"accident_type"`
}

type ProdBugResponse struct {
	BugResponse
	Function map[string]int  `json:"function"`
	Bugs     []ProdBugDetail `json:"bugs"`
}

type DevBugResponse struct {
	BugResponse
	Engineer map[string]int `json:"engineer"`
	Bugs     []DevBugDetail `json:"bugs"`
}

type BugDetail struct {
	Summary        string    `json:"summary"`
	Severity       string    `json:"severity"`
	AccidentBug    string    `json:"accident_bug"`
	ResolutionTime float64   `json:"resolution_time"`
	Squad          string    `json:"squad"`
	CreatedDate    time.Time `json:"created_date"`
	Url            string    `json:"url"`
}

type DevBugDetail struct {
	BugDetail
	Assignee string `json:"assignee"`
}

type ProdBugDetail struct {
	BugDetail
	Function    string  `json:"function"`
	FixingTime  float64 `json:"fixing_time"`
	TestingTime float64 `json:"testing_time"`
}

type GetEngAnalyticsResponse struct {
	Summary EngSummaryAnalytics `json:"summary"`
	Data    []EngAnalytics      `json:"data"`
}

type EngSummaryAnalytics struct {
	TotalEpic     int            `json:"total_epic"`
	TotalSP       float64        `json:"total_sp"`
	TotalManpower int            `json:"total_manpower"`
	SPPerManpower float64        `json:"sp_per_manpower"`
	TotalHours    float64        `json:"total_hours"`
	HoursPerSP    float64        `json:"hours_per_sp"`
	TotalSPEng    float64        `json:"total_sp_eng"`
	TotalEng      int            `json:"total_eng"`
	SPPerEng      float64        `json:"sp_per_eng"`
	TotalEngHours float64        `json:"total_eng_hours"`
	EngHoursPerSP float64        `json:"eng_hours_per_sp"`
	TotalSPQA     float64        `json:"total_sp_qa"`
	TotalQA       int            `json:"total_qa"`
	SPPerQA       float64        `json:"sp_per_qa"`
	TotalQAHours  float64        `json:"total_qa_hours"`
	QAHoursPerSP  float64        `json:"qa_hours_per_sp"`
	Bugs          map[string]int `json:"bugs"`
}

type EngMemberMetrics struct {
	TotalSP   float64 `json:"total_sp"`
	TotalHour float64 `json:"total_hour"`
	HourPerSp float64 `json:"hour_per_sp"`
}

type EngAnalyticsMetrics struct {
	TotalManpower int     `json:"total_manpower"`
	SPPerManpower float64 `json:"sp_per_manpower"`
	EngMemberMetrics
}

type EngAnalyticsPerRole struct {
	RoleName string `json:"role_name"`
	EngAnalyticsMetrics
	PerMembers []EngAnalyticsPerMember `json:"per_members"`
}

type EngAnalyticsPerMember struct {
	MemberName string `json:"member_name"`
	EngMemberMetrics
}

type EngAnalytics struct {
	ReleaseDate string `json:"release_date"`
	TotalEpic   int    `json:"total_epic"`
	EngAnalyticsMetrics
	PerRoles []EngAnalyticsPerRole `json:"per_role"`
}
