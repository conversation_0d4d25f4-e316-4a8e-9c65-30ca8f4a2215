package dto

import "github.com/Lionparcel/pentools/internal/model"

type GetSDETReportResponse struct {
	Params                        GetSDETReportRequest                  `json:"params"`
	RegressionReport              []model.RegressionReport              `json:"regression_report"`
	RegressionExecutionTimeReport []model.RegressionExecutionTimeReport `json:"regression_execution_time_report"`
	AdditionTestCaseReport        []model.TestCaseReport                `json:"addition_test_case_report"`
	AdjustmentTestCaseReport      []model.TestCaseReport                `json:"adjustment_test_case_report"`
}
type GetSDETReportRequest struct {
	StartDate string `json:"start_date"`
	EndDate   string `json:"end_date"`
}
