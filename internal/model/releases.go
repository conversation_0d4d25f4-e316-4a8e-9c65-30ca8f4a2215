package model

// ReleaseDocument mirrors the OpenSearch document structure exactly.
type ReleaseDocument struct {
	Title          string           `json:"title"`
	ReleaseDate    string           `json:"release_date"`
	ReleaseType    string           `json:"release_type"`
	Squads         []ReleaseSquad   `json:"squads"`
	ReleaseSummary []ReleaseSummary `json:"release_summary"`
	Repo           []ReleaseRepo    `json:"repo"`
	Todos          []ReleaseTodo    `json:"todos"`
}

type ReleaseSquad struct {
	Name           string `json:"name"`
	ReleaseVersion string `json:"release_version"`
}

type ReleaseSummary struct {
	Type     string `json:"type"`
	TaskLink string `json:"task_link"`
	Title    string `json:"title"`
}

type ReleaseRepo struct {
	RepoName         string `json:"repo_name"`
	ReleaseVersion   string `json:"release_version"`
	PR               string `json:"pr"`
	ConfigManagement string `json:"config_management"`
}

type ReleaseTodo struct {
	Task      string `json:"task"`
	TaskURL   string `json:"task_url"`
	Priority  int    `json:"priority"`
	Owner     string `json:"owner"`
	Status    string `json:"status"`
	StartDate string `json:"start_date"`
	EndDate   string `json:"end_date"`
	Notes     string `json:"notes"`
}
