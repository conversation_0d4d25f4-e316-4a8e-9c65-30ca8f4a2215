package model

import (
	"encoding/json"
	"fmt"
	"strings"
)

// Document represents the root of an Atlassian Document Format (ADF) document
type Document struct {
	Version int    `json:"version"`
	Type    string `json:"type"`
	Content []Node `json:"content"`
}

// Node represents a generic node in the ADF structure
type Node struct {
	Type    string                 `json:"type"`
	Content []Node                 `json:"content,omitempty"`
	Text    string                 `json:"text,omitempty"`
	Marks   []Mark                 `json:"marks,omitempty"`
	Attrs   map[string]interface{} `json:"attrs,omitempty"`
}

// Mark represents text formatting or embellishment
type Mark struct {
	Type  string                 `json:"type"`
	Attrs map[string]interface{} `json:"attrs,omitempty"`
}

// BlockNodeType defines the available block node types
type BlockNodeType string

const (
	// Root node
	DocNode BlockNodeType = "doc"

	// Top-level block nodes
	BlockquoteNode     BlockNodeType = "blockquote"
	BulletListNode     BlockNodeType = "bulletList"
	CodeBlockNode      BlockNodeType = "codeBlock"
	ExpandNode         BlockNodeType = "expand"
	HeadingNode        BlockNodeType = "heading"
	MediaGroupNode     BlockNodeType = "mediaGroup"
	MediaSingleNode    BlockNodeType = "mediaSingle"
	OrderedListNode    BlockNodeType = "orderedList"
	PanelNode          BlockNodeType = "panel"
	ParagraphNode      BlockNodeType = "paragraph"
	RuleNode           BlockNodeType = "rule"
	TableNode          BlockNodeType = "table"
	MultiBodiedExtNode BlockNodeType = "multiBodiedExtension"

	// Child block nodes
	ListItemNode       BlockNodeType = "listItem"
	MediaNode          BlockNodeType = "media"
	NestedExpandNode   BlockNodeType = "nestedExpand"
	TableCellNode      BlockNodeType = "tableCell"
	TableHeaderNode    BlockNodeType = "tableHeader"
	TableRowNode       BlockNodeType = "tableRow"
	ExtensionFrameNode BlockNodeType = "extensionFrame"
)

// InlineNodeType defines the available inline node types
type InlineNodeType string

const (
	DateNode        InlineNodeType = "date"
	EmojiNode       InlineNodeType = "emoji"
	HardBreakNode   InlineNodeType = "hardBreak"
	InlineCardNode  InlineNodeType = "inlineCard"
	MentionNode     InlineNodeType = "mention"
	StatusNode      InlineNodeType = "status"
	TextNode        InlineNodeType = "text"
	MediaInlineNode InlineNodeType = "mediaInline"
)

// MarkType defines the available mark types
type MarkType string

const (
	BorderMark    MarkType = "border"
	CodeMark      MarkType = "code"
	EmMark        MarkType = "em"
	LinkMark      MarkType = "link"
	StrikeMark    MarkType = "strike"
	StrongMark    MarkType = "strong"
	SubSupMark    MarkType = "subsup"
	TextColorMark MarkType = "textColor"
	UnderlineMark MarkType = "underline"
	AlignmentMark MarkType = "alignment"
)

// JiraDocumentParser provides methods to parse and manipulate ADF documents
type JiraDocumentParser struct{}

// NewJiraDocumentParser creates a new instance of JiraDocumentParser
func NewJiraDocumentParser() *JiraDocumentParser {
	return &JiraDocumentParser{}
}

// ParseDocument parses a JSON string into a Document structure
func (p *JiraDocumentParser) ParseDocument(jsonData []byte) (*Document, error) {
	var doc Document
	if err := json.Unmarshal(jsonData, &doc); err != nil {
		return nil, fmt.Errorf("failed to parse ADF document: %w", err)
	}

	// Validate document structure
	if doc.Type != string(DocNode) {
		return nil, fmt.Errorf("invalid document type: expected 'doc', got '%s'", doc.Type)
	}

	if doc.Version == 0 {
		return nil, fmt.Errorf("document version is required")
	}

	return &doc, nil
}

// ToJSON converts a Document structure back to JSON
func (p *JiraDocumentParser) ToJSON(doc *Document) ([]byte, error) {
	return json.MarshalIndent(doc, "", "  ")
}

// ExtractPlainText extracts plain text content from the document
func (p *JiraDocumentParser) ExtractPlainText(doc *Document) string {
	var textBuilder strings.Builder
	p.extractTextFromNodes(doc.Content, &textBuilder)
	return strings.TrimSpace(textBuilder.String())
}

// FormatToMarkdown converts the ADF document to Markdown format
func (p *JiraDocumentParser) FormatToMarkdown(doc *Document) string {
	var mdBuilder strings.Builder
	p.formatNodesToMarkdown(doc.Content, &mdBuilder, 0, false)
	return strings.TrimSpace(mdBuilder.String())
}

// formatNodesToMarkdown recursively converts nodes to Markdown
func (p *JiraDocumentParser) formatNodesToMarkdown(nodes []Node, builder *strings.Builder, listLevel int, inOrderedList bool) {
	for i, node := range nodes {
		switch node.Type {
		case string(TextNode):
			p.formatTextNodeToMarkdown(node, builder)
		case string(HardBreakNode):
			builder.WriteString("  \n") // Markdown line break
		case string(ParagraphNode):
			p.formatNodesToMarkdown(node.Content, builder, listLevel, inOrderedList)
			if i < len(nodes)-1 {
				builder.WriteString("\n\n")
			}
		case string(HeadingNode):
			level := p.getHeadingLevel(node)
			builder.WriteString(strings.Repeat("#", level) + " ")
			p.formatNodesToMarkdown(node.Content, builder, listLevel, inOrderedList)
			builder.WriteString("\n\n")
		case string(BulletListNode):
			p.formatNodesToMarkdown(node.Content, builder, listLevel, false)
			if i < len(nodes)-1 {
				builder.WriteString("\n")
			}
		case string(OrderedListNode):
			p.formatNodesToMarkdown(node.Content, builder, listLevel, true)
			if i < len(nodes)-1 {
				builder.WriteString("\n")
			}
		case string(ListItemNode):
			indent := strings.Repeat("  ", listLevel)
			if inOrderedList {
				builder.WriteString(fmt.Sprintf("%s1. ", indent))
			} else {
				builder.WriteString(fmt.Sprintf("%s- ", indent))
			}
			p.formatNodesToMarkdown(node.Content, builder, listLevel+1, inOrderedList)
			builder.WriteString("\n")
		case string(CodeBlockNode):
			language := p.getCodeBlockLanguage(node)
			builder.WriteString("```" + language + "\n")
			p.formatNodesToMarkdown(node.Content, builder, listLevel, inOrderedList)
			builder.WriteString("\n```\n\n")
		case string(BlockquoteNode):
			// Format blockquote content with > prefix
			var quoteBuilder strings.Builder
			p.formatNodesToMarkdown(node.Content, &quoteBuilder, listLevel, inOrderedList)
			quoteText := strings.TrimSpace(quoteBuilder.String())
			lines := strings.Split(quoteText, "\n")
			for _, line := range lines {
				builder.WriteString("> " + line + "\n")
			}
			builder.WriteString("\n")
		case string(RuleNode):
			builder.WriteString("---\n\n")
		case string(TableNode):
			p.formatTableToMarkdown(node, builder)
		case string(TableRowNode):
			builder.WriteString("|")
			p.formatNodesToMarkdown(node.Content, builder, listLevel, inOrderedList)
			builder.WriteString("\n")
		case string(TableCellNode), string(TableHeaderNode):
			builder.WriteString(" ")
			p.formatNodesToMarkdown(node.Content, builder, listLevel, inOrderedList)
			builder.WriteString(" |")
		case string(PanelNode):
			// Format panel as a blockquote with info
			panelType := p.getPanelType(node)
			if panelType != "" {
				builder.WriteString(fmt.Sprintf("> **%s**\n>\n", strings.Title(panelType)))
			}
			var panelBuilder strings.Builder
			p.formatNodesToMarkdown(node.Content, &panelBuilder, listLevel, inOrderedList)
			panelText := strings.TrimSpace(panelBuilder.String())
			lines := strings.Split(panelText, "\n")
			for _, line := range lines {
				builder.WriteString("> " + line + "\n")
			}
			builder.WriteString("\n")
		case string(MentionNode):
			mention := p.getMentionText(node)
			builder.WriteString(mention)
		case string(EmojiNode):
			emoji := p.getEmojiText(node)
			builder.WriteString(emoji)
		case string(DateNode):
			date := p.getDateText(node)
			builder.WriteString(date)
		case string(StatusNode):
			status := p.getStatusText(node)
			builder.WriteString(status)
		case string(InlineCardNode):
			url := p.getInlineCardURL(node)
			title := p.getInlineCardTitle(node)
			if title != "" {
				builder.WriteString(fmt.Sprintf("[%s](%s)", title, url))
			} else {
				builder.WriteString(url)
			}
		default:
			// For other node types, continue formatting from content
			if len(node.Content) > 0 {
				p.formatNodesToMarkdown(node.Content, builder, listLevel, inOrderedList)
			}
		}
	}
}

// formatTextNodeToMarkdown formats a text node with its marks to Markdown
func (p *JiraDocumentParser) formatTextNodeToMarkdown(node Node, builder *strings.Builder) {
	text := node.Text

	// Apply marks to text
	var prefix, suffix string
	var linkURL string

	for _, mark := range node.Marks {
		switch mark.Type {
		case string(StrongMark):
			prefix = "**" + prefix
			suffix = suffix + "**"
		case string(EmMark):
			prefix = "*" + prefix
			suffix = suffix + "*"
		case string(CodeMark):
			prefix = "`" + prefix
			suffix = suffix + "`"
		case string(StrikeMark):
			prefix = "~~" + prefix
			suffix = suffix + "~~"
		case string(UnderlineMark):
			// Markdown doesn't have native underline, use HTML
			prefix = "<u>" + prefix
			suffix = suffix + "</u>"
		case string(LinkMark):
			if mark.Attrs != nil {
				if href, ok := mark.Attrs["href"].(string); ok {
					linkURL = href
				}
			}
		case string(TextColorMark):
			// Markdown doesn't support colors natively, use HTML
			if mark.Attrs != nil {
				if color, ok := mark.Attrs["color"].(string); ok {
					prefix = fmt.Sprintf(`<span style="color: %s">`, color) + prefix
					suffix = suffix + "</span>"
				}
			}
		}
	}

	// Handle links
	if linkURL != "" {
		builder.WriteString(fmt.Sprintf("[%s%s%s](%s)", prefix, text, suffix, linkURL))
	} else {
		builder.WriteString(prefix + text + suffix)
	}
}

// Helper functions for extracting node attributes
func (p *JiraDocumentParser) getHeadingLevel(node Node) int {
	if node.Attrs != nil {
		if level, ok := node.Attrs["level"].(float64); ok {
			return int(level)
		}
	}
	return 1 // Default to h1
}

func (p *JiraDocumentParser) getCodeBlockLanguage(node Node) string {
	if node.Attrs != nil {
		if lang, ok := node.Attrs["language"].(string); ok {
			return lang
		}
	}
	return "" // No language specified
}

func (p *JiraDocumentParser) getPanelType(node Node) string {
	if node.Attrs != nil {
		if panelType, ok := node.Attrs["panelType"].(string); ok {
			return panelType
		}
	}
	return ""
}

func (p *JiraDocumentParser) getMentionText(node Node) string {
	if node.Attrs != nil {
		if text, ok := node.Attrs["text"].(string); ok {
			return text
		}
		if id, ok := node.Attrs["id"].(string); ok {
			return "@" + id
		}
	}
	return "@unknown"
}

func (p *JiraDocumentParser) getEmojiText(node Node) string {
	if node.Attrs != nil {
		if shortName, ok := node.Attrs["shortName"].(string); ok {
			return ":" + shortName + ":"
		}
		if text, ok := node.Attrs["text"].(string); ok {
			return text
		}
	}
	return ":emoji:"
}

func (p *JiraDocumentParser) getDateText(node Node) string {
	if node.Attrs != nil {
		if timestamp, ok := node.Attrs["timestamp"].(string); ok {
			return timestamp
		}
	}
	return ""
}

func (p *JiraDocumentParser) getStatusText(node Node) string {
	if node.Attrs != nil {
		if text, ok := node.Attrs["text"].(string); ok {
			return text
		}
	}
	return ""
}

func (p *JiraDocumentParser) getInlineCardURL(node Node) string {
	if node.Attrs != nil {
		if url, ok := node.Attrs["url"].(string); ok {
			return url
		}
	}
	return ""
}

func (p *JiraDocumentParser) getInlineCardTitle(node Node) string {
	if node.Attrs != nil {
		if title, ok := node.Attrs["title"].(string); ok {
			return title
		}
	}
	return ""
}

func (p *JiraDocumentParser) formatTableToMarkdown(tableNode Node, builder *strings.Builder) {
	var rows []Node
	var hasHeader bool

	// Extract table rows
	for _, child := range tableNode.Content {
		if child.Type == string(TableRowNode) {
			rows = append(rows, child)
		}
	}

	if len(rows) == 0 {
		return
	}

	// Check if first row contains headers
	if len(rows) > 0 {
		firstRow := rows[0]
		for _, cell := range firstRow.Content {
			if cell.Type == string(TableHeaderNode) {
				hasHeader = true
				break
			}
		}
	}

	// Format table rows
	for i, row := range rows {
		builder.WriteString("|")
		for _, cell := range row.Content {
			builder.WriteString(" ")
			p.formatNodesToMarkdown(cell.Content, builder, 0, false)
			builder.WriteString(" |")
		}
		builder.WriteString("\n")

		// Add header separator after first row if it contains headers
		if i == 0 && hasHeader && len(row.Content) > 0 {
			builder.WriteString("|")
			for range row.Content {
				builder.WriteString("---|")
			}
			builder.WriteString("\n")
		}
	}
	builder.WriteString("\n")
}

// extractTextFromNodes recursively extracts text from nodes
func (p *JiraDocumentParser) extractTextFromNodes(nodes []Node, builder *strings.Builder) {
	for _, node := range nodes {
		switch node.Type {
		case string(TextNode):
			builder.WriteString(node.Text)
		case string(HardBreakNode):
			builder.WriteString("\n")
		case string(ParagraphNode):
			p.extractTextFromNodes(node.Content, builder)
			builder.WriteString("\n")
		case string(HeadingNode):
			p.extractTextFromNodes(node.Content, builder)
			builder.WriteString("\n")
		case string(ListItemNode):
			builder.WriteString("• ")
			p.extractTextFromNodes(node.Content, builder)
		case string(BulletListNode), string(OrderedListNode):
			p.extractTextFromNodes(node.Content, builder)
		case string(CodeBlockNode):
			p.extractTextFromNodes(node.Content, builder)
			builder.WriteString("\n")
		case string(BlockquoteNode):
			builder.WriteString("> ")
			p.extractTextFromNodes(node.Content, builder)
			builder.WriteString("\n")
		default:
			// For other node types, continue extracting from content
			if len(node.Content) > 0 {
				p.extractTextFromNodes(node.Content, builder)
			}
		}
	}
}

// GetNodesByType returns all nodes of a specific type from the document
func (p *JiraDocumentParser) GetNodesByType(doc *Document, nodeType string) []Node {
	var result []Node
	p.findNodesByType(doc.Content, nodeType, &result)
	return result
}

// findNodesByType recursively finds nodes of a specific type
func (p *JiraDocumentParser) findNodesByType(nodes []Node, nodeType string, result *[]Node) {
	for _, node := range nodes {
		if node.Type == nodeType {
			*result = append(*result, node)
		}
		if len(node.Content) > 0 {
			p.findNodesByType(node.Content, nodeType, result)
		}
	}
}

// GetMentions extracts all mentions from the document
func (p *JiraDocumentParser) GetMentions(doc *Document) []string {
	var mentions []string
	mentionNodes := p.GetNodesByType(doc, string(MentionNode))

	for _, node := range mentionNodes {
		if node.Attrs != nil {
			if id, ok := node.Attrs["id"].(string); ok {
				mentions = append(mentions, id)
			}
		}
	}

	return mentions
}

// GetLinks extracts all links from the document
func (p *JiraDocumentParser) GetLinks(doc *Document) []string {
	var links []string
	p.extractLinksFromNodes(doc.Content, &links)
	return links
}

// extractLinksFromNodes recursively extracts links from nodes
func (p *JiraDocumentParser) extractLinksFromNodes(nodes []Node, links *[]string) {
	for _, node := range nodes {
		// Check marks for link marks
		for _, mark := range node.Marks {
			if mark.Type == string(LinkMark) && mark.Attrs != nil {
				if href, ok := mark.Attrs["href"].(string); ok {
					*links = append(*links, href)
				}
			}
		}

		// Check inline card nodes
		if node.Type == string(InlineCardNode) && node.Attrs != nil {
			if url, ok := node.Attrs["url"].(string); ok {
				*links = append(*links, url)
			}
		}

		// Recursively check content
		if len(node.Content) > 0 {
			p.extractLinksFromNodes(node.Content, links)
		}
	}
}

// CreateSimpleDocument creates a simple ADF document with text content
func (p *JiraDocumentParser) CreateSimpleDocument(text string) *Document {
	return &Document{
		Version: 1,
		Type:    string(DocNode),
		Content: []Node{
			{
				Type: string(ParagraphNode),
				Content: []Node{
					{
						Type: string(TextNode),
						Text: text,
					},
				},
			},
		},
	}
}

// CreateDocumentWithHeading creates an ADF document with a heading and content
func (p *JiraDocumentParser) CreateDocumentWithHeading(heading string, level int, content string) *Document {
	nodes := []Node{
		{
			Type: string(HeadingNode),
			Attrs: map[string]interface{}{
				"level": level,
			},
			Content: []Node{
				{
					Type: string(TextNode),
					Text: heading,
				},
			},
		},
	}

	if content != "" {
		nodes = append(nodes, Node{
			Type: string(ParagraphNode),
			Content: []Node{
				{
					Type: string(TextNode),
					Text: content,
				},
			},
		})
	}

	return &Document{
		Version: 1,
		Type:    string(DocNode),
		Content: nodes,
	}
}

// AddBoldText adds bold formatting to text
func (p *JiraDocumentParser) AddBoldText(text string) Node {
	return Node{
		Type: string(TextNode),
		Text: text,
		Marks: []Mark{
			{
				Type: string(StrongMark),
			},
		},
	}
}

// AddItalicText adds italic formatting to text
func (p *JiraDocumentParser) AddItalicText(text string) Node {
	return Node{
		Type: string(TextNode),
		Text: text,
		Marks: []Mark{
			{
				Type: string(EmMark),
			},
		},
	}
}

// AddLinkText adds a link to text
func (p *JiraDocumentParser) AddLinkText(text, url string) Node {
	return Node{
		Type: string(TextNode),
		Text: text,
		Marks: []Mark{
			{
				Type: string(LinkMark),
				Attrs: map[string]interface{}{
					"href": url,
				},
			},
		},
	}
}

// ValidateDocument validates if the document structure is valid ADF
func (p *JiraDocumentParser) ValidateDocument(doc *Document) error {
	if doc.Type != string(DocNode) {
		return fmt.Errorf("root node must be of type 'doc'")
	}

	if doc.Version <= 0 {
		return fmt.Errorf("document version must be greater than 0")
	}

	return p.validateNodes(doc.Content)
}

// validateNodes recursively validates nodes structure
func (p *JiraDocumentParser) validateNodes(nodes []Node) error {
	for _, node := range nodes {
		if node.Type == "" {
			return fmt.Errorf("node type cannot be empty")
		}

		// Text nodes should have text content
		if node.Type == string(TextNode) && node.Text == "" {
			return fmt.Errorf("text node must have text content")
		}

		// Recursively validate child nodes
		if len(node.Content) > 0 {
			if err := p.validateNodes(node.Content); err != nil {
				return err
			}
		}
	}

	return nil
}
