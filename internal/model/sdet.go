package model

type RegressionReport struct {
	Squad         string  `json:"squad"`
	DateRelease   string  `json:"date_release"`
	Platform      string  `json:"platform"`
	Env           string  `json:"env"`
	TotalPassed   int64   `json:"total_passed"`
	TotalFailed   int64   `json:"total_failed"`
	ExecutionTime float64 `json:"exection_time"`
	SuccessRate   float64 `json:"success_rate"`
}

type RegressionExecutionTimeReport struct {
	TestID        string  `json:"test_id"`
	DateRelease   string  `json:"date_release"`
	Platform      string  `json:"platform"`
	Status        string  `json:"status"`
	ExecutionTime float64 `json:"execution_time"`
}

type TestCaseReport struct {
	Author   string `json:"author"`
	Date     string `json:"date"`
	Platform string `json:"platform"`
	Value    int64  `json:"value"`
}
