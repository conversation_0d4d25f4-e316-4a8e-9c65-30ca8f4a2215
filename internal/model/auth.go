package model

import (
	"encoding/hex"
	"time"

	"github.com/go-webauthn/webauthn/webauthn"
)

// User represents a user in the system with password and optional WebAuthn authentication
type User struct {
	ID                   uint                 `gorm:"primaryKey" json:"id"`
	Username             string               `gorm:"uniqueIndex;not null" json:"username"`
	DisplayName          string               `gorm:"not null" json:"display_name"`
	PasswordHash         string               `gorm:"not null" json:"-"` // Never expose password hash in JSON
	Role                 UserRole             `gorm:"not null;default:'user'" json:"role"`
	IsApproved           bool                 `gorm:"default:false" json:"is_approved"`
	IsActive             bool                 `gorm:"default:true" json:"is_active"`
	WebAuthnEnabled      bool                 `gorm:"column:webauthn_enabled;default:false" json:"webauthn_enabled"`
	PasswordResetToken   *string              `gorm:"index" json:"-"` // Never expose reset token in JSON
	PasswordResetExpires *time.Time           `json:"-"`
	LastLoginAt          *time.Time           `json:"last_login_at"`
	FailedLoginAttempts  int                  `gorm:"default:0" json:"-"`
	LockedUntil          *time.Time           `json:"-"`
	CreatedAt            time.Time            `json:"created_at"`
	UpdatedAt            time.Time            `json:"updated_at"`
	Credentials          []WebAuthnCredential `gorm:"foreignKey:UserID" json:"credentials,omitempty"`
}

func (User) TableName() string {
	return "pentools_users"
}

// WebAuthnCredential represents a WebAuthn credential for a user
type WebAuthnCredential struct {
	ID              uint       `gorm:"primaryKey" json:"id"`
	UserID          uint       `gorm:"not null;index" json:"user_id"`
	CredentialID    []byte     `gorm:"not null;uniqueIndex" json:"-"`
	CredentialIDHex string     `gorm:"not null;uniqueIndex" json:"credential_id"`
	PublicKey       []byte     `gorm:"not null" json:"-"`
	AttestationType string     `gorm:"not null" json:"attestation_type"`
	AAGUID          []byte     `gorm:"column:aaguid" json:"-"`
	SignCount       uint32     `gorm:"default:0" json:"sign_count"`
	DeviceName      string     `gorm:"not null" json:"device_name"`
	DeviceType      DeviceType `gorm:"not null" json:"device_type"`
	BackupEligible  bool       `gorm:"default:false" json:"backup_eligible"`
	BackupState     bool       `gorm:"default:false" json:"backup_state"`
	CreatedAt       time.Time  `json:"created_at"`
	LastUsedAt      *time.Time `json:"last_used_at"`
}

func (WebAuthnCredential) TableName() string {
	return "pentools_webauthn_credentials"
}

// UserRole represents user roles in the system
type UserRole string

const (
	RoleAdmin UserRole = "admin"
	RoleUser  UserRole = "user"
)

// DeviceType represents the type of authenticator device
type DeviceType string

const (
	DeviceTypePlatform      DeviceType = "platform"       // Built-in authenticators (Touch ID, Windows Hello)
	DeviceTypeCrossPlatform DeviceType = "cross-platform" // External authenticators (security keys)
)

// WebAuthnSession represents a temporary session for WebAuthn challenges
type WebAuthnSession struct {
	ID          uint        `gorm:"primaryKey" json:"id"`
	SessionID   string      `gorm:"uniqueIndex;not null" json:"session_id"`
	Username    string      `gorm:"not null" json:"username"`
	Challenge   []byte      `gorm:"not null" json:"-"`
	SessionType SessionType `gorm:"not null" json:"session_type"`
	ExpiresAt   time.Time   `gorm:"not null" json:"expires_at"`
	CreatedAt   time.Time   `json:"created_at"`
}

func (WebAuthnSession) TableName() string {
	return "pentools_webauthn_sessions"
}

// SessionType represents the type of WebAuthn session
type SessionType string

const (
	SessionTypeRegistration   SessionType = "registration"
	SessionTypeAuthentication SessionType = "authentication"
)

// BeforeCreate hook to set CredentialIDHex from CredentialID
func (wc *WebAuthnCredential) BeforeCreate() error {
	if len(wc.CredentialID) > 0 {
		wc.CredentialIDHex = hex.EncodeToString(wc.CredentialID)
	}
	return nil
}

// BeforeUpdate hook to update CredentialIDHex when CredentialID changes
func (wc *WebAuthnCredential) BeforeUpdate() error {
	if len(wc.CredentialID) > 0 {
		wc.CredentialIDHex = hex.EncodeToString(wc.CredentialID)
	}
	return nil
}

// WebAuthn interface implementations for User

// WebAuthnID returns the user's ID as bytes for WebAuthn
func (u User) WebAuthnID() []byte {
	return []byte(u.Username)
}

// WebAuthnName returns the user's username for WebAuthn
func (u User) WebAuthnName() string {
	return u.Username
}

// WebAuthnDisplayName returns the user's display name for WebAuthn
func (u User) WebAuthnDisplayName() string {
	return u.DisplayName
}

// WebAuthnIcon returns an empty string as we don't use icons
func (u User) WebAuthnIcon() string {
	return ""
}

// WebAuthnCredentials returns the user's credentials as WebAuthn credentials
func (u User) WebAuthnCredentials() []webauthn.Credential {
	credentials := make([]webauthn.Credential, len(u.Credentials))
	for i, cred := range u.Credentials {
		credentials[i] = webauthn.Credential{
			ID:              cred.CredentialID,
			PublicKey:       cred.PublicKey,
			AttestationType: cred.AttestationType,
			Flags: webauthn.CredentialFlags{
				BackupEligible: cred.BackupEligible,
				BackupState:    cred.BackupState,
			},
			Authenticator: webauthn.Authenticator{
				AAGUID:       cred.AAGUID,
				SignCount:    cred.SignCount,
				CloneWarning: false,
			},
		}
	}
	return credentials
}

// LoginResponse represents the response after successful login
type LoginResponse struct {
	AccessToken  string `json:"accessToken"`
	RefreshToken string `json:"refreshToken"`
	User         User   `json:"user"`
}

// UserListResponse represents a paginated list of users
type UserListResponse struct {
	Users      []User `json:"users"`
	Total      int64  `json:"total"`
	Page       int    `json:"page"`
	Limit      int    `json:"limit"`
	TotalPages int    `json:"total_pages"`
}

// CreateUserRequest represents the request to create a new user
type CreateUserRequest struct {
	Username    string   `json:"username" validate:"required,min=3,max=50"`
	DisplayName string   `json:"display_name" validate:"required,min=1,max=100"`
	Role        UserRole `json:"role" validate:"required,oneof=admin user"`
	Password    string   `json:"password" validate:"required,min=6"`
}

// UpdateUserRequest represents the request to update a user
type UpdateUserRequest struct {
	DisplayName *string   `json:"display_name,omitempty" validate:"omitempty,min=1,max=100"`
	Role        *UserRole `json:"role,omitempty" validate:"omitempty,oneof=admin user"`
	IsApproved  *bool     `json:"is_approved,omitempty"`
	IsActive    *bool     `json:"is_active,omitempty"`
}

// UserRegistrationRequest represents new user registration request
type UserRegistrationRequest struct {
	Username    string `json:"username" validate:"required,min=3,max=50"`
	Password    string `json:"password" validate:"required,min=6"`
	DisplayName string `json:"display_name" validate:"required,min=1,max=100"`
}

// PasswordLoginRequest represents password-based login request
type PasswordLoginRequest struct {
	Username string `json:"username" validate:"required,min=3,max=50"`
	Password string `json:"password" validate:"required,min=6"`
}

// PasswordLoginResponse represents successful password login response
type PasswordLoginResponse struct {
	AccessToken  string `json:"accessToken"`
	RefreshToken string `json:"refreshToken"`
	User         User   `json:"user"`
}

// WebAuthnRegistrationRequest represents WebAuthn registration initiation request (for authenticated user)
type WebAuthnRegistrationRequest struct {
	DeviceName string `json:"device_name" validate:"max=100"`
}

// RegistrationRequest represents WebAuthn registration initiation request (legacy/standalone)
type RegistrationRequest struct {
	Username    string `json:"username" validate:"required,min=3,max=50"`
	DisplayName string `json:"display_name" validate:"required,min=1,max=100"`
}

// RegistrationResponse represents WebAuthn registration completion request
type RegistrationResponse struct {
	Username   string      `json:"username" validate:"required"`
	Credential interface{} `json:"credential" validate:"required"`
}

// LoginRequest represents WebAuthn authentication initiation request
type LoginRequest struct {
	Username string `json:"username" validate:"required"`
}

// LoginFinishRequest represents WebAuthn authentication completion request
type LoginFinishRequest struct {
	Username   string      `json:"username" validate:"required"`
	Credential interface{} `json:"credential" validate:"required"`
}

// DeviceRegistrationRequest represents adding a new device to existing account
type DeviceRegistrationRequest struct {
	DeviceName string      `json:"device_name" validate:"required,min=1,max=50"`
	Credential interface{} `json:"credential" validate:"required"`
}
