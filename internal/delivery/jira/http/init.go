package http

import (
	"context"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/pkg/config"
	"github.com/labstack/echo/v4"
)

type Usecase interface {
	GetReport(ctx context.Context, req dto.GetJiraReportRequest) (*dto.GetJiraReportResponse, error)
	GetUserEpic(ctx context.Context, req dto.GetUserEpicRequest) (*dto.GetUserEpicResponse, error)
	GetBugs(ctx context.Context, req dto.GetBugsRequest) (*dto.GetJiraBugsResponse, error)
	GetTasks(ctx context.Context, req dto.GetTasksRequest) (*dto.GetTasksResponse, error)
	GetCount(ctx context.Context, req dto.GetCountRequest) (*dto.GetCountResponse, error)
	GetSprints(ctx context.Context, req dto.GetSprintRequest) (*dto.GetSprintResponse, error)
	GetIssueChangelog(ctx context.Context, req dto.GetChangelogRequest) (*dto.GetChangelogResponse, error)
}
type JiraHTTPHandler struct {
	usecase Usecase
}

func New(_ *config.Config, e *echo.Echo, usecase Usecase) {
	h := &JiraHTTPHandler{
		usecase: usecase,
	}
	e.GET("/api/jira/report", h.GetReport)
	e.GET("/api/jira/:user_id/epic", h.GetUserEpic)
	e.GET("/api/jira/epic", h.GetEpics)
	e.GET("/api/jira/bug", h.GetBugs)
	e.GET("/api/jira/task", h.GetTasks)
	e.GET("/api/jira/count", h.GetCount)
	e.GET("/api/jira/sprint", h.GetSprints)
	e.GET("/api/jira/issue/:issue_key/changelog", h.GetIssueChangelog)
}
