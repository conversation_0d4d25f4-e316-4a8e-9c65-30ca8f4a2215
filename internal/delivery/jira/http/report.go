package http

import (
	"net/http"
	"net/url"
	"time"

	validation "github.com/go-ozzo/ozzo-validation/v4"
	"github.com/labstack/echo/v4"

	"github.com/Lionparcel/pentools/internal/dto"
)

// GetReport godoc
// @Summary      Get report from jira for PE
// @Description  Get Report from jira for PE.
// @Tags         jira
// @Accept       json
// @Produce      json
// @Param        q query dto.GetJiraReportRequest false "filter query"
// @Router       /api/jira/report [get]
// @Success      200  {object}  dto.GetJiraReportResponse
func (h *JiraHTTPHandler) GetReport(c echo.Context) error {
	var query dto.GetJiraReportRequest
	if err := c.Bind(&query); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	if err := validation.ValidateStruct(&query,
		validation.Field(&query.StartDate, validation.When(query.StartDate != "", validation.Date(time.DateOnly))),
		validation.Field(&query.EndDate, validation.When(query.EndDate != "", validation.Date(time.DateOnly))),
	); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	resp, err := h.usecase.GetReport(c.Request().Context(), query)
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}
	return c.JSON(http.StatusOK, resp)
}

// GetUserEpic godoc
// @Summary      Get Epic of PE
// @Description  Get List Epic of PE and the issues handled
// @Tags         jira
// @Accept       json
// @Produce      json
// @Param        q query dto.GetUserEpicRequest false "filter query"
// @Param        user_id path string true "filter path"
// @Router       /api/jira/{user_id}/epic [get]
// @Success      200  {object}  dto.GetUserEpicResponse
// @Deprecated
func (h *JiraHTTPHandler) GetUserEpic(c echo.Context) error {
	var query dto.GetUserEpicRequest
	if err := c.Bind(&query); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	if err := validation.ValidateStruct(&query,
		validation.Field(&query.StartDate, validation.When(query.StartDate != "", validation.Date(time.DateOnly))),
		validation.Field(&query.EndDate, validation.When(query.EndDate != "", validation.Date(time.DateOnly))),
		validation.Field(&query.UserID, validation.Required),
	); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	var err error
	query.UserID, err = url.QueryUnescape(query.UserID)
	if err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	query.UserIDs = []string{query.UserID}
	resp, err := h.usecase.GetUserEpic(c.Request().Context(), query)
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, resp)
}

// GetEpics godoc
// @Summary      Get Epic of PE
// @Description  Get List Epic of PE and the issues handled
// @Tags         jira
// @Accept       json
// @Produce      json
// @Param        q query dto.GetEpicsRequest false "filter query"
// @Router       /api/jira/epic [get]
// @Success      200  {object}  dto.GetUserEpicResponse
func (h *JiraHTTPHandler) GetEpics(c echo.Context) error {
	var query dto.GetEpicsRequest
	if err := c.Bind(&query); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	if err := validation.ValidateStruct(&query,
		validation.Field(&query.StartDate, validation.When(query.StartDate != "", validation.Date(time.DateOnly))),
		validation.Field(&query.EndDate, validation.When(query.EndDate != "", validation.Date(time.DateOnly))),
	); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	if len(query.UserIDs) == 0 {
		query.UserIDs = []string{"712020:b6ff5117-0224-4899-a5fc-7499e7a23188", "5fc60f0b0dd553006fc2a25a", "712020:7727b678-7a39-4627-a7ca-62d9a0da12e1", "712020:fdb9158c-f15a-448d-aa56-61d177b2f510", "712020:3b3b163c-7449-42d9-8722-a8b825e1410d", "712020:781a66ca-5cf6-40ea-9593-035ba947fd40", "712020:e6324922-858c-4a85-a3a2-d6fe359325f7", "712020:01e1fed4-1e4f-4481-aeb2-c096155e1f7b", "557058:170997e2-bc24-4549-ac70-c9dd666bcd0c"}
	}

	resp, err := h.usecase.GetUserEpic(c.Request().Context(), dto.GetUserEpicRequest{
		StartDate: query.StartDate,
		EndDate:   query.EndDate,
		UserIDs:   query.UserIDs,
	})
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}
	return c.JSON(http.StatusOK, resp)
}

// GetBugs godoc
// @Summary      Get Bugs reported by QA
// @Description  Get Bugs reported by QA
// @Tags         jira
// @Accept       json
// @Produce      json
// @Param        q query dto.GetBugsRequest false "filter query"
// @Router       /api/jira/bug [get]
// @Success      200  {object}  dto.GetJiraBugsResponse
func (h *JiraHTTPHandler) GetBugs(c echo.Context) error {
	var query dto.GetBugsRequest
	if err := c.Bind(&query); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	if err := validation.ValidateStruct(&query,
		validation.Field(&query.StartDate, validation.When(query.StartDate != "", validation.Date(time.DateOnly))),
		validation.Field(&query.EndDate, validation.When(query.EndDate != "", validation.Date(time.DateOnly))),
	); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	resp, err := h.usecase.GetBugs(c.Request().Context(), query)
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}
	return c.JSON(http.StatusOK, resp)
}

// GetTasks godoc
// @Summary      Get tasks from Jira
// @Description  Get tasks from Jira with filtering options
// @Tags         jira
// @Accept       json
// @Produce      json
// @Param        q query dto.GetTasksRequest false "filter query"
// @Router       /api/jira/task [get]
// @Success      200  {object}  dto.GetTasksResponse
func (h *JiraHTTPHandler) GetTasks(c echo.Context) error {
	var query dto.GetTasksRequest
	if err := c.Bind(&query); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	if err := validation.ValidateStruct(&query, validation.Field(&query.JQL, validation.Required)); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	resp, err := h.usecase.GetTasks(c.Request().Context(), query)
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}
	return c.JSON(http.StatusOK, resp)
}

// GetCount godoc
// @Summary      Get count of issues from Jira
// @Description  Get count of issues from Jira using JQL query
// @Tags         jira
// @Accept       json
// @Produce      json
// @Param        q query dto.GetCountRequest false "filter query"
// @Router       /api/jira/count [get]
// @Success      200  {object}  dto.GetCountResponse
func (h *JiraHTTPHandler) GetCount(c echo.Context) error {
	var query dto.GetCountRequest
	if err := c.Bind(&query); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	if err := validation.ValidateStruct(&query, validation.Field(&query.JQL, validation.Required)); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	resp, err := h.usecase.GetCount(c.Request().Context(), query)
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}
	return c.JSON(http.StatusOK, resp)
}

// GetSprints godoc
// @Summary      Get sprints from JIRA board
// @Description  Get sprints from JIRA board with optional date filtering
// @Tags         jira
// @Accept       json
// @Produce      json
// @Param        q query dto.GetSprintRequest false "filter query"
// @Router       /api/jira/sprint [get]
// @Success      200  {object}  dto.GetSprintResponse
func (h *JiraHTTPHandler) GetSprints(c echo.Context) error {
	var query dto.GetSprintRequest
	if err := c.Bind(&query); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}

	// Set default board ID if not provided
	if query.BoardID == 0 {
		query.BoardID = 75
	}

	resp, err := h.usecase.GetSprints(c.Request().Context(), query)
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}
	return c.JSON(http.StatusOK, resp)
}

// GetIssueChangelog godoc
// @Summary      Get changelog of JIRA issue
// @Description  Get changelog history of a specific JIRA issue for time tracking analysis
// @Tags         jira
// @Accept       json
// @Produce      json
// @Param        issue_key path string true "JIRA issue key"
// @Router       /api/jira/issue/{issue_key}/changelog [get]
// @Success      200  {object}  dto.GetChangelogResponse
func (h *JiraHTTPHandler) GetIssueChangelog(c echo.Context) error {
	var req dto.GetChangelogRequest
	if err := c.Bind(&req); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}

	resp, err := h.usecase.GetIssueChangelog(c.Request().Context(), req)
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, resp)
}
