package http

import (
	"net/http"
	"time"

	"github.com/Lionparcel/pentools/internal/dto"
	validation "github.com/go-ozzo/ozzo-validation/v4"
	"github.com/labstack/echo/v4"
)

// GetBug godoc
// @Summary      Get Bug for Engineering Team.
// @Description  Get Bug for Engineering Team.
// @Tags         eng
// @Accept       json
// @Produce      json
// @Param        q query dto.GetEngRequest false "filter query"
// @Router       /api/eng/bug [get]
// @Success      200  {object}  dto.GetEngBugResponse
func (h *SEHTTPHandler) GetBug(c echo.Context) error {
	var query dto.GetEngRequest
	if err := c.Bind(&query); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	if err := validation.ValidateStruct(&query,
		validation.Field(&query.StartDate, validation.When(query.StartDate != "", validation.Date(time.DateOnly))),
		validation.Field(&query.EndDate, validation.When(query.EndDate != "", validation.Date(time.DateOnly))),
	); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	resp, err := h.usecase.GetBug(c.Request().Context(), query)
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, resp)
}
