package http

import (
	"net/http"
	"strconv"

	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/middleware"
	"github.com/labstack/echo/v4"
)

// PasswordLogin handles password-based authentication
// @Summary Login with password
// @Description Authenticate user with username and password
// @Tags auth
// @Accept json
// @Produce json
// @Param request body model.PasswordLoginRequest true "Login request"
// @Success 200 {object} model.PasswordLoginResponse
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/auth/login [post]
func (h *Handler) PasswordLogin(c echo.Context) error {
	var req model.PasswordLoginRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if req.Username == "" || req.Password == "" {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Username and password are required",
		})
	}

	// Authenticate user
	response, err := h.authUC.LoginWithPassword(c.Request().Context(), req)
	if err != nil {
		return echo.NewHTTPError(http.StatusUnauthorized, map[string]string{
			"error": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, response)
}

// ChangePassword handles password change requests
// @Summary Change password
// @Description Change user password (requires authentication)
// @Tags auth
// @Accept json
// @Produce json
// @Param request body map[string]string true "Password change request"
// @Success 200 {object} map[string]string
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/auth/change-password [post]
func (h *Handler) ChangePassword(c echo.Context) error {
	// Get user ID from context (set by auth middleware)
	userCtx, err := middleware.GetUserFromContext(c)
	if err != nil {
		return echo.NewHTTPError(http.StatusUnauthorized, map[string]string{
			"error": "Authentication required",
		})
	}

	var req struct {
		CurrentPassword string `json:"current_password"`
		NewPassword     string `json:"new_password"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if req.CurrentPassword == "" || req.NewPassword == "" {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Current password and new password are required",
		})
	}

	if len(req.NewPassword) < 6 {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "New password must be at least 6 characters long",
		})
	}

	// Change password
	err = h.authUC.ChangePassword(c.Request().Context(), userCtx.ID, req.CurrentPassword, req.NewPassword)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "Password changed successfully",
	})
}

// EnableWebAuthn enables WebAuthn for authenticated user
// @Summary Enable WebAuthn
// @Description Enable WebAuthn enhancement for authenticated user
// @Tags auth
// @Accept json
// @Produce json
// @Success 200 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/auth/webauthn/enable [post]
func (h *Handler) EnableWebAuthn(c echo.Context) error {
	// Get user ID from context (set by auth middleware)
	userCtx, err := middleware.GetUserFromContext(c)
	if err != nil {
		return echo.NewHTTPError(http.StatusUnauthorized, map[string]string{
			"error": "Authentication required",
		})
	}

	// Enable WebAuthn for user
	err = h.authUC.EnableWebAuthn(c.Request().Context(), userCtx.ID)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": "Failed to enable WebAuthn",
		})
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "WebAuthn enabled successfully",
	})
}

// DisableWebAuthn disables WebAuthn for authenticated user
// @Summary Disable WebAuthn
// @Description Disable WebAuthn and remove all credentials for authenticated user
// @Tags auth
// @Accept json
// @Produce json
// @Success 200 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/auth/webauthn/disable [post]
func (h *Handler) DisableWebAuthn(c echo.Context) error {
	// Get user ID from context (set by auth middleware)
	userCtx, err := middleware.GetUserFromContext(c)
	if err != nil {
		return echo.NewHTTPError(http.StatusUnauthorized, map[string]string{
			"error": "Authentication required",
		})
	}

	// Disable WebAuthn for user
	err = h.authUC.DisableWebAuthn(c.Request().Context(), userCtx.ID)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": "Failed to disable WebAuthn",
		})
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "WebAuthn disabled successfully",
	})
}

// AdminChangeUserPassword handles admin password change requests for other users
// @Summary Change user password (admin only)
// @Description Change password for any user (admin only)
// @Tags admin
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param userId path int true "User ID"
// @Param request body map[string]string true "Password change request"
// @Success 200 {object} map[string]string
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/users/{userId}/change-password [post]
func (h *Handler) AdminChangeUserPassword(c echo.Context) error {
	userIDStr := c.Param("userId")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid user ID",
		})
	}

	var req struct {
		NewPassword string `json:"new_password"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if req.NewPassword == "" {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "New password is required",
		})
	}

	if len(req.NewPassword) < 6 {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "New password must be at least 6 characters long",
		})
	}

	// Admin change password (no current password required)
	err = h.authUC.AdminChangeUserPassword(c.Request().Context(), uint(userID), req.NewPassword)
	if err != nil {
		if err.Error() == "user not found" {
			return echo.NewHTTPError(http.StatusNotFound, map[string]string{
				"error": err.Error(),
			})
		}
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "User password changed successfully",
	})
}