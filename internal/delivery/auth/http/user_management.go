package http

import (
	"bytes"
	"encoding/json"
	"net/http"
	"strconv"

	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/middleware"
	"github.com/Lionparcel/pentools/pkg/webauthn"
	"github.com/go-webauthn/webauthn/protocol"
	"github.com/labstack/echo/v4"
)

// GetCurrentUserProfile gets current user's profile
// @Summary Get current user profile
// @Description Gets the current authenticated user's profile information
// @Tags profile
// @Security BearerAuth
// @Produce json
// @Success 200 {object} model.User
// @Failure 401 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/auth/profile [get]
func (h *Handler) GetCurrentUserProfile(c echo.Context) error {
	userCtx, err := middleware.GetUserFromContext(c)
	if err != nil {
		return echo.NewHTTPError(http.StatusUnauthorized, map[string]string{
			"error": "User not authenticated",
		})
	}

	user, err := h.authUC.GetCurrentUserProfile(c.Request().Context(), userCtx.ID)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, user)
}

// UpdateCurrentUserProfile updates current user's profile
// @Summary Update current user profile
// @Description Updates the current authenticated user's profile information
// @Tags profile
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body map[string]string true "Profile update request"
// @Success 200 {object} model.User
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/auth/profile [put]
func (h *Handler) UpdateCurrentUserProfile(c echo.Context) error {
	userCtx, err := middleware.GetUserFromContext(c)
	if err != nil {
		return echo.NewHTTPError(http.StatusUnauthorized, map[string]string{
			"error": "User not authenticated",
		})
	}

	var req map[string]string
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid request body",
		})
	}

	displayName := req["display_name"]
	if displayName == "" {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Display name is required",
		})
	}

	user, err := h.authUC.UpdateCurrentUserProfile(c.Request().Context(), userCtx.ID, displayName)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, user)
}

// GetUserDevices gets current user's devices
// @Summary Get user devices
// @Description Gets all WebAuthn credentials/devices for the current user
// @Tags devices
// @Security BearerAuth
// @Produce json
// @Success 200 {array} model.WebAuthnCredential
// @Failure 401 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/auth/devices [get]
func (h *Handler) GetUserDevices(c echo.Context) error {
	userCtx, err := middleware.GetUserFromContext(c)
	if err != nil {
		return echo.NewHTTPError(http.StatusUnauthorized, map[string]string{
			"error": "User not authenticated",
		})
	}

	devices, err := h.authUC.GetUserDevices(c.Request().Context(), userCtx.ID)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, devices)
}

// BeginDeviceRegistration starts device registration for current user
// @Summary Start device registration
// @Description Starts WebAuthn registration for an additional device
// @Tags devices
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body map[string]string true "Device registration request"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/auth/devices/register [post]
func (h *Handler) BeginDeviceRegistration(c echo.Context) error {
	userCtx, err := middleware.GetUserFromContext(c)
	if err != nil {
		return echo.NewHTTPError(http.StatusUnauthorized, map[string]string{
			"error": "User not authenticated",
		})
	}

	var req map[string]string
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid request body",
		})
	}

	deviceName := req["device_name"]
	if deviceName == "" {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Device name is required",
		})
	}

	creation, sessionData, err := h.authUC.BeginDeviceRegistration(c.Request().Context(), userCtx.ID, deviceName)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": err.Error(),
		})
	}

	// Store session data
	sessions[sessionData.SessionID] = sessionData

	response := map[string]interface{}{
		"publicKey": creation,
		"sessionId": sessionData.SessionID,
	}

	return c.JSON(http.StatusOK, response)
}

// FinishDeviceRegistration completes device registration for current user
// @Summary Complete device registration
// @Description Completes WebAuthn registration for an additional device
// @Tags devices
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body model.DeviceRegistrationRequest true "Device registration completion"
// @Success 200 {object} map[string]string
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/auth/devices/register/finish [post]
func (h *Handler) FinishDeviceRegistration(c echo.Context) error {
	userCtx, err := middleware.GetUserFromContext(c)
	if err != nil {
		return echo.NewHTTPError(http.StatusUnauthorized, map[string]string{
			"error": "User not authenticated",
		})
	}

	var req model.DeviceRegistrationRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid request body",
		})
	}

	// Get session data
	sessionID := c.Request().Header.Get("X-Session-ID")
	if sessionID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Session ID required in X-Session-ID header",
		})
	}

	sessionDataInterface, exists := sessions[sessionID]
	if !exists {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid or expired session",
		})
	}

	sessionData, ok := sessionDataInterface.(*webauthn.SessionData)
	if !ok {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": "Invalid session data",
		})
	}

	// Convert credential to JSON and parse it
	credentialJSON, err := json.Marshal(req.Credential)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Failed to marshal credential: " + err.Error(),
		})
	}

	// Parse credential response using bytes reader
	parsedResponse, err := protocol.ParseCredentialCreationResponseBody(bytes.NewReader(credentialJSON))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Failed to parse credential response: " + err.Error(),
		})
	}

	err = h.authUC.FinishDeviceRegistration(c.Request().Context(), userCtx.ID, req.DeviceName, sessionData, parsedResponse)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": err.Error(),
		})
	}

	// Clean up session
	delete(sessions, sessionID)

	return c.JSON(http.StatusOK, map[string]string{
		"message": "Device registered successfully",
	})
}

// DeleteUserDevice deletes a user's device
// @Summary Delete user device
// @Description Deletes a WebAuthn credential/device for the current user
// @Tags devices
// @Security BearerAuth
// @Param deviceId path int true "Device ID"
// @Success 200 {object} map[string]string
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/auth/devices/{deviceId} [delete]
func (h *Handler) DeleteUserDevice(c echo.Context) error {
	userCtx, err := middleware.GetUserFromContext(c)
	if err != nil {
		return echo.NewHTTPError(http.StatusUnauthorized, map[string]string{
			"error": "User not authenticated",
		})
	}

	deviceIDStr := c.Param("deviceId")
	deviceID, err := strconv.ParseUint(deviceIDStr, 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid device ID",
		})
	}

	err = h.authUC.DeleteUserDevice(c.Request().Context(), userCtx.ID, uint(deviceID))
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "Device deleted successfully",
	})
}

// Admin-only endpoints

// GetUsers gets all users (admin only)
// @Summary Get all users
// @Description Gets a paginated list of all users (admin only)
// @Tags admin
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} model.UserListResponse
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/users [get]
func (h *Handler) GetUsers(c echo.Context) error {
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if limit < 1 || limit > 100 {
		limit = 20
	}

	response, err := h.authUC.GetUsers(c.Request().Context(), page, limit)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, response)
}

// CreateUser creates a new user (admin only)
// @Summary Create user
// @Description Creates a new user account (admin only)
// @Tags admin
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body model.CreateUserRequest true "User creation request"
// @Success 201 {object} model.User
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/users [post]
func (h *Handler) CreateUser(c echo.Context) error {
	var req model.CreateUserRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid request body",
		})
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Validation failed: " + err.Error(),
		})
	}

	user, err := h.authUC.CreateUser(c.Request().Context(), &req)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": err.Error(),
		})
	}

	return c.JSON(http.StatusCreated, user)
}

// GetUser gets a specific user (admin only)
// @Summary Get user
// @Description Gets a specific user by ID (admin only)
// @Tags admin
// @Security BearerAuth
// @Param userId path int true "User ID"
// @Success 200 {object} model.User
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/users/{userId} [get]
func (h *Handler) GetUser(c echo.Context) error {
	userIDStr := c.Param("userId")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid user ID",
		})
	}

	user, err := h.authUC.GetUser(c.Request().Context(), uint(userID))
	if err != nil {
		return echo.NewHTTPError(http.StatusNotFound, map[string]string{
			"error": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, user)
}

// UpdateUser updates a user (admin only)
// @Summary Update user
// @Description Updates a user's information (admin only)
// @Tags admin
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param userId path int true "User ID"
// @Param request body model.UpdateUserRequest true "User update request"
// @Success 200 {object} model.User
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/users/{userId} [put]
func (h *Handler) UpdateUser(c echo.Context) error {
	userIDStr := c.Param("userId")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid user ID",
		})
	}

	var req model.UpdateUserRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid request body",
		})
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Validation failed: " + err.Error(),
		})
	}

	user, err := h.authUC.UpdateUser(c.Request().Context(), uint(userID), &req)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, user)
}

// DeleteUser deletes a user (admin only)
// @Summary Delete user
// @Description Soft deletes a user account (admin only)
// @Tags admin
// @Security BearerAuth
// @Param userId path int true "User ID"
// @Success 200 {object} map[string]string
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/users/{userId} [delete]
func (h *Handler) DeleteUser(c echo.Context) error {
	userIDStr := c.Param("userId")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid user ID",
		})
	}

	err = h.authUC.DeleteUser(c.Request().Context(), uint(userID))
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "User deleted successfully",
	})
}

// UpdateUserRole updates a user's role (admin only)
// @Summary Update user role
// @Description Updates a user's role (admin only)
// @Tags admin
// @Security BearerAuth
// @Accept json
// @Param userId path int true "User ID"
// @Param request body map[string]string true "Role update request"
// @Success 200 {object} map[string]string
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/users/{userId}/role [put]
func (h *Handler) UpdateUserRole(c echo.Context) error {
	userIDStr := c.Param("userId")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid user ID",
		})
	}

	var req map[string]string
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid request body",
		})
	}

	roleStr := req["role"]
	if roleStr == "" {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Role is required",
		})
	}

	role := model.UserRole(roleStr)
	if role != model.RoleAdmin && role != model.RoleUser {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid role. Must be 'admin' or 'user'",
		})
	}

	err = h.authUC.UpdateUserRole(c.Request().Context(), uint(userID), role)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "User role updated successfully",
	})
}

// GetUserDevicesAdmin gets a user's devices (admin only)
// @Summary Get user devices (admin)
// @Description Gets all devices for a specific user (admin only)
// @Tags admin
// @Security BearerAuth
// @Param userId path int true "User ID"
// @Success 200 {array} model.WebAuthnCredential
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/users/{userId}/devices [get]
func (h *Handler) GetUserDevicesAdmin(c echo.Context) error {
	userIDStr := c.Param("userId")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid user ID",
		})
	}

	devices, err := h.authUC.GetUserDevices(c.Request().Context(), uint(userID))
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, devices)
}

// DeleteUserDeviceAdmin deletes a user's device (admin only)
// @Summary Delete user device (admin)
// @Description Deletes a specific device for a user (admin only)
// @Tags admin
// @Security BearerAuth
// @Param userId path int true "User ID"
// @Param deviceId path int true "Device ID"
// @Success 200 {object} map[string]string
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/users/{userId}/devices/{deviceId} [delete]
func (h *Handler) DeleteUserDeviceAdmin(c echo.Context) error {
	userIDStr := c.Param("userId")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid user ID",
		})
	}

	deviceIDStr := c.Param("deviceId")
	deviceID, err := strconv.ParseUint(deviceIDStr, 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid device ID",
		})
	}

	err = h.authUC.DeleteUserDevice(c.Request().Context(), uint(userID), uint(deviceID))
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "Device deleted successfully",
	})
}

// GetCredentialStats gets credential statistics (admin only)
// @Summary Get credential statistics
// @Description Gets statistics about WebAuthn credentials (admin only)
// @Tags admin
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/admin/stats/credentials [get]
func (h *Handler) GetCredentialStats(c echo.Context) error {
	stats, err := h.authUC.GetCredentialStats(c.Request().Context())
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, stats)
}
