package http

import (
	"errors"

	"github.com/Lionparcel/pentools/pkg/middleware"
	"github.com/labstack/echo/v4"
)

// UserContext represents the authenticated user information from middleware
type UserContext struct {
	UserID   uint   `json:"user_id"`
	Username string `json:"username"`
	Role     string `json:"role"`
}

// getUserFromContext extracts user information from Echo context set by JWT middleware
func getUserFromContext(c echo.Context) (*UserContext, error) {
	// Get user context from middleware
	userCtx, err := middleware.GetUserFromContext(c)
	if err != nil {
		return nil, errors.New("user not found in context")
	}

	return &UserContext{
		UserID:   userCtx.ID,
		Username: userCtx.Username,
		Role:     userCtx.Role,
	}, nil
}