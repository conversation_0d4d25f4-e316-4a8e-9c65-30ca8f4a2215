package http

import (
	"bytes"
	"encoding/json"
	"net/http"

	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/auth"
	"github.com/Lionparcel/pentools/pkg/webauthn"
	"github.com/go-webauthn/webauthn/protocol"
	"github.com/labstack/echo/v4"
)

// WebAuthn session storage (in production, use Redis or database)
var sessions = make(map[string]interface{})

// BeginRegistration starts WebAuthn registration process
// @Summary Start WebAuthn registration
// @Description Initiates WebAuthn registration for a new user
// @Tags auth
// @Accept json
// @Produce json
// @Param request body model.RegistrationRequest true "Registration request"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/auth/webauthn/register/begin [post]
func (h *Handler) BeginRegistration(c echo.Context) error {
	var req model.RegistrationRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid request body",
		})
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Validation failed: " + err.Error(),
		})
	}

	creation, sessionData, err := h.authUC.BeginRegistration(c.Request().Context(), req.Username, req.DisplayName)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": err.Error(),
		})
	}

	// Store session data (in production, use secure session storage)
	sessions[sessionData.SessionID] = sessionData

	// Return creation options and session ID
	response := map[string]interface{}{
		"publicKey": creation,
		"sessionId": sessionData.SessionID,
	}

	return c.JSON(http.StatusOK, response)
}

// FinishRegistration completes WebAuthn registration
// @Summary Complete WebAuthn registration
// @Description Completes WebAuthn registration and creates user account
// @Tags auth
// @Accept json
// @Produce json
// @Param request body model.RegistrationResponse true "Registration response"
// @Success 200 {object} model.LoginResponse
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/auth/webauthn/register/finish [post]
func (h *Handler) FinishRegistration(c echo.Context) error {
	var req model.RegistrationResponse
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid request body",
		})
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Validation failed: " + err.Error(),
		})
	}

	// Get session data
	sessionID := c.Request().Header.Get("X-Session-ID")
	if sessionID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Session ID required in X-Session-ID header",
		})
	}

	sessionDataInterface, exists := sessions[sessionID]
	if !exists {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid or expired session",
		})
	}

	sessionData, ok := sessionDataInterface.(*webauthn.SessionData)
	if !ok {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": "Invalid session data",
		})
	}

	// Convert credential to JSON and parse it
	credentialJSON, err := json.Marshal(req.Credential)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Failed to marshal credential: " + err.Error(),
		})
	}

	// Parse credential response using bytes reader
	parsedResponse, err := protocol.ParseCredentialCreationResponseBody(bytes.NewReader(credentialJSON))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Failed to parse credential response: " + err.Error(),
		})
	}

	// Finish registration
	loginResponse, err := h.authUC.FinishRegistration(c.Request().Context(), req.Username, sessionData, parsedResponse)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": err.Error(),
		})
	}

	// Clean up session
	delete(sessions, sessionID)

	return c.JSON(http.StatusOK, loginResponse)
}

// BeginLogin starts WebAuthn authentication
// @Summary Start WebAuthn login
// @Description Initiates WebAuthn authentication for an existing user
// @Tags auth
// @Accept json
// @Produce json
// @Param request body model.LoginRequest true "Login request"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/auth/webauthn/login/begin [post]
func (h *Handler) BeginLogin(c echo.Context) error {
	var req model.LoginRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid request body",
		})
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Validation failed: " + err.Error(),
		})
	}

	assertion, sessionData, err := h.authUC.BeginLogin(c.Request().Context(), req.Username)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": err.Error(),
		})
	}

	// Store session data
	sessions[sessionData.SessionID] = sessionData

	// Return assertion options and session ID
	response := map[string]interface{}{
		"publicKey": assertion,
		"sessionId": sessionData.SessionID,
	}

	return c.JSON(http.StatusOK, response)
}

// FinishLogin completes WebAuthn authentication
// @Summary Complete WebAuthn login
// @Description Completes WebAuthn authentication and returns JWT token
// @Tags auth
// @Accept json
// @Produce json
// @Param X-Session-ID header string true "Session ID"
// @Success 200 {object} model.LoginResponse
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/auth/webauthn/login/finish [post]
func (h *Handler) FinishLogin(c echo.Context) error {
	// Get session data
	sessionID := c.Request().Header.Get("X-Session-ID")
	if sessionID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Session ID required in X-Session-ID header",
		})
	}

	sessionDataInterface, exists := sessions[sessionID]
	if !exists {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid or expired session",
		})
	}

	sessionData, ok := sessionDataInterface.(*webauthn.SessionData)
	if !ok {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": "Invalid session data",
		})
	}

	// Get username from session data
	username := sessionData.Username

	// Finish login
	loginResponse, err := h.authUC.FinishLogin(c.Request().Context(), username, sessionData, c.Request())
	if err != nil {
		return echo.NewHTTPError(http.StatusUnauthorized, map[string]string{
			"error": err.Error(),
		})
	}

	// Clean up session
	delete(sessions, sessionID)

	return c.JSON(http.StatusOK, loginResponse)
}

// BeginDiscoverableLogin starts usernameless WebAuthn authentication
// @Summary Start discoverable login
// @Description Initiates usernameless WebAuthn authentication
// @Tags auth
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]string
// @Router /api/auth/webauthn/discoverable/begin [post]
func (h *Handler) BeginDiscoverableLogin(c echo.Context) error {
	assertion, sessionData, err := h.authUC.BeginDiscoverableLogin(c.Request().Context())
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": err.Error(),
		})
	}

	// Store session data
	sessions[sessionData.SessionID] = sessionData

	// Return assertion options and session ID
	response := map[string]interface{}{
		"publicKey": assertion,
		"sessionId": sessionData.SessionID,
	}

	return c.JSON(http.StatusOK, response)
}

// FinishDiscoverableLogin completes usernameless WebAuthn authentication
// @Summary Complete discoverable login
// @Description Completes usernameless WebAuthn authentication
// @Tags auth
// @Accept json
// @Produce json
// @Success 200 {object} model.LoginResponse
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/auth/webauthn/discoverable/finish [post]
func (h *Handler) FinishDiscoverableLogin(c echo.Context) error {
	// Get session data
	sessionID := c.Request().Header.Get("X-Session-ID")
	if sessionID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Session ID required in X-Session-ID header",
		})
	}

	sessionDataInterface, exists := sessions[sessionID]
	if !exists {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid or expired session",
		})
	}

	sessionData, ok := sessionDataInterface.(*webauthn.SessionData)
	if !ok {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": "Invalid session data",
		})
	}

	// Finish discoverable login
	loginResponse, err := h.authUC.FinishDiscoverableLogin(c.Request().Context(), sessionData, c.Request())
	if err != nil {
		return echo.NewHTTPError(http.StatusUnauthorized, map[string]string{
			"error": err.Error(),
		})
	}

	// Clean up session
	delete(sessions, sessionID)

	return c.JSON(http.StatusOK, loginResponse)
}

// ValidateToken validates a JWT token
// @Summary Validate JWT token
// @Description Validates a JWT token and returns user information
// @Tags auth
// @Accept json
// @Produce json
// @Param request body map[string]string true "Token validation request"
// @Success 200 {object} model.User
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Router /api/auth/token/validate [post]
func (h *Handler) ValidateToken(c echo.Context) error {
	var req map[string]string
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid request body",
		})
	}

	token := req["token"]
	if token == "" {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Token is required",
		})
	}

	user, err := h.authUC.ValidateToken(c.Request().Context(), token)
	if err != nil {
		return echo.NewHTTPError(http.StatusUnauthorized, map[string]string{
			"error": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, user)
}

// RefreshToken refreshes a JWT token
// @Summary Refresh JWT token
// @Description Refreshes a JWT token with a new expiry time
// @Tags auth
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer refresh_token"
// @Success 200 {object} map[string]string
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Router /api/auth/token/refresh [post]
func (h *Handler) RefreshToken(c echo.Context) error {
	// Get Authorization header
	authHeader := c.Request().Header.Get("Authorization")
	if authHeader == "" {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Authorization header is required",
		})
	}

	// Extract token from Bearer header
	token, err := auth.ExtractTokenFromBearer(authHeader)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid authorization header format",
		})
	}

	newToken, err := h.authUC.RefreshToken(c.Request().Context(), token)
	if err != nil {
		return echo.NewHTTPError(http.StatusUnauthorized, map[string]string{
			"error": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]string{
		"accessToken": newToken,
	})
}
