package http

import (
	"github.com/Lionparcel/pentools/internal/usecase/auth"
	"github.com/Lionparcel/pentools/pkg/middleware"
	"github.com/labstack/echo/v4"
)

// <PERSON><PERSON> holds the auth use case and handles HTTP requests
type Handler struct {
	authUC *auth.UseCase
}

// New creates a new authentication HTTP handler and sets up routes
func New(e *echo.Echo, authUC *auth.UseCase) *Handler {
	handler := &Handler{
		authUC: authUC,
	}
	
	// JWT middleware for protected routes
	jwtAuth := middleware.JWTAuthMiddleware(authUC.GetJWTManager())
	adminOnly := middleware.RequireAdminMiddleware()
	
	// Password authentication routes (public)
	auth := e.Group("/api/auth")
	auth.POST("/login", handler.PasswordLogin)
	auth.POST("/register", handler.Register)
	auth.POST("/check-approval", handler.CheckApprovalStatus)
	
	// WebAuthn routes
	webauthn := auth.Group("/webauthn")
	
	// Public WebAuthn routes (no authentication required)
	webauthn.POST("/register/begin", handler.BeginRegistration)
	webauthn.POST("/register/finish", handler.FinishRegistration)
	webauthn.POST("/login/begin", handler.BeginLogin)
	webauthn.POST("/login/finish", handler.FinishLogin)
	webauthn.POST("/discoverable/begin", handler.BeginDiscoverableLogin)
	webauthn.POST("/discoverable/finish", handler.FinishDiscoverableLogin)
	
	// Authenticated WebAuthn management routes
	webauthnAuth := webauthn.Group("", jwtAuth)
	webauthnAuth.POST("/enable", handler.EnableWebAuthn)
	webauthnAuth.POST("/disable", handler.DisableWebAuthn)
	
	// Token management routes
	token := e.Group("/api/auth/token")
	token.POST("/validate", handler.ValidateToken)
	token.POST("/refresh", handler.RefreshToken)
	
	// Authenticated password management routes
	authRoutes := e.Group("/api/auth", jwtAuth)
	authRoutes.POST("/change-password", handler.ChangePassword)
	
	// User profile routes (authenticated users)
	profile := e.Group("/api/auth/profile", jwtAuth)
	profile.GET("", handler.GetCurrentUserProfile)
	profile.PUT("", handler.UpdateCurrentUserProfile)
	
	// Device management routes (authenticated users)
	devices := e.Group("/api/auth/devices", jwtAuth)
	devices.GET("", handler.GetUserDevices)
	devices.POST("/register", handler.BeginDeviceRegistration)
	devices.POST("/register/finish", handler.FinishDeviceRegistration)
	devices.DELETE("/:deviceId", handler.DeleteUserDevice)
	
	// Admin-only user management routes
	users := e.Group("/api/users", jwtAuth, adminOnly)
	users.GET("", handler.GetUsers)
	users.POST("", handler.CreateUser)
	users.GET("/:userId", handler.GetUser)
	users.PUT("/:userId", handler.UpdateUser)
	users.DELETE("/:userId", handler.DeleteUser)
	users.PUT("/:userId/role", handler.UpdateUserRole)
	users.POST("/:userId/change-password", handler.AdminChangeUserPassword)
	users.GET("/:userId/devices", handler.GetUserDevicesAdmin)
	users.DELETE("/:userId/devices/:deviceId", handler.DeleteUserDeviceAdmin)
	
	// Admin statistics routes
	stats := e.Group("/api/admin/stats", jwtAuth, adminOnly)
	stats.GET("/credentials", handler.GetCredentialStats)

	// Admin user approval routes
	admin := e.Group("/api/admin", jwtAuth, adminOnly)
	admin.GET("/users/pending", handler.GetPendingUsers)
	admin.POST("/users/:userId/approve", handler.ApproveUser)
	admin.POST("/users/:userId/reject", handler.RejectUser)
	
	return handler
}