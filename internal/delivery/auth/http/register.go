package http

import (
	"net/http"

	"github.com/Lionparcel/pentools/internal/model"
	"github.com/labstack/echo/v4"
)

// Register handles user registration
// @Summary Register new user
// @Description Register a new user (requires admin approval)
// @Tags auth
// @Accept json
// @Produce json
// @Param request body model.UserRegistrationRequest true "Registration request"
// @Success 201 {object} map[string]string
// @Failure 400 {object} map[string]string
// @Failure 409 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/auth/register [post]
func (h *Handler) Register(c echo.Context) error {
	var req model.UserRegistrationRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if req.Username == "" || req.Password == "" || req.DisplayName == "" {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Username, password, and display name are required",
		})
	}

	if len(req.Password) < 6 {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Password must be at least 6 characters long",
		})
	}

	// Register user
	user, err := h.authUC.RegisterUser(c.Request().Context(), req)
	if err != nil {
		if err.Error() == "username already exists" {
			return echo.NewHTTPError(http.StatusConflict, map[string]string{
				"error": err.Error(),
			})
		}
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": err.Error(),
		})
	}

	return c.JSON(http.StatusCreated, map[string]interface{}{
		"message": "Registration successful. Please wait for admin approval.",
		"user": map[string]interface{}{
			"id":           user.ID,
			"username":     user.Username,
			"display_name": user.DisplayName,
			"is_approved":  user.IsApproved,
		},
	})
}

// CheckApprovalStatus checks if user is approved by admin
// @Summary Check user approval status
// @Description Check if user is approved for login
// @Tags auth
// @Accept json
// @Produce json
// @Param request body map[string]string true "Username"
// @Success 200 {object} map[string]bool
// @Failure 400 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/auth/check-approval [post]
func (h *Handler) CheckApprovalStatus(c echo.Context) error {
	var req struct {
		Username string `json:"username"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid request body",
		})
	}

	if req.Username == "" {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Username is required",
		})
	}

	// Get user by username to get ID
	user, err := h.authUC.GetUserByUsername(c.Request().Context(), req.Username)
	if err != nil {
		return echo.NewHTTPError(http.StatusNotFound, map[string]string{
			"error": "User not found",
		})
	}

	// Check approval status
	isApproved, err := h.authUC.CheckUserApprovalStatus(c.Request().Context(), user.ID)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": "Failed to check approval status",
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"is_approved": isApproved,
		"is_rejected": !user.IsActive && !isApproved, // User is rejected if not active and not approved
		"username":    req.Username,
	})
}