package http

import (
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"
)

// GetPendingUsers returns users pending approval (admin only)
// @Summary Get pending users
// @Description Get list of users pending admin approval
// @Tags admin
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {array} model.User
// @Failure 401 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/admin/users/pending [get]
func (h *Handler) GetPendingUsers(c echo.Context) error {
	users, err := h.authUC.GetPendingUsers(c.Request().Context())
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": "Failed to get pending users",
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"users": users,
		"count": len(users),
	})
}

// ApproveUser approves a user for login (admin only)
// @Summary Approve user
// @Description Approve a user for login access
// @Tags admin
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param userId path int true "User ID"
// @Success 200 {object} map[string]string
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/admin/users/{userId}/approve [post]
func (h *Handler) ApproveUser(c echo.Context) error {
	userIDParam := c.Param("userId")
	userID, err := strconv.ParseUint(userIDParam, 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid user ID",
		})
	}

	if err := h.authUC.ApproveUser(c.Request().Context(), uint(userID)); err != nil {
		if err.Error() == "user not found" {
			return echo.NewHTTPError(http.StatusNotFound, map[string]string{
				"error": err.Error(),
			})
		}
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": "Failed to approve user",
		})
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "User approved successfully",
	})
}

// RejectUser rejects a user registration (admin only)
// @Summary Reject user
// @Description Reject a user registration
// @Tags admin
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param userId path int true "User ID"
// @Success 200 {object} map[string]string
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/admin/users/{userId}/reject [post]
func (h *Handler) RejectUser(c echo.Context) error {
	userIDParam := c.Param("userId")
	userID, err := strconv.ParseUint(userIDParam, 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Invalid user ID",
		})
	}

	if err := h.authUC.RejectUser(c.Request().Context(), uint(userID)); err != nil {
		if err.Error() == "user not found" {
			return echo.NewHTTPError(http.StatusNotFound, map[string]string{
				"error": err.Error(),
			})
		}
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": "Failed to reject user",
		})
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "User rejected successfully",
	})
}