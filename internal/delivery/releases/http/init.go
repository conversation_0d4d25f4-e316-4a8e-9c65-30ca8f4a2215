package http

import (
	"context"
	"net/http"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/pkg/config"
	"github.com/labstack/echo/v4"
)

type Usecase interface {
	Create(ctx context.Context, req dto.CreateReleaseRequest) (*dto.CreateReleaseResponse, error)
	Get(ctx context.Context, req dto.GetReleaseRequest) (*dto.ReleaseDoc, error)
	Update(ctx context.Context, req dto.UpdateReleaseRequest) error
	Delete(ctx context.Context, req dto.DeleteReleaseRequest) (*dto.DeleteReleaseResponse, error)
	List(ctx context.Context, req dto.ListReleasesRequest) ([]dto.ReleaseDoc, error)
}

type ReleasesHTTPHandler struct{ usecase Usecase }

func New(_ *config.Config, e *echo.Echo, uc Usecase) {
	h := &ReleasesHTTPHandler{usecase: uc}
	e.GET("/releases", h.List)
	e.GET("/releases/:id", h.Get)
	e.POST("/releases", h.Create)
	e.PUT("/releases/:id", h.Update)
	e.DELETE("/releases/:id", h.Delete)
}

// List godoc
// @Summary      List releases
// @Tags         releases
// @Accept       json
// @Produce      json
// @Param        q query dto.ListReleasesRequest false "query params"
// @Router       /releases [get]
// @Success      200  {array}  dto.ReleaseDoc
func (h *ReleasesHTTPHandler) List(c echo.Context) error {
	var q dto.ListReleasesRequest
	if err := c.Bind(&q); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	res, err := h.usecase.List(c.Request().Context(), q)
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}
	return c.JSON(http.StatusOK, res)
}

// Get godoc
// @Summary      Get release by ID
// @Tags         releases
// @Accept       json
// @Produce      json
// @Param        id path string true "document id"
// @Router       /releases/{id} [get]
// @Success      200  {object}  dto.ReleaseDoc
func (h *ReleasesHTTPHandler) Get(c echo.Context) error {
	req := dto.GetReleaseRequest{ID: c.Param("id")}
	res, err := h.usecase.Get(c.Request().Context(), req)
	if err != nil {
		// surface 404 when not found
		if err.Error() == "not found" { // mapped sentinel from usecase layer
			return c.NoContent(http.StatusNotFound)
		}
		return c.String(http.StatusInternalServerError, err.Error())
	}
	return c.JSON(http.StatusOK, res)
}

// Create godoc
// @Summary      Create release document
// @Tags         releases
// @Accept       json
// @Produce      json
// @Param        body body dto.CreateReleaseRequest true "release payload"
// @Router       /releases [post]
// @Success      201  {object}  dto.CreateReleaseResponse
func (h *ReleasesHTTPHandler) Create(c echo.Context) error {
	var req dto.CreateReleaseRequest
	if err := c.Bind(&req); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	res, err := h.usecase.Create(c.Request().Context(), req)
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}
	return c.JSON(http.StatusCreated, res)
}

// Update godoc
// @Summary      Update release document
// @Tags         releases
// @Accept       json
// @Produce      json
// @Param        id path string true "document id"
// @Param        body body dto.UpdateReleaseRequest true "release payload"
// @Router       /releases/{id} [put]
// @Success      204  {string}  string ""
func (h *ReleasesHTTPHandler) Update(c echo.Context) error {
	var req dto.UpdateReleaseRequest
	if err := c.Bind(&req); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	req.ID = c.Param("id")
	if err := h.usecase.Update(c.Request().Context(), req); err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}
	return c.NoContent(http.StatusNoContent)
}

// Delete godoc
// @Summary      Delete release document
// @Tags         releases
// @Accept       json
// @Produce      json
// @Param        id path string true "document id"
// @Router       /releases/{id} [delete]
// @Success      200  {object}  dto.DeleteReleaseResponse
func (h *ReleasesHTTPHandler) Delete(c echo.Context) error {
	req := dto.DeleteReleaseRequest{ID: c.Param("id")}
	res, err := h.usecase.Delete(c.Request().Context(), req)
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}
	return c.JSON(http.StatusOK, res)
}
