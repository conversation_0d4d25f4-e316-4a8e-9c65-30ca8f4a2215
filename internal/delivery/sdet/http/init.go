package http

import (
	"context"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/pkg/config"
	"github.com/labstack/echo/v4"
)

type Usecase interface {
	GetReport(ctx context.Context, startDate, endDate string) (dto.GetSDETReportResponse, error)
}
type SDETHTTPHandler struct {
	usecase Usecase
}

func New(_ *config.Config, e *echo.Echo, usecase Usecase) {
	h := &SDETHTTPHandler{
		usecase: usecase,
	}
	e.GET("/api/sdet/report", h.GetReport)
}
