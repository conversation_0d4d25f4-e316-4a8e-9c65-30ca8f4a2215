package http

import (
	"net/http"
	"time"

	"github.com/Lionparcel/pentools/internal/dto"
	validation "github.com/go-ozzo/ozzo-validation/v4"
	"github.com/labstack/echo/v4"
)

// GetReport godoc
// @Summary      Get report from sdet Database.
// @Description  Get Report from sdet Database.
// @Tags         sdet
// @Accept       json
// @Produce      json
// @Param        q query dto.GetSDETReportRequest false "filter query"
// @Router       /api/sdet/report [get]
// @Success      200  {object}  dto.GetSDETReportResponse
func (h *SDETHTTPHandler) GetReport(c echo.Context) error {
	var query dto.GetSDETReportRequest
	if err := c.Bind(&query); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	if err := validation.ValidateStruct(&query,
		validation.Field(&query.StartDate, validation.When(query.StartDate != "", validation.Date(time.DateOnly))),
		validation.Field(&query.EndDate, validation.When(query.EndDate != "", validation.Date(time.DateOnly))),
	); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	resp, err := h.usecase.GetReport(c.Request().Context(), query.StartDate, query.EndDate)
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, resp)
}
