package http

import (
	"net/http"
	"time"

	"github.com/Lionparcel/pentools/internal/dto"
	validation "github.com/go-ozzo/ozzo-validation/v4"
	"github.com/labstack/echo/v4"
)

// GetReport godoc
// @Summary      Get report for SA Team.
// @Description  Get report for SA Team.
// @Tags         sa
// @Accept       json
// @Produce      json
// @Param        q query dto.GetSAReportRequest false "filter query"
// @Router       /api/sa/report [get]
// @Success      200  {object}  dto.GetSAReportResponse
func (h *SAHTTPHandler) GetReport(c echo.Context) error {
	var query dto.GetSAReportRequest
	if err := c.Bind(&query); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	if err := validation.ValidateStruct(&query,
		validation.Field(&query.StartDate, validation.When(query.StartDate != "", validation.Date(time.DateOnly))),
		validation.Field(&query.EndDate, validation.When(query.EndDate != "", validation.Date(time.DateOnly))),
	); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	resp, err := h.usecase.GetReport(c.Request().Context(), query)
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, resp)
}
