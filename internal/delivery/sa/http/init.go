package http

import (
	"context"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/pkg/config"
	"github.com/labstack/echo/v4"
)

type Usecase interface {
	GetReport(ctx context.Context, req dto.GetSAReportRequest) (*dto.GetSAReportResponse, error)
}
type SAHTTPHandler struct {
	usecase Usecase
}

func New(_ *config.Config, e *echo.Echo, usecase Usecase) {
	h := &SAHTTPHandler{
		usecase: usecase,
	}
	e.GET("/api/sa/report", h.GetReport)
}
