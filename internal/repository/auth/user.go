package auth

import (
	"context"
	"fmt"
	"time"

	"github.com/Lionparcel/pentools/internal/model"
	"go.opentelemetry.io/otel/attribute"
	"gorm.io/gorm"
)

// GetUserByUsername retrieves a user by username
func (r *Repository) GetUserByUsername(ctx context.Context, username string) (*model.User, error) {
	ctx, span := r.tracer.Start(ctx, "repository.GetUserByUsername")
	defer span.End()
	
	span.SetAttributes(attribute.String("username", username))
	
	var user model.User
	err := r.GetSlaveDB(ctx).
		Where("username = ? AND is_active = ?", username, true).
		Preload("Credentials").
		First(&user).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			span.SetAttributes(attribute.Bool("user_found", false))
			return nil, fmt.Errorf("user not found: %w", err)
		}
		return nil, fmt.Errorf("failed to get user by username: %w", err)
	}
	
	span.SetAttributes(
		attribute.Bool("user_found", true),
		attribute.Int("user_id", int(user.ID)),
		attribute.String("user_role", string(user.Role)),
	)
	
	return &user, nil
}

// GetUserByID retrieves a user by ID
func (r *Repository) GetUserByID(ctx context.Context, id uint) (*model.User, error) {
	ctx, span := r.tracer.Start(ctx, "repository.GetUserByID")
	defer span.End()
	
	span.SetAttributes(attribute.Int("user_id", int(id)))
	
	var user model.User
	err := r.GetSlaveDB(ctx).
		Where("id = ? AND is_active = ?", id, true).
		Preload("Credentials").
		First(&user).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			span.SetAttributes(attribute.Bool("user_found", false))
			return nil, fmt.Errorf("user not found: %w", err)
		}
		return nil, fmt.Errorf("failed to get user by ID: %w", err)
	}
	
	span.SetAttributes(
		attribute.Bool("user_found", true),
		attribute.String("username", user.Username),
		attribute.String("user_role", string(user.Role)),
	)
	
	return &user, nil
}

// CreateUser creates a new user
func (r *Repository) CreateUser(ctx context.Context, user *model.User) error {
	ctx, span := r.tracer.Start(ctx, "repository.CreateUser")
	defer span.End()
	
	span.SetAttributes(
		attribute.String("username", user.Username),
		attribute.String("display_name", user.DisplayName),
		attribute.String("role", string(user.Role)),
	)
	
	err := r.GetDB(ctx).Create(user).Error
	if err != nil {
		return fmt.Errorf("failed to create user: %w", err)
	}
	
	span.SetAttributes(attribute.Int("created_user_id", int(user.ID)))
	
	return nil
}

// UpdateUser updates an existing user
func (r *Repository) UpdateUser(ctx context.Context, id uint, updates map[string]interface{}) error {
	ctx, span := r.tracer.Start(ctx, "repository.UpdateUser")
	defer span.End()
	
	span.SetAttributes(attribute.Int("user_id", int(id)))
	
	result := r.GetDB(ctx).
		Model(&model.User{}).
		Where("id = ?", id).
		Updates(updates)
	
	if result.Error != nil {
		return fmt.Errorf("failed to update user: %w", result.Error)
	}
	
	if result.RowsAffected == 0 {
		return fmt.Errorf("user not found or no changes made")
	}
	
	span.SetAttributes(attribute.Int64("rows_affected", result.RowsAffected))
	
	return nil
}

// DeleteUser soft deletes a user by setting is_active to false
func (r *Repository) DeleteUser(ctx context.Context, id uint) error {
	ctx, span := r.tracer.Start(ctx, "repository.DeleteUser")
	defer span.End()
	
	span.SetAttributes(attribute.Int("user_id", int(id)))
	
	result := r.GetDB(ctx).
		Model(&model.User{}).
		Where("id = ?", id).
		Update("is_active", false)
	
	if result.Error != nil {
		return fmt.Errorf("failed to delete user: %w", result.Error)
	}
	
	if result.RowsAffected == 0 {
		return fmt.Errorf("user not found")
	}
	
	span.SetAttributes(attribute.Int64("rows_affected", result.RowsAffected))
	
	return nil
}

// GetUsers retrieves a paginated list of users
func (r *Repository) GetUsers(ctx context.Context, offset, limit int) ([]model.User, int64, error) {
	ctx, span := r.tracer.Start(ctx, "repository.GetUsers")
	defer span.End()
	
	span.SetAttributes(
		attribute.Int("offset", offset),
		attribute.Int("limit", limit),
	)
	
	var users []model.User
	var total int64
	
	// Get total count
	err := r.GetSlaveDB(ctx).
		Model(&model.User{}).
		Where("is_active = ?", true).
		Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count users: %w", err)
	}
	
	// Get users with pagination
	err = r.GetSlaveDB(ctx).
		Where("is_active = ?", true).
		Preload("Credentials").
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&users).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get users: %w", err)
	}
	
	span.SetAttributes(
		attribute.Int64("total_users", total),
		attribute.Int("returned_users", len(users)),
	)
	
	return users, total, nil
}

// UpdateUserRole updates a user's role
func (r *Repository) UpdateUserRole(ctx context.Context, id uint, role model.UserRole) error {
	ctx, span := r.tracer.Start(ctx, "repository.UpdateUserRole")
	defer span.End()
	
	span.SetAttributes(
		attribute.Int("user_id", int(id)),
		attribute.String("new_role", string(role)),
	)
	
	result := r.GetDB(ctx).
		Model(&model.User{}).
		Where("id = ?", id).
		Update("role", role)
	
	if result.Error != nil {
		return fmt.Errorf("failed to update user role: %w", result.Error)
	}
	
	if result.RowsAffected == 0 {
		return fmt.Errorf("user not found")
	}
	
	span.SetAttributes(attribute.Int64("rows_affected", result.RowsAffected))
	
	return nil
}

// GetUserWithCredentials retrieves a user with all their credentials
func (r *Repository) GetUserWithCredentials(ctx context.Context, username string) (*model.User, error) {
	ctx, span := r.tracer.Start(ctx, "repository.GetUserWithCredentials")
	defer span.End()
	
	span.SetAttributes(attribute.String("username", username))
	
	var user model.User
	err := r.GetSlaveDB(ctx).
		Where("username = ? AND is_active = ?", username, true).
		Preload("Credentials", func(db *gorm.DB) *gorm.DB {
			return db.Order("created_at DESC")
		}).
		First(&user).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			span.SetAttributes(attribute.Bool("user_found", false))
			return nil, fmt.Errorf("user not found: %w", err)
		}
		return nil, fmt.Errorf("failed to get user with credentials: %w", err)
	}
	
	span.SetAttributes(
		attribute.Bool("user_found", true),
		attribute.Int("user_id", int(user.ID)),
		attribute.Int("credentials_count", len(user.Credentials)),
	)
	
	return &user, nil
}

// CheckUsernameExists checks if a username already exists
func (r *Repository) CheckUsernameExists(ctx context.Context, username string) (bool, error) {
	ctx, span := r.tracer.Start(ctx, "repository.CheckUsernameExists")
	defer span.End()
	
	span.SetAttributes(attribute.String("username", username))
	
	var count int64
	err := r.GetSlaveDB(ctx).
		Model(&model.User{}).
		Where("username = ?", username).
		Count(&count).Error
	
	if err != nil {
		return false, fmt.Errorf("failed to check username exists: %w", err)
	}
	
	exists := count > 0
	span.SetAttributes(attribute.Bool("username_exists", exists))
	
	return exists, nil
}

// GetUserByIDWithTx retrieves a user by ID within a transaction
func (r *Repository) GetUserByIDWithTx(ctx context.Context, tx *gorm.DB, id uint) (*model.User, error) {
	ctx, span := r.tracer.Start(ctx, "repository.GetUserByIDWithTx")
	defer span.End()
	
	span.SetAttributes(attribute.Int("user_id", int(id)))
	
	var user model.User
	err := tx.WithContext(ctx).
		Where("id = ?", id).
		First(&user).Error
	
	if err != nil {
		return nil, fmt.Errorf("failed to get user by ID with transaction: %w", err)
	}
	
	return &user, nil
}

// UpdateUserSecurityFieldsWithTx updates user security fields within a transaction
func (r *Repository) UpdateUserSecurityFieldsWithTx(ctx context.Context, tx *gorm.DB, userID uint, failedAttempts int, lockedUntil *time.Time) error {
	ctx, span := r.tracer.Start(ctx, "repository.UpdateUserSecurityFieldsWithTx")
	defer span.End()
	
	span.SetAttributes(
		attribute.Int("user_id", int(userID)),
		attribute.Int("failed_attempts", failedAttempts),
	)
	
	updates := map[string]interface{}{
		"failed_login_attempts": failedAttempts,
		"locked_until":         lockedUntil,
	}
	
	result := tx.WithContext(ctx).
		Model(&model.User{}).
		Where("id = ?", userID).
		Updates(updates)
	
	if result.Error != nil {
		return fmt.Errorf("failed to update user security fields: %w", result.Error)
	}
	
	return nil
}

// UpdateUserLastLoginWithTx updates user's last login time within a transaction
func (r *Repository) UpdateUserLastLoginWithTx(ctx context.Context, tx *gorm.DB, userID uint, lastLoginAt *time.Time) error {
	ctx, span := r.tracer.Start(ctx, "repository.UpdateUserLastLoginWithTx")
	defer span.End()
	
	span.SetAttributes(attribute.Int("user_id", int(userID)))
	
	result := tx.WithContext(ctx).
		Model(&model.User{}).
		Where("id = ?", userID).
		Update("last_login_at", lastLoginAt)
	
	if result.Error != nil {
		return fmt.Errorf("failed to update user last login: %w", result.Error)
	}
	
	return nil
}

// UpdateUserPassword updates a user's password hash
func (r *Repository) UpdateUserPassword(ctx context.Context, userID uint, passwordHash string) error {
	ctx, span := r.tracer.Start(ctx, "repository.UpdateUserPassword")
	defer span.End()
	
	span.SetAttributes(attribute.Int("user_id", int(userID)))
	
	result := r.GetDB(ctx).
		Model(&model.User{}).
		Where("id = ?", userID).
		Update("password_hash", passwordHash)
	
	if result.Error != nil {
		return fmt.Errorf("failed to update user password: %w", result.Error)
	}
	
	if result.RowsAffected == 0 {
		return fmt.Errorf("user not found")
	}
	
	return nil
}

// UpdateUserWebAuthnStatus updates a user's WebAuthn enabled status
func (r *Repository) UpdateUserWebAuthnStatus(ctx context.Context, userID uint, enabled bool) error {
	ctx, span := r.tracer.Start(ctx, "repository.UpdateUserWebAuthnStatus")
	defer span.End()
	
	span.SetAttributes(
		attribute.Int("user_id", int(userID)),
		attribute.Bool("webauthn_enabled", enabled),
	)
	
	result := r.GetDB(ctx).
		Model(&model.User{}).
		Where("id = ?", userID).
		Update("webauthn_enabled", enabled)
	
	if result.Error != nil {
		return fmt.Errorf("failed to update user WebAuthn status: %w", result.Error)
	}
	
	if result.RowsAffected == 0 {
		return fmt.Errorf("user not found")
	}
	
	return nil
}

// UpdateUserWebAuthnStatusWithTx updates a user's WebAuthn enabled status within a transaction
func (r *Repository) UpdateUserWebAuthnStatusWithTx(ctx context.Context, tx *gorm.DB, userID uint, enabled bool) error {
	ctx, span := r.tracer.Start(ctx, "repository.UpdateUserWebAuthnStatusWithTx")
	defer span.End()
	
	span.SetAttributes(
		attribute.Int("user_id", int(userID)),
		attribute.Bool("webauthn_enabled", enabled),
	)
	
	result := tx.WithContext(ctx).
		Model(&model.User{}).
		Where("id = ?", userID).
		Update("webauthn_enabled", enabled)
	
	if result.Error != nil {
		return fmt.Errorf("failed to update user WebAuthn status: %w", result.Error)
	}
	
	return nil
}

// GetUsersByApprovalStatus retrieves users by their approval status
func (r *Repository) GetUsersByApprovalStatus(ctx context.Context, isApproved bool) ([]model.User, error) {
	ctx, span := r.tracer.Start(ctx, "repository.GetUsersByApprovalStatus")
	defer span.End()
	
	span.SetAttributes(attribute.Bool("is_approved", isApproved))
	
	var users []model.User
	err := r.GetSlaveDB(ctx).
		Where("is_approved = ? AND is_active = ?", isApproved, true).
		Order("created_at DESC").
		Find(&users).Error
	
	if err != nil {
		return nil, fmt.Errorf("failed to get users by approval status: %w", err)
	}
	
	span.SetAttributes(attribute.Int("users_count", len(users)))
	
	return users, nil
}