package auth

import (
	"context"

	"github.com/Lionparcel/pentools/pkg/database/postgresql"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
)

// Repository handles authentication-related database operations
type Repository struct {
	db     *postgresql.Connections
	tracer trace.Tracer
}

// New creates a new authentication repository
func New(db *postgresql.Connections) *Repository {
	return &Repository{
		db:     db,
		tracer: otel.Tracer("auth-repository"),
	}
}

// GetDB returns the database connection for the given context
func (r *Repository) GetDB(ctx context.Context) *gorm.DB {
	// Use master connection for write operations by default
	// Use slave connection for read operations when explicitly requested
	return r.db.Master(ctx)
}

// GetSlaveDB returns the slave database connection for read operations
func (r *Repository) GetSlaveDB(ctx context.Context) *gorm.DB {
	return r.db.Slave
}

// WithTx executes a function within a database transaction
func (r *Repository) WithTx(ctx context.Context, fn func(tx *gorm.DB) error) error {
	return r.db.Transaction(ctx, func(ctx context.Context) error {
		tx := r.GetDB(ctx)
		return fn(tx)
	})
}
