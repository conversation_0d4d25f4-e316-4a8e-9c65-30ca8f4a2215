package auth

import (
	"context"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/Lionparcel/pentools/internal/model"
	"go.opentelemetry.io/otel/attribute"
	"gorm.io/gorm"
)

// CreateCredential creates a new WebAuthn credential
func (r *Repository) CreateCredential(ctx context.Context, credential *model.WebAuthnCredential) error {
	ctx, span := r.tracer.Start(ctx, "repository.CreateCredential")
	defer span.End()

	// Set the hex representation of credential ID
	if len(credential.CredentialID) > 0 {
		credential.CredentialIDHex = hex.EncodeToString(credential.CredentialID)
	}

	span.SetAttributes(
		attribute.Int("user_id", int(credential.UserID)),
		attribute.String("device_name", credential.DeviceName),
		attribute.String("device_type", string(credential.DeviceType)),
	)

	err := r.GetDB(ctx).Create(credential).Error
	if err != nil {
		return fmt.Errorf("failed to create credential: %w", err)
	}

	span.SetAttributes(attribute.Int("credential_id", int(credential.ID)))

	return nil
}

// GetCredentialsByUserID retrieves all credentials for a user
func (r *Repository) GetCredentialsByUserID(ctx context.Context, userID uint) ([]model.WebAuthnCredential, error) {
	ctx, span := r.tracer.Start(ctx, "repository.GetCredentialsByUserID")
	defer span.End()

	span.SetAttributes(attribute.Int("user_id", int(userID)))

	var credentials []model.WebAuthnCredential
	err := r.GetSlaveDB(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Find(&credentials).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get credentials by user ID: %w", err)
	}

	span.SetAttributes(attribute.Int("credentials_count", len(credentials)))

	return credentials, nil
}

// GetCredentialByCredentialID retrieves a credential by its credential ID
func (r *Repository) GetCredentialByCredentialID(ctx context.Context, credentialID []byte) (*model.WebAuthnCredential, error) {
	ctx, span := r.tracer.Start(ctx, "repository.GetCredentialByCredentialID")
	defer span.End()

	credentialIDHex := hex.EncodeToString(credentialID)
	span.SetAttributes(attribute.String("credential_id_hex", credentialIDHex))

	var credential model.WebAuthnCredential
	err := r.GetSlaveDB(ctx).
		Where("credential_id = ? OR credential_id_hex = ?", credentialID, credentialIDHex).
		First(&credential).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			span.SetAttributes(attribute.Bool("credential_found", false))
			return nil, fmt.Errorf("credential not found: %w", err)
		}
		return nil, fmt.Errorf("failed to get credential by credential ID: %w", err)
	}

	span.SetAttributes(
		attribute.Bool("credential_found", true),
		attribute.Int("credential_db_id", int(credential.ID)),
		attribute.Int("user_id", int(credential.UserID)),
	)

	return &credential, nil
}

// GetCredentialByCredentialIDHex retrieves a credential by its hex-encoded credential ID
func (r *Repository) GetCredentialByCredentialIDHex(ctx context.Context, credentialIDHex string) (*model.WebAuthnCredential, error) {
	ctx, span := r.tracer.Start(ctx, "repository.GetCredentialByCredentialIDHex")
	defer span.End()

	span.SetAttributes(attribute.String("credential_id_hex", credentialIDHex))

	var credential model.WebAuthnCredential
	err := r.GetSlaveDB(ctx).
		Where("credential_id_hex = ?", credentialIDHex).
		First(&credential).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			span.SetAttributes(attribute.Bool("credential_found", false))
			return nil, fmt.Errorf("credential not found: %w", err)
		}
		return nil, fmt.Errorf("failed to get credential by credential ID hex: %w", err)
	}

	span.SetAttributes(
		attribute.Bool("credential_found", true),
		attribute.Int("credential_db_id", int(credential.ID)),
		attribute.Int("user_id", int(credential.UserID)),
	)

	return &credential, nil
}

// UpdateCredentialSignCount updates the sign count for a credential
func (r *Repository) UpdateCredentialSignCount(ctx context.Context, credentialID []byte, signCount uint32) error {
	ctx, span := r.tracer.Start(ctx, "repository.UpdateCredentialSignCount")
	defer span.End()

	credentialIDHex := hex.EncodeToString(credentialID)
	span.SetAttributes(
		attribute.String("credential_id_hex", credentialIDHex),
		attribute.Int("sign_count", int(signCount)),
	)

	result := r.GetDB(ctx).
		Model(&model.WebAuthnCredential{}).
		Where("credential_id = ? OR credential_id_hex = ?", credentialID, credentialIDHex).
		Update("sign_count", signCount)

	if result.Error != nil {
		return fmt.Errorf("failed to update credential sign count: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("credential not found")
	}

	span.SetAttributes(attribute.Int64("rows_affected", result.RowsAffected))

	return nil
}

// UpdateCredentialLastUsed updates the last used timestamp for a credential
func (r *Repository) UpdateCredentialLastUsed(ctx context.Context, credentialID []byte) error {
	ctx, span := r.tracer.Start(ctx, "repository.UpdateCredentialLastUsed")
	defer span.End()

	credentialIDHex := hex.EncodeToString(credentialID)
	now := time.Now()

	span.SetAttributes(
		attribute.String("credential_id_hex", credentialIDHex),
		attribute.String("last_used_at", now.Format(time.RFC3339)),
	)

	result := r.GetDB(ctx).
		Model(&model.WebAuthnCredential{}).
		Where("credential_id = ? OR credential_id_hex = ?", credentialID, credentialIDHex).
		Update("last_used_at", &now)

	if result.Error != nil {
		return fmt.Errorf("failed to update credential last used: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("credential not found")
	}

	span.SetAttributes(attribute.Int64("rows_affected", result.RowsAffected))

	return nil
}

// UpdateCredentialUsage updates both sign count and last used timestamp
func (r *Repository) UpdateCredentialUsage(ctx context.Context, credentialID []byte, signCount uint32) error {
	ctx, span := r.tracer.Start(ctx, "repository.UpdateCredentialUsage")
	defer span.End()

	credentialIDHex := hex.EncodeToString(credentialID)
	now := time.Now()

	span.SetAttributes(
		attribute.String("credential_id_hex", credentialIDHex),
		attribute.Int("sign_count", int(signCount)),
		attribute.String("last_used_at", now.Format(time.RFC3339)),
	)

	updates := map[string]interface{}{
		"sign_count":   signCount,
		"last_used_at": &now,
	}

	result := r.GetDB(ctx).
		Model(&model.WebAuthnCredential{}).
		Where("credential_id = ? OR credential_id_hex = ?", credentialID, credentialIDHex).
		Updates(updates)

	if result.Error != nil {
		return fmt.Errorf("failed to update credential usage: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("credential not found")
	}

	span.SetAttributes(attribute.Int64("rows_affected", result.RowsAffected))

	return nil
}

// UpdateCredentialUsageWithFlags updates sign count, last used timestamp, and backup flags
func (r *Repository) UpdateCredentialUsageWithFlags(ctx context.Context, credentialID []byte, signCount uint32, backupEligible, backupState bool) error {
	ctx, span := r.tracer.Start(ctx, "repository.UpdateCredentialUsageWithFlags")
	defer span.End()

	credentialIDHex := hex.EncodeToString(credentialID)
	now := time.Now()

	span.SetAttributes(
		attribute.String("credential_id_hex", credentialIDHex),
		attribute.Int("sign_count", int(signCount)),
		attribute.String("last_used_at", now.Format(time.RFC3339)),
		attribute.Bool("backup_eligible", backupEligible),
		attribute.Bool("backup_state", backupState),
	)

	updates := map[string]interface{}{
		"sign_count":      signCount,
		"last_used_at":    &now,
		"backup_eligible": backupEligible,
		"backup_state":    backupState,
	}

	result := r.GetDB(ctx).
		Model(&model.WebAuthnCredential{}).
		Where("credential_id = ? OR credential_id_hex = ?", credentialID, credentialIDHex).
		Updates(updates)

	if result.Error != nil {
		return fmt.Errorf("failed to update credential usage with flags: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("credential not found")
	}

	span.SetAttributes(attribute.Int64("rows_affected", result.RowsAffected))

	return nil
}

// DeleteCredential deletes a credential by its ID
func (r *Repository) DeleteCredential(ctx context.Context, id uint) error {
	ctx, span := r.tracer.Start(ctx, "repository.DeleteCredential")
	defer span.End()

	span.SetAttributes(attribute.Int("credential_db_id", int(id)))

	result := r.GetDB(ctx).
		Delete(&model.WebAuthnCredential{}, id)

	if result.Error != nil {
		return fmt.Errorf("failed to delete credential: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("credential not found")
	}

	span.SetAttributes(attribute.Int64("rows_affected", result.RowsAffected))

	return nil
}

// DeleteUserCredentials deletes all credentials for a user
func (r *Repository) DeleteUserCredentials(ctx context.Context, userID uint) error {
	ctx, span := r.tracer.Start(ctx, "repository.DeleteUserCredentials")
	defer span.End()

	span.SetAttributes(attribute.Int("user_id", int(userID)))

	result := r.GetDB(ctx).
		Where("user_id = ?", userID).
		Delete(&model.WebAuthnCredential{})

	if result.Error != nil {
		return fmt.Errorf("failed to delete user credentials: %w", result.Error)
	}

	span.SetAttributes(attribute.Int64("credentials_deleted", result.RowsAffected))

	return nil
}

// GetCredentialsByUser retrieves all credentials for a user by username
func (r *Repository) GetCredentialsByUser(ctx context.Context, username string) ([]model.WebAuthnCredential, error) {
	ctx, span := r.tracer.Start(ctx, "repository.GetCredentialsByUser")
	defer span.End()

	span.SetAttributes(attribute.String("username", username))

	var credentials []model.WebAuthnCredential
	err := r.GetSlaveDB(ctx).
		Joins("JOIN users ON users.id = webauthn_credentials.user_id").
		Where("users.username = ? AND users.is_active = ?", username, true).
		Find(&credentials).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get credentials by user: %w", err)
	}

	span.SetAttributes(attribute.Int("credentials_count", len(credentials)))

	return credentials, nil
}

// GetCredentialStats returns statistics about credentials
func (r *Repository) GetCredentialStats(ctx context.Context) (map[string]interface{}, error) {
	ctx, span := r.tracer.Start(ctx, "repository.GetCredentialStats")
	defer span.End()

	stats := make(map[string]interface{})

	// Total credentials
	var totalCredentials int64
	err := r.GetSlaveDB(ctx).
		Model(&model.WebAuthnCredential{}).
		Count(&totalCredentials).Error
	if err != nil {
		return nil, fmt.Errorf("failed to count total credentials: %w", err)
	}
	stats["total_credentials"] = totalCredentials

	// Credentials by device type
	var deviceTypeStats []struct {
		DeviceType string `json:"device_type"`
		Count      int64  `json:"count"`
	}
	err = r.GetSlaveDB(ctx).
		Model(&model.WebAuthnCredential{}).
		Select("device_type, COUNT(*) as count").
		Group("device_type").
		Find(&deviceTypeStats).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get device type stats: %w", err)
	}
	stats["by_device_type"] = deviceTypeStats

	// Recently used credentials (last 30 days)
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)
	var recentlyUsed int64
	err = r.GetSlaveDB(ctx).
		Model(&model.WebAuthnCredential{}).
		Where("last_used_at > ?", thirtyDaysAgo).
		Count(&recentlyUsed).Error
	if err != nil {
		return nil, fmt.Errorf("failed to count recently used credentials: %w", err)
	}
	stats["recently_used"] = recentlyUsed

	// Never used credentials
	var neverUsed int64
	err = r.GetSlaveDB(ctx).
		Model(&model.WebAuthnCredential{}).
		Where("last_used_at IS NULL").
		Count(&neverUsed).Error
	if err != nil {
		return nil, fmt.Errorf("failed to count never used credentials: %w", err)
	}
	stats["never_used"] = neverUsed

	span.SetAttributes(
		attribute.Int64("total_credentials", totalCredentials),
		attribute.Int64("recently_used", recentlyUsed),
		attribute.Int64("never_used", neverUsed),
	)

	return stats, nil
}

// CleanupOldCredentials removes credentials that haven't been used in a specified duration
func (r *Repository) CleanupOldCredentials(ctx context.Context, olderThan time.Duration) (int64, error) {
	ctx, span := r.tracer.Start(ctx, "repository.CleanupOldCredentials")
	defer span.End()

	cutoffTime := time.Now().Add(-olderThan)
	span.SetAttributes(attribute.String("cutoff_time", cutoffTime.Format(time.RFC3339)))

	result := r.GetDB(ctx).
		Where("last_used_at < ? OR (last_used_at IS NULL AND created_at < ?)", cutoffTime, cutoffTime).
		Delete(&model.WebAuthnCredential{})

	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup old credentials: %w", result.Error)
	}

	span.SetAttributes(attribute.Int64("credentials_cleaned", result.RowsAffected))

	return result.RowsAffected, nil
}

// DeleteAllUserCredentialsWithTx deletes all WebAuthn credentials for a user within a transaction
func (r *Repository) DeleteAllUserCredentialsWithTx(ctx context.Context, tx *gorm.DB, userID uint) error {
	ctx, span := r.tracer.Start(ctx, "repository.DeleteAllUserCredentialsWithTx")
	defer span.End()

	span.SetAttributes(attribute.Int("user_id", int(userID)))

	result := tx.WithContext(ctx).
		Where("user_id = ?", userID).
		Delete(&model.WebAuthnCredential{})

	if result.Error != nil {
		return fmt.Errorf("failed to delete all user credentials: %w", result.Error)
	}

	span.SetAttributes(attribute.Int64("deleted_credentials", result.RowsAffected))

	return nil
}
