package jira

import (
	"net/http"

	"github.com/Lionparcel/pentools/pkg/config"
	"go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"
)

type API struct {
	client *http.Client
	cfg    *config.Config
}

func NewAPI(cfg *config.Config) *API {
	return &API{
		client: &http.Client{
			Transport: otelhttp.NewTransport(http.DefaultTransport, otelhttp.WithPublicEndpoint()),
		},
		cfg: cfg,
	}
}
