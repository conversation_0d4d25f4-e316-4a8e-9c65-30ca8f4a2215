package jira

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"github.com/Lionparcel/pentools/internal/model"
)

func (a *API) GroupMember(ctx context.Context, req model.GroupMemberRequest) (*[]model.Author, error) {
	var (
		startAt    = req.StartAt
		allMembers []model.Author
		isLast     = false
	)

	for !isLast {
		jiraURL := "https://lionparcel.atlassian.net/rest/api/3/group/member?"
		queryParams := []string{
			"maxResults=50",
			"groupname=" + url.QueryEscape(req.Name),
			"includeInactiveUsers=" + strconv.FormatBool(req.IncludeInactiveUsers),
			"startAt=" + strconv.Itoa(startAt),
		}
		jiraURL += strings.Join(queryParams, "&")

		var res model.GroupMemberResponse
		if err := call(ctx, a.client, a.cfg.Jira.<PERSON>Key, jiraURL, &res); err != nil {
			return nil, err
		}

		allMembers = append(allMembers, res.Values...)
		isLast = res.IsLast
		startAt += len(res.Values) // move to next page
	}

	return &allMembers, nil
}

func (a *API) SearchIssue(ctx context.Context, req model.SearchIssueRequest) (*model.SearchIssueResponse, error) {
	res := &model.SearchIssueResponse{
		Issues: []model.Issue{},
		Params: req,
	}
	var errorSearchIssue error
	for {
		resSearchIssue, err := a.searchIssue(ctx, req)
		if err != nil {
			errorSearchIssue = err
			break
		}
		res.Issues = append(res.Issues, resSearchIssue.Issues...)
		if resSearchIssue.NextPageToken == nil {
			break
		}

		if *resSearchIssue.NextPageToken == "" {
			break
		}
		if req.Limit > 0 && len(res.Issues) >= int(req.Limit) {
			break
		}
		req.NextPageToken = *resSearchIssue.NextPageToken
	}
	if len(res.Issues) > 0 {
		return res, nil
	}
	return res, errorSearchIssue
}
func (a *API) searchIssue(ctx context.Context, req model.SearchIssueRequest) (*model.SearchIssueResponse, error) {
	jiraURL := "https://lionparcel.atlassian.net/rest/api/3/search/jql?"
	limit := 5000
	if req.Limit > 0 {
		limit = req.Limit
	}
	queryParams := []string{fmt.Sprintf("maxResults=%d", limit)}
	if req.JQL != "" {
		queryParams = append(queryParams, "jql="+url.QueryEscape(req.JQL))
	}
	if req.Fields != "" {
		queryParams = append(queryParams, "fields="+url.QueryEscape(req.Fields))
	}
	if req.NextPageToken != "" {
		queryParams = append(queryParams, "nextPageToken="+url.QueryEscape(req.NextPageToken))
	}
	if req.Expand != "" {
		queryParams = append(queryParams, "expand="+url.QueryEscape(req.Expand))
	}
	jiraURL += strings.Join(queryParams, "&")
	var res model.SearchIssueResponse
	if err := call(ctx, a.client, a.cfg.Jira.SecretKey, jiraURL, &res); err != nil {
		return nil, err
	}
	res.Params = req
	return &res, nil
}

func call[T any](ctx context.Context, client *http.Client, secret string, jiraURL string, target *T) error {
	httpReq, err := http.NewRequestWithContext(ctx, http.MethodGet, jiraURL, nil)
	if err != nil {
		return err
	}
	httpReq.Header.Set("Authorization", secret)

	resp, err := client.Do(httpReq)
	if err != nil {
		return err
	}
	if resp.StatusCode != http.StatusOK {
		body, readAllErr := io.ReadAll(resp.Body)
		if readAllErr != nil {
			return readAllErr
		}
		return errors.New(string(body))
	}
	defer resp.Body.Close()
	return json.NewDecoder(resp.Body).Decode(target)
}

func (a *API) CountIssue(ctx context.Context, req model.CountIssueRequest) (*model.CountIssueResponse, error) {
	jiraURL := "https://lionparcel.atlassian.net/rest/api/3/search/approximate-count"

	requestBody, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}

	httpReq, err := http.NewRequestWithContext(ctx, http.MethodPost, jiraURL, strings.NewReader(string(requestBody)))
	if err != nil {
		return nil, err
	}
	httpReq.Header.Set("Authorization", a.cfg.Jira.SecretKey)
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := a.client.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, readAllErr := io.ReadAll(resp.Body)
		if readAllErr != nil {
			return nil, readAllErr
		}
		return nil, errors.New(string(body))
	}

	var res model.CountIssueResponse
	if err := json.NewDecoder(resp.Body).Decode(&res); err != nil {
		return nil, err
	}

	return &res, nil
}

func (a *API) GetSprints(ctx context.Context, boardID int) (*model.SprintResponse, error) {
	jiraURL := fmt.Sprintf("https://lionparcel.atlassian.net/rest/agile/1.0/board/%d/sprint", boardID)

	httpReq, err := http.NewRequestWithContext(ctx, http.MethodGet, jiraURL, nil)
	if err != nil {
		return nil, err
	}

	httpReq.Header.Set("Authorization", a.cfg.Jira.SecretKey)

	resp, err := a.client.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, readAllErr := io.ReadAll(resp.Body)
		if readAllErr != nil {
			return nil, readAllErr
		}
		return nil, errors.New(string(body))
	}

	var result model.SprintResponse
	return &result, json.NewDecoder(resp.Body).Decode(&result)
}

func (a *API) GetIssueChangelog(ctx context.Context, issueKey string) (*model.ChangelogResponse, error) {
	jiraURL := fmt.Sprintf("https://lionparcel.atlassian.net/rest/api/3/issue/%s?expand=changelog", issueKey)

	var res model.ChangelogResponse
	if err := call(ctx, a.client, a.cfg.Jira.SecretKey, jiraURL, &res); err != nil {
		return nil, err
	}

	return &res, nil
}

func (a *API) GetIssueWorklogs(ctx context.Context, issueKey string) (*model.FieldsWorklog, error) {
	jiraURL := fmt.Sprintf("https://lionparcel.atlassian.net//rest/api/3/issue/%s/worklog", issueKey)
	var res model.FieldsWorklog
	if err := call(ctx, a.client, a.cfg.Jira.SecretKey, jiraURL, &res); err != nil {
		return nil, err
	}
	return &res, nil
}
