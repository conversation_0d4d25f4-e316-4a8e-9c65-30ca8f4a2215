package sdet

import (
	"context"

	"github.com/Lionparcel/pentools/internal/model"
)

func (u *Database) GetRegressionReport(ctx context.Context, startDate, endDate string) ([]model.RegressionReport, error) {
	var report []model.RegressionReport
	if err := u.db.Slave.WithContext(ctx).Raw(`
with LatestResults as (
select
	tr_tc_test_id,
	tr_env_name,
	tr_test_result,
	tr_release_tag,
	tr_platform_name,
	tr_update_at,
	tr_time_execution,
	row_number() over (
      partition by tr_tc_test_id,
	tr_release_tag
order by
	tr_update_at desc
    ) as rn
from
	test_result
where tr_release_tag >= ? and tr_release_tag <= ?
)
select
	tc.tc_squad_name as squad,
	lr.tr_release_tag as date_release,
	lr.tr_platform_name as platform,
	lr.tr_env_name as env,
	COUNT(case when lr.tr_test_result = 'PASS' then 1 end) as total_passed,
	COUNT(case when lr.tr_test_result = 'FAIL' then 1 end) as total_failed,
	SUM(lr.tr_time_execution) as execution_time,
	-- Count unique test IDs
	ROUND(
    (COUNT(case when lr.tr_test_result = 'PASS' then 1 end) * 100.0) / 
    nullif(COUNT(distinct lr.tr_tc_test_id), 0), 2
  ) as success_rate
from
	LatestResults lr
join test_case tc on
	lr.tr_tc_test_id = tc.tc_test_id
where
	-- Pick only the latest test result per tr_tc_test_id
	lr.rn = 1
group by
	tc.tc_squad_name,
	lr.tr_platform_name,
	lr.tr_release_tag,
	lr.tr_env_name
order by
	date_release desc;
	`, startDate, endDate).Find(&report).Error; err != nil {
		return nil, err
	}
	return report, nil
}
func (u *Database) GetRegressionExecutionTimeReport(ctx context.Context, startDate, endDate string) ([]model.RegressionExecutionTimeReport, error) {
	var report []model.RegressionExecutionTimeReport
	if err := u.db.Slave.WithContext(ctx).Raw(`
select
	tr_tc_test_id as test_id,
	tr_release_tag as date_release,
	tr_platform_name as platform,
	tr_test_result as status,
	SUM(tr_time_execution) as execution_time
	-- Count unique test IDs
from
	test_result
where
	tr_time_execution > 90
	and tr_release_tag >= ? and tr_release_tag <= ?
group by
	tr_tc_test_id,
	tr_release_tag,
	tr_platform_name,
	tr_test_result
order by
	execution_time desc;
	`, startDate, endDate).Find(&report).Error; err != nil {
		return nil, err
	}
	return report, nil
}
func (u *Database) GetAdditionTestCaseReport(ctx context.Context, startDate, endDate string) ([]model.TestCaseReport, error) {
	var report []model.TestCaseReport
	if err := u.db.Slave.WithContext(ctx).Raw(`
select
	tc_author_name as author,
	DATE_TRUNC('day', tc_created_at) as date,
	tc_platform_name as platform,
	COUNT(*) as value
from
	test_case
where
	tc_created_at is not null
	and tc_author_name is not null
	and tc_author_name != ''
	and tc_created_at >= ? and tc_created_at <= ?
group by
	DATE_TRUNC('day', tc_created_at),
	tc_platform_name,
	tc_author_name
order by
	date desc;
	`, startDate, endDate).Find(&report).Error; err != nil {
		return nil, err
	}
	return report, nil
}
func (u *Database) GetAdjustmentTestCaseReport(ctx context.Context, startDate, endDate string) ([]model.TestCaseReport, error) {
	var report []model.TestCaseReport
	if err := u.db.Slave.WithContext(ctx).Raw(`
select
	tc_updated_by as author,
	DATE_TRUNC('day', tc_updated_at) as date,
	tc_platform_name as platform,
	COUNT(*) as value
from
	test_case
where
	tc_updated_at is not null
	and tc_updated_by is not null
	and tc_updated_by != ''
	and tc_updated_at >= ? and tc_updated_at <= ?
group by
	DATE_TRUNC('day', tc_updated_at),
	tc_platform_name,
	tc_updated_by
order by
	date desc;
	`, startDate, endDate).Find(&report).Error; err != nil {
		return nil, err
	}
	return report, nil
}
