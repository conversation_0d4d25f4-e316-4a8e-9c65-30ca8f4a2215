package opensearch

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"strings"

	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/config"
	opensearch "github.com/opensearch-project/opensearch-go/v2"
	"github.com/opensearch-project/opensearch-go/v2/opensearchapi"
)

// ErrNotFound is returned when a document is not found.
var ErrNotFound = errors.New("not found")

// ReleasesRepository describes repository operations for releases index.
type ReleasesRepository interface {
	Create(ctx context.Context, doc model.ReleaseDocument) (string, error)
	Get(ctx context.Context, id string) (*model.ReleaseDocument, error)
	Update(ctx context.Context, id string, doc model.ReleaseDocument) error
	Delete(ctx context.Context, id string) (bool, error)
	Search(ctx context.Context, query string, limit, offset int) ([]struct {
		ID  string
		Doc model.ReleaseDocument
	}, error)
}

// ReleasesRepo is a concrete implementation backed by OpenSearch Go client.
type ReleasesRepo struct {
	client *opensearch.Client
	index  string
}

// NewReleasesRepository creates a repository backed by OpenSearch Go client.
func NewReleasesRepository(cfg *config.Config) (*ReleasesRepo, error) {
	if cfg == nil || len(cfg.OpenSearch.Addresses) == 0 {
		return nil, errors.New("opensearch addresses not configured")
	}
	if cfg.OpenSearch.Index == "" {
		return nil, errors.New("opensearch index not configured")
	}
	cli, err := opensearch.NewClient(opensearch.Config{
		Addresses: cfg.OpenSearch.Addresses,
		Username:  cfg.OpenSearch.Username,
		Password:  cfg.OpenSearch.Password,
	})
	if err != nil {
		return nil, err
	}
	return &ReleasesRepo{client: cli, index: cfg.OpenSearch.Index}, nil
}

// Create indexes a new release document and returns its ID.
func (r *ReleasesRepo) Create(ctx context.Context, doc model.ReleaseDocument) (string, error) {
	body, err := json.Marshal(doc)
	if err != nil {
		return "", err
	}
	req := opensearchapi.IndexRequest{Index: r.index, Body: bytes.NewReader(body)}
	res, doErr := req.Do(ctx, r.client)
	if doErr != nil {
		return "", doErr
	}
	defer res.Body.Close()
	if res.StatusCode >= http.StatusMultipleChoices {
		b, _ := io.ReadAll(res.Body)
		return "", errors.New(strings.TrimSpace(string(b)))
	}
	var out struct {
		ID string `json:"_id"`
	}
	if decErr := json.NewDecoder(res.Body).Decode(&out); decErr != nil {
		return "", decErr
	}
	return out.ID, nil
}

// Get retrieves a release document by ID.
func (r *ReleasesRepo) Get(ctx context.Context, id string) (*model.ReleaseDocument, error) {
	req := opensearchapi.GetRequest{Index: r.index, DocumentID: id}
	res, err := req.Do(ctx, r.client)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	if res.StatusCode == http.StatusNotFound {
		return nil, ErrNotFound
	}
	if res.StatusCode >= http.StatusMultipleChoices {
		b, _ := io.ReadAll(res.Body)
		return nil, errors.New(strings.TrimSpace(string(b)))
	}
	var out struct {
		Found  bool                  `json:"found"`
		Source model.ReleaseDocument `json:"_source"`
	}
	if decErr := json.NewDecoder(res.Body).Decode(&out); decErr != nil {
		return nil, decErr
	}
	if !out.Found {
		return nil, ErrNotFound
	}
	return &out.Source, nil
}

// Update partially updates a release document by ID.
func (r *ReleasesRepo) Update(ctx context.Context, id string, doc model.ReleaseDocument) error {
	payload := map[string]any{"doc": doc}
	body, err := json.Marshal(payload)
	if err != nil {
		return err
	}
	req := opensearchapi.UpdateRequest{Index: r.index, DocumentID: id, Body: bytes.NewReader(body)}
	res, doErr := req.Do(ctx, r.client)
	if doErr != nil {
		return doErr
	}
	defer res.Body.Close()
	if res.StatusCode >= http.StatusMultipleChoices {
		b, _ := io.ReadAll(res.Body)
		return errors.New(strings.TrimSpace(string(b)))
	}
	return nil
}

// Delete removes a release document by ID.
func (r *ReleasesRepo) Delete(ctx context.Context, id string) (bool, error) {
	req := opensearchapi.DeleteRequest{Index: r.index, DocumentID: id}
	res, err := req.Do(ctx, r.client)
	if err != nil {
		return false, err
	}
	defer res.Body.Close()
	if res.StatusCode == http.StatusNotFound {
		return false, ErrNotFound
	}
	if res.StatusCode >= http.StatusMultipleChoices {
		b, _ := io.ReadAll(res.Body)
		return false, errors.New(strings.TrimSpace(string(b)))
	}
	return true, nil
}

// Search queries release documents.
func (r *ReleasesRepo) Search(ctx context.Context, query string, limit, offset int) ([]struct {
	ID  string
	Doc model.ReleaseDocument
}, error) {
	if limit <= 0 {
		limit = 20
	}
	if offset < 0 {
		offset = 0
	}
	var q any
	if strings.TrimSpace(query) == "" {
		q = map[string]any{"match_all": map[string]any{}}
	} else {
		q = map[string]any{"query_string": map[string]any{"query": query}}
	}
	body, err := json.Marshal(map[string]any{"query": q, "size": limit, "from": offset})
	if err != nil {
		return nil, err
	}
	req := opensearchapi.SearchRequest{Index: []string{r.index}, Body: bytes.NewReader(body)}
	res, doErr := req.Do(ctx, r.client)
	if doErr != nil {
		return nil, doErr
	}
	defer res.Body.Close()
	if res.StatusCode >= http.StatusMultipleChoices {
		b, _ := io.ReadAll(res.Body)
		return nil, errors.New(strings.TrimSpace(string(b)))
	}
	var out struct {
		Hits struct {
			Hits []struct {
				ID     string                `json:"_id"`
				Source model.ReleaseDocument `json:"_source"`
			} `json:"hits"`
		} `json:"hits"`
	}
	if decErr := json.NewDecoder(res.Body).Decode(&out); decErr != nil {
		return nil, decErr
	}
	result := make([]struct {
		ID  string
		Doc model.ReleaseDocument
	}, 0, len(out.Hits.Hits))
	for _, h := range out.Hits.Hits {
		result = append(result, struct {
			ID  string
			Doc model.ReleaseDocument
		}{ID: h.ID, Doc: h.Source})
	}
	return result, nil
}
