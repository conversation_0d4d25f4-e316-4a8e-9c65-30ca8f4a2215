package jira

import (
	"context"

	"github.com/Lionparcel/pentools/internal/dto"
)

func (u *Usecase) GetIssueChangelog(ctx context.Context, req dto.GetChangelogRequest) (*dto.GetChangelogResponse, error) {
	changelogResp, err := u.jira.GetIssueChangelog(ctx, req.IssueKey)
	if err != nil {
		return nil, err
	}

	// Convert model to DTO
	histories := make([]dto.ChangelogHistory, 0, len(changelogResp.Changelog.Histories))
	for _, history := range changelogResp.Changelog.Histories {
		var items []dto.ChangelogItem
		for _, item := range history.Items {
			items = append(items, dto.ChangelogItem{
				Field:      item.Field,
				Fieldtype:  item.Fieldtype,
				FieldID:    item.FieldID,
				From:       item.From,
				FromString: item.FromString,
				To:         item.To,
				ToString:   item.ToString,
			})
		}

		histories = append(histories, dto.ChangelogHistory{
			ID: history.ID,
			Author: dto.ChangelogAuthor{
				AccountID:    history.Author.AccountID,
				DisplayName:  history.Author.DisplayName,
				EmailAddress: history.Author.EmailAddress,
			},
			Created: history.Created,
			Items:   items,
		})
	}

	return &dto.GetChangelogResponse{
		Expand: changelogResp.Expand,
		ID:     changelogResp.ID,
		Self:   changelogResp.Self,
		Key:    changelogResp.Key,
		Changelog: dto.Changelog{
			StartAt:    changelogResp.Changelog.StartAt,
			MaxResults: changelogResp.Changelog.MaxResults,
			Total:      changelogResp.Changelog.Total,
			Histories:  histories,
		},
	}, nil
}
