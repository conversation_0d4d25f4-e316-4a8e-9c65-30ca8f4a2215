package jira

import (
	"context"

	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/config"
)

type Jira interface {
	SearchIssue(ctx context.Context, req model.SearchIssueRequest) (*model.SearchIssueResponse, error)
	CountIssue(ctx context.Context, req model.CountIssueRequest) (*model.CountIssueResponse, error)
	GetSprints(ctx context.Context, boardID int) (*model.SprintResponse, error)
	GetIssueChangelog(ctx context.Context, issueKey string) (*model.ChangelogResponse, error)
	GetIssueWorklogs(ctx context.Context, issueKey string) (*model.FieldsWorklog, error)
}
type Usecase struct {
	cfg  *config.Config
	jira Jira
}

func New(cfg *config.Config, jira Jira) *Usecase {
	return &Usecase{
		cfg:  cfg,
		jira: jira,
	}
}
