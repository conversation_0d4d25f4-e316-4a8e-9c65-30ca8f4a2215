package jira

import (
	"context"
	"fmt"
	"log"
	"slices"
	"strings"
	"sync"
	"time"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/timex"
)

func (u *Usecase) GetUserEpic(ctx context.Context, req dto.GetUserEpicRequest) (*dto.GetUserEpicResponse, error) {
	startDate, endDate := timex.GetStartDateEndDateOrDefault(req.StartDate, req.EndDate)
	fieldsArr := []string{
		"worklog", "issuetype", "timetracking", "customfield_10080", "customfield_10024", "customfield_10101", "summary", "created", "creator", "reporter", "customfield_10033", "customfield_10016", "customfield_10360", "customfield_10361", "customfield_10393", "customfield_10591", "status", string(model.JiraFieldBEDepedenciesOrExternalFactors), string(model.JiraFieldBETechnicalComplexity), string(model.JiraFieldBENonFunctionalRequirements), string(model.JiraFieldRequirementClarity), string(model.JiraFieldSizeOfWorks), string(model.JiraFieldFEDesignUIFactor), string(model.JiraFieldFETechnicalComplexity), string(model.JiraFieldFENonFunctionalRequirements), string(model.JiraFieldRatingAcceptanceCriteria), string(model.JiraFieldRatingSupportingMaterialsRefs), string(model.JiraFieldRatingActionability), string(model.JiraFieldRatingLanguageAndStructure)}
	fields := strings.Join(fieldsArr, ",")

	responseEpic, responseWorklog := u.fetchJiraIssuesConcurrently(ctx, startDate, endDate, fields, req.UserIDs)

	userEpicResponse := &dto.GetUserEpicResponse{Epics: []dto.EpicOfUser{}}

	// Handle worklog issues for Principal Technical Documentation
	if epic := u.getPTDWorklog(responseWorklog, req.UserID); epic != nil {
		userEpicResponse.Epics = append(userEpicResponse.Epics, *epic)
	}

	// Handle Epic issues
	if responseEpic == nil {
		return userEpicResponse, nil
	}
	endDateTime, _ := time.Parse(time.DateOnly, endDate)

	epicIssues := u.getListEpicConcurrently(ctx, responseEpic.Issues, req.UserIDs, timex.EndOfDay(endDateTime))
	slices.SortFunc(epicIssues, func(a, b dto.EpicOfUser) int {
		if a.GroomingDate == b.GroomingDate {
			return 0
		}
		if a.GroomingDate > b.GroomingDate {
			return -1
		}
		return 1
	})
	userEpicResponse.Epics = append(userEpicResponse.Epics, epicIssues...)

	return userEpicResponse, nil
}

// fetchJiraIssuesConcurrently fetches Epic and Worklog issues concurrently.
func (u *Usecase) fetchJiraIssuesConcurrently(
	ctx context.Context, startDate, endDate, fields string, userIDs []string,
) (epicResp, worklogResp *model.SearchIssueResponse) {
	var wg sync.WaitGroup
	const numConcurrentFetches = 2
	wg.Add(numConcurrentFetches)
	stringUserIDs := strings.Join(userIDs, ",")
	go func() {
		defer func() {
			if err := recover(); err != nil {
				log.Printf("goroutine panic recovered: %v", err)
			}
			wg.Done()
		}()
		jql := fmt.Sprintf("worklogAuthor in (%s) and worklogDate >= %s and worklogDate <= %s and type IN (Epic, Story)", stringUserIDs, startDate, endDate)
		resp, _ := u.jira.SearchIssue(ctx, model.SearchIssueRequest{
			JQL:       jql,
			Fields:    fields,
			StartDate: startDate,
			EndDate:   endDate,
		})
		epicResp = resp
	}()

	go func() {
		defer func() {
			if err := recover(); err != nil {
				log.Printf("goroutine panic recovered: %v", err)
			}
			wg.Done()
		}()
		if len(userIDs) > 1 {
			return
		}
		jql := fmt.Sprintf(`project = "Principal Technical Documentation" and reporter in (%s) and worklogDate >= %s and worklogDate <= %s`, stringUserIDs, startDate, endDate)
		resp, _ := u.jira.SearchIssue(ctx, model.SearchIssueRequest{
			JQL:       jql,
			Fields:    fields,
			StartDate: startDate,
			EndDate:   endDate,
		})
		worklogResp = resp
	}()

	wg.Wait()
	return
}

// getPTDWorklog returns the first matching EpicOfUser for Principal Technical Documentation.
func (u *Usecase) getPTDWorklog(responseWorklog *model.SearchIssueResponse, userID string) *dto.EpicOfUser {
	if responseWorklog == nil || len(responseWorklog.Issues) == 0 {
		return nil
	}
	for _, issue := range responseWorklog.Issues {
		if strings.Contains(strings.ToLower(issue.Fields.Summary), "worklog") {
			epic := buildEpicOfUserFromWorklog(issue, userID)
			return &epic
		}
	}
	return nil
}

// getListEpicConcurrently get epics objects concurrently.
func (u *Usecase) getListEpicConcurrently(ctx context.Context, issues []model.Issue, userIDs []string, endDate time.Time) []dto.EpicOfUser {
	var wg sync.WaitGroup
	var mutex sync.Mutex
	epics := []dto.EpicOfUser{}
	wg.Add(len(issues))
	for _, issue := range issues {
		go func(issue model.Issue) {
			defer func() {
				if err := recover(); err != nil {
					log.Printf("goroutine panic recovered: %v", err)
				}
				wg.Done()
			}()
			epic, err := u.getAndBuildEpicOfUser(ctx, issue, userIDs, endDate)
			if err != nil {
				return
			}
			mutex.Lock()
			epics = append(epics, epic)
			mutex.Unlock()
		}(issue)
	}
	wg.Wait()
	return epics
}

// Helper function to build EpicOfUser from a worklog issue
func buildEpicOfUserFromWorklog(issue model.Issue, userID string) dto.EpicOfUser {
	firstCreatedWorklog, lastCreatedWorklog := issue.Fields.Worklog.FirstAndLastCreatedWorklog()
	operationHour := int64(0)
	issues := []dto.EpicOfUserIssue{}
	const expectedRegexMatches = 3
	for _, wl := range issue.Fields.Worklog.Worklogs {
		if userID != wl.Author.AccountID {
			continue
		}
		comment := wl.FormattedComment()
		i := strings.Index(comment, "]")
		typeStr := strings.TrimPrefix(strings.TrimSuffix(comment[:i+1], "]"), "[")
		issueName := comment[i+1:]
		issueName = strings.TrimSpace(strings.TrimPrefix(issueName, "\n"))
		issues = append(issues, dto.EpicOfUserIssue{
			Name:                wl.Author.DisplayName,
			UserID:              wl.Author.AccountID,
			Type:                typeStr,
			IssueName:           issueName,
			LinkTicket:          issue.TicketLink(),
			TimeSpentHour:       (time.Duration(wl.TimeSpentSeconds) * time.Second).Hours(),
			TimeSpentHourSecond: wl.TimeSpentSeconds,
			TimeSpentHuman:      (time.Duration(wl.TimeSpentSeconds) * time.Second).String(),
			Status:              model.IssueStatusDone,
			LastActivityAt:      wl.Started,
		})
		operationHour += wl.TimeSpentSeconds
	}
	return dto.EpicOfUser{
		Reporter:            issue.Fields.Reporter.DisplayName,
		EpicName:            issue.Fields.Summary,
		EpicLink:            issue.TicketLink(),
		GroomingDate:        firstCreatedWorklog,
		DoneBreakDownDate:   lastCreatedWorklog,
		Issues:              issues,
		OperationHour:       (time.Duration(operationHour) * time.Second).Hours(),
		OperationHourHuman:  (time.Duration(operationHour) * time.Second).String(),
		OperationHourSecond: operationHour,
		BreakDownStatus:     "Done",
	}
}

func (u *Usecase) getAndBuildEpicOfUser(ctx context.Context, issue model.Issue, userID []string, endDate time.Time) (dto.EpicOfUser, error) {
	filterUserID := userID[0]
	if len(userID) > 1 {
		filterUserID = ""
	}
	breakDownDurationSecond := issue.Fields.Worklog.BreakDownDurationSecond(filterUserID, endDate)

	epic := dto.EpicOfUser{
		Reporter:                issue.Fields.Reporter.DisplayName,
		EpicName:                issue.Fields.Summary,
		EpicLink:                issue.TicketLink(),
		GroomingDate:            issue.Fields.Worklog.GroomingDate(filterUserID),
		DoneBreakDownDate:       issue.Fields.Worklog.DoneBreakDownDate(filterUserID),
		BreakDownDurationSecond: breakDownDurationSecond,
		BreakDownDurationHour:   (time.Duration(breakDownDurationSecond) * time.Second).Hours(),
		BreakDownDurationHuman:  (time.Duration(breakDownDurationSecond) * time.Second).String(),
		BreakDownStatus:         issue.Fields.Worklog.BreakDownStatus(filterUserID),
		ExpectedStartDate:       issue.Fields.ExpectedStartDate,
		ExpectedEndDate:         issue.Fields.ExpectedEndDate,
		Worklogs:                dto.NewJiraWorklogs(issue.Fields.Worklog.Worklogs, filterUserID, endDate),
	}
	stringUserIDs := strings.Join(userID, ",")
	jql := fmt.Sprintf(`(parentEpic = %s or Parent = %s) and type not in (Epic, Bug, Story) and ("Platform Engineer[User Picker (single user)]" in (%s) or reporter in (%s) or assignee in (%s))`, issue.Key, issue.Key, stringUserIDs, stringUserIDs, stringUserIDs)
	fieldsArr := []string{"worklog", "issuetype", "timetracking", "customfield_10080", "customfield_10024", "customfield_10101", "summary", "created", "creator", "reporter", "customfield_10033", "customfield_10016", "customfield_10360", "customfield_10361", "customfield_10393", "customfield_10591", "status", string(model.JiraFieldBEDepedenciesOrExternalFactors), string(model.JiraFieldBETechnicalComplexity), string(model.JiraFieldBENonFunctionalRequirements), string(model.JiraFieldRequirementClarity), string(model.JiraFieldSizeOfWorks), string(model.JiraFieldFEDesignUIFactor), string(model.JiraFieldFETechnicalComplexity), string(model.JiraFieldFENonFunctionalRequirements), string(model.JiraFieldRatingAcceptanceCriteria), string(model.JiraFieldRatingSupportingMaterialsRefs), string(model.JiraFieldRatingActionability), string(model.JiraFieldRatingLanguageAndStructure)}
	fields := strings.Join(fieldsArr, ",")

	responseIssue, err := u.jira.SearchIssue(ctx, model.SearchIssueRequest{
		JQL:    jql,
		Fields: fields,
	})
	if err != nil {
		return dto.EpicOfUser{}, err
	}
	if responseIssue == nil {
		return epic, nil
	}
	issueList := []dto.EpicOfUserIssue{}
	totalSP := float64(0)
	totalOperationHourSecond := int64(0)
	totalDetailingHourSecond := int64(0)
	for _, issue := range responseIssue.Issues {
		if issue.Fields.PEUserID() != filterUserID && filterUserID != "" {
			continue
		}
		sp := issue.Fields.SP()
		totalSP += sp
		operationHourSecond := issue.Fields.Worklog.OperationHourSecond(filterUserID, endDate)
		detailingDurationSecond := issue.Fields.Worklog.DetailingDurationSecond(filterUserID, endDate)
		totalOperationHourSecond += operationHourSecond
		totalDetailingHourSecond += detailingDurationSecond
		worklogs := dto.NewJiraWorklogs(issue.Fields.Worklog.Worklogs, filterUserID, endDate)
		issueList = append(issueList, dto.EpicOfUserIssue{
			Name:                           issue.Fields.PEName(),
			UserID:                         issue.Fields.PEUserID(),
			Type:                           issue.Fields.Issuetype.Name,
			IssueName:                      issue.Fields.Summary,
			LinkTicket:                     issue.TicketLink(),
			Status:                         issue.Fields.Worklog.IssueStatus(filterUserID, endDate),
			SP:                             sp,
			TicketStatus:                   issue.Fields.Status.Name,
			TimeSpentHour:                  (time.Duration(operationHourSecond+detailingDurationSecond) * time.Second).Hours(),
			TimeSpentHourSecond:            operationHourSecond + detailingDurationSecond,
			TimeSpentHuman:                 timex.FormatDurationHuman(operationHourSecond + detailingDurationSecond),
			TimeSpentDetailingHour:         (time.Duration(detailingDurationSecond) * time.Second).Hours(),
			TimeSpentDetailingHourSecond:   detailingDurationSecond,
			TimeSpentDetailingHuman:        timex.FormatDurationHuman(detailingDurationSecond),
			TimeSpentOperationHour:         (time.Duration(operationHourSecond) * time.Second).Hours(),
			TimeSpentOperationHourSecond:   operationHourSecond,
			TimeSpentOperationHuman:        timex.FormatDurationHuman(operationHourSecond),
			LastActivityAt:                 issue.Fields.Worklog.LastActivityAt(filterUserID, endDate),
			ExpectedStartDate:              issue.Fields.ExpectedStartDate,
			ExpectedEndDate:                issue.Fields.ExpectedEndDate,
			Worklogs:                       worklogs,
			BEDepedenciesOrExternalFactors: issue.Fields.BEDepedenciesOrExternalFactors.Value,
			BETechnicalComplexity:          issue.Fields.BETechnicalComplexity.Value,
			BENonFunctionalRequirements:    issue.Fields.BENonFunctionalRequirements.Value,
			RequirementClarity:             issue.Fields.RequirementClarity.Value,
			SizeOfWorks:                    issue.Fields.SizeOfWorks.Value,
			FEDesignUIFactor:               issue.Fields.FEDesignUIFactor.Value,
			FETechnicalComplexity:          issue.Fields.FETechnicalComplexity.Value,
			FENonFunctionalRequirements:    issue.Fields.FENonFunctionalRequirements.Value,
			RatingAcceptanceCriteria:       issue.Fields.RatingAcceptanceCriteria.Value,
			RatingActionability:            issue.Fields.RatingActionability.Value,
			RatingSupportingMaterialsRefs:  issue.Fields.RatingSupportingMaterialsRefs.Value,
			RatingLanguageAndStructure:     issue.Fields.RatingLanguageAndStructure.Value,
		})
	}
	epic.SP = totalSP
	epic.Issues = issueList
	epic.OperationHourSecond = totalOperationHourSecond
	epic.OperationHour = (time.Duration(totalOperationHourSecond) * time.Second).Hours()
	epic.OperationHourHuman = timex.FormatDurationHuman(totalOperationHourSecond)
	epic.DetailingDurationSecond = totalDetailingHourSecond
	epic.DetailingDurationHour = (time.Duration(totalDetailingHourSecond) * time.Second).Hours()
	epic.DetailingDurationHuman = timex.FormatDurationHuman(totalDetailingHourSecond)
	return epic, nil
}
