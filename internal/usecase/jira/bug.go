package jira

import (
	"context"
	"fmt"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/timex"
)

func (u *Usecase) GetBugs(ctx context.Context, req dto.GetBugsRequest) (*dto.GetJiraBugsResponse, error) {
	startDate, endDate := timex.GetStartDateEndDateOrDefault(req.StartDate, req.EndDate)
	filterByPE := ""
	if req.PEUserID != "" {
		filterByPE = fmt.Sprintf(`AND "Platform Engineer[User Picker (single user)]" = %s `, req.PEUserID)
	}
	jql := fmt.Sprintf(`(type = Bug) %s AND created >= %s AND created <= %s AND "Environment Bug[Dropdown]" = Development  ORDER BY issuekey DESC`, filterByPE, startDate, endDate)
	fields := "issuetype,assignee,labels,parent,customfield_10033,summary,created,creator,reporter,customfield_10090,customfield_10151,customfield_10155,customfield_10132,customfield_10194,customfield_10156,customfield_10150,customfield_10169,customfield_10170,customfield_10193,customfield_10327,customfield_10328,customfield_10329,customfield_10332,customfield_10459"
	bugIssues, err := u.jira.SearchIssue(ctx, model.SearchIssueRequest{
		JQL:       jql,
		Fields:    fields,
		StartDate: startDate,
		EndDate:   endDate,
	})
	if err != nil {
		return nil, err
	}
	bugResponses := make([]dto.JiraBug, 0, len(bugIssues.Issues))
	for _, issue := range bugIssues.Issues {
		bugResponse := dto.JiraBug{
			BugName:             issue.Fields.Summary,
			BugLink:             issue.TicketLink(),
			QAAssignee:          issue.Fields.QAAssignee.DisplayName,
			PriorityFeature:     issue.Fields.PriorityFeature.Value,
			Squad:               issue.Fields.Squad.Value,
			EnvironmentBug:      issue.Fields.EnvironmentBug.Value,
			TestCaseType:        issue.Fields.TestCaseType.Value,
			AccidentBug:         issue.Fields.AccidentBug.Value,
			SeverityBug:         issue.Fields.SeverityBug.Value,
			TotalTestCaseMedium: issue.Fields.TotalTestCaseMedium,
			TotalTestCaseHigh:   issue.Fields.TotalTestCaseHigh,
			ClassificationIssue: issue.Fields.ClassificationIssue.Value,
			IssueTriggerFlow:    issue.Fields.IssueTriggerFlow.Value,
			TriggerActionIssue:  issue.Fields.TriggerActionIssue.Value,
			ImpactIssue:         issue.Fields.ImpactIssue.Value,
			IssueFromRole:       issue.Fields.IssueFromRole.Value,
			Reporter:            issue.Fields.Reporter.DisplayName,
			Assignee:            issue.Fields.Assignee.DisplayName,
			ParentName:          issue.Fields.Parentx().Fields.Summary,
			ParentLink:          issue.Fields.Parentx().TicketLink(),
			PEName:              issue.Fields.PEAssigned.DisplayName,
			PEUserID:            issue.Fields.PEAssigned.AccountID,
			TicketCreatedAt:     issue.Fields.Created,
			Labels:              issue.Fields.Labels,
			BugSegmentation:     issue.Fields.BugSegmentation.Value,
		}
		bugResponses = append(bugResponses, bugResponse)
	}
	return &dto.GetJiraBugsResponse{
		Bugs: bugResponses,
	}, nil
}
