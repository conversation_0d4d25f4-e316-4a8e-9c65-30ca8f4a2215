package jira

import (
	"context"
	"strings"
	"time"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/timex"
)

// curl example:
// curl --location 'https://lionparcel.atlassian.net/rest/api/3/search/jql?jql=project%20%3D%20Principal%20and%20status%20WAS%20IN%20(%22In%20Progress%22%2C%20%22In%20Testing%20Dev%20(Optional)%22%2C%20%22In%20testing%20stg%20(OPTIONAL)%22)%20DURING%20(%222025-08-04%2000%3A00%22%2C%20%222025-08-08%2023%3A59%22)%20ORDER%20BY%20assignee&fields=*all' \
func (u *Usecase) GetTasks(ctx context.Context, req dto.GetTasksRequest) (*dto.GetTasksResponse, error) {
	fields := "key,summary,issuetype,status,priority,assignee,reporter,created,updated,labels,parent,description,timetracking,worklog,customfield_10016,customfield_10024,customfield_10080,customfield_10101,creator,customfield_10087,customfield_10591,fixVersions,duedate,customfield_10756,customfield_10015"
	expand := "changelog"
	if req.WithIssueDescription {
		fields += "," + string(model.JiraFieldIssueRootCause)
		fields += "," + string(model.JiraFieldIssueBusinessImpact)
		fields += "," + string(model.JiraFieldIssueSolutionShortTerm)
		fields += "," + string(model.JiraFieldIssueSolutionLongTerm)
		fields += "," + string(model.JiraFieldDescription)
		expand = ""
	}

	// Search for tasks
	taskIssues, err := u.jira.SearchIssue(ctx, model.SearchIssueRequest{
		JQL:    req.JQL,
		Fields: fields,
		Expand: expand,
		Limit:  req.Limit,
	})
	if err != nil {
		return nil, err
	}

	tasks := make([]dto.JiraTask, 0, len(taskIssues.Issues))

	for _, issue := range taskIssues.Issues {
		release := model.FixVersion{}
		if len(issue.Fields.FixVersion) > 0 {
			release = issue.Fields.FixVersion[0]
		}
		task := dto.JiraTask{
			TaskName:               issue.Fields.Summary,
			TaskLink:               issue.TicketLink(),
			Type:                   issue.Fields.Issuetype.Name,
			Status:                 issue.Fields.Status.Name,
			Priority:               issue.Fields.Priority.Name,
			Assignee:               issue.Fields.Assignee.DisplayName,
			AssigneeID:             issue.Fields.Assignee.AccountID,
			Reporter:               issue.Fields.Reporter.DisplayName,
			Created:                issue.Fields.Created,
			Updated:                issue.Fields.Updated,
			SP:                     issue.Fields.SP(),
			Labels:                 issue.Fields.Labels,
			ParentName:             issue.Fields.Parentx().Fields.Summary,
			ParentLink:             issue.Fields.Parentx().TicketLink(),
			TypeTask:               issue.Fields.TypeTask.Value,
			Category:               issue.Fields.Category.Value,
			IssueRootCause:         issue.Fields.IssueRootCause.Format(),
			IssueBusinessImpact:    issue.Fields.IssueBusinessImpact.Format(),
			IssueSolutionShortTerm: issue.Fields.IssueSolutionShortTerm.Format(),
			IssueSolutionLongTerm:  issue.Fields.IssueSolutionLongTerm.Format(),
			Description:            issue.Fields.Description.Format(),
			ReleaseName:            release.Name,
			ReleaseAt:              release.ReleaseDate,
			DueDate:                issue.Fields.DueDate,
			SPDev:                  defaultWhenNil(issue.Fields.SPDev, 0),
			QAPoint:                defaultWhenNil(issue.Fields.SPQAPoint, 0),
			StartDate:              issue.Fields.StartDate,
		}
		worklogs := processTaskWorklogs(issue.Fields.Worklog.Worklogs)
		task.TimeSpentSecond = issue.Fields.Worklog.BreakDownDurationSecond("", time.Now().Add(100*30*12*24*time.Hour))
		if len(worklogs) == 0 {
			worklogs = dto.NewJiraWorklogsFromChangelog(issue.Changelog)
			for i := range worklogs {
				worklogs[i].UserID = issue.Fields.Assignee.AccountID
				worklogs[i].Name = issue.Fields.Assignee.DisplayName
				if worklogs[i].Type == "HOLD" {
					continue
				}
				task.TimeSpentSecond += worklogs[i].TimeSpentSecond
			}
		}
		task.TimeSpentHours = (time.Duration(task.TimeSpentSecond) * time.Second).Hours()
		task.TimeSpentHuman = timex.FormatDurationHuman(task.TimeSpentSecond)
		task.Worklogs = worklogs

		tasks = append(tasks, task)
	}

	return &dto.GetTasksResponse{
		Tasks: tasks,
	}, nil
}

// processTaskWorklogs filters worklogs based on user IDs and returns total seconds and processed worklogs
func processTaskWorklogs(worklogs []model.WorklogElement) []dto.Worklog {
	processedWorklogs := []dto.Worklog{}

	for _, wl := range worklogs {
		comment := wl.FormattedComment()
		i := strings.Index(comment, "]")
		var typeStr, description string
		if i > 0 && strings.HasPrefix(comment, "[") {
			typeStr = strings.TrimPrefix(strings.TrimSuffix(comment[:i+1], "]"), "[")
			description = comment[i+1:]
		} else {
			description = comment
		}

		processedWorklogs = append(processedWorklogs, dto.Worklog{
			Name:            wl.Author.DisplayName,
			UserID:          wl.Author.AccountID,
			Type:            typeStr,
			Description:     description,
			TimeSpentHuman:  wl.TimeSpent,
			TimeSpentHour:   (time.Duration(wl.TimeSpentSeconds) * time.Second).Hours(),
			TimeSpentSecond: wl.TimeSpentSeconds,
			CreatedAt:       wl.Started,
		})
	}

	return processedWorklogs
}

func defaultWhenNil[T any](value *T, defaultValue T) T {
	if value == nil {
		return defaultValue
	}
	return *value
}
