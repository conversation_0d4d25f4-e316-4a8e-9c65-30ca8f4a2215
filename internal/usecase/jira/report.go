package jira

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/timex"
)

func (u *Usecase) GetReport(ctx context.Context, req dto.GetJiraReportRequest) (*dto.GetJiraReportResponse, error) {
	startDate, endDate := timex.GetStartDateEndDateOrDefault(req.StartDate, req.EndDate)
	jql := u.cfg.Jira.DefaultJQLGetReporting
	if req.JQL != "" {
		jql = req.JQL
	}
	jql = fmt.Sprintf(jql, startDate, endDate)

	fieldsArr := []string{
		"worklog", "issuetype", "timetracking", "customfield_10080", "customfield_10024", "customfield_10101", "summary", "created", "creator", "reporter", "customfield_10033", "customfield_10016", "customfield_10360", "customfield_10361", "customfield_10393", "customfield_10591", string(model.JiraFieldBEDepedenciesOrExternalFactors), string(model.JiraFieldBETechnicalComplexity), string(model.JiraFieldBENonFunctionalRequirements), string(model.JiraFieldRequirementClarity), string(model.JiraFieldSizeOfWorks), string(model.JiraFieldFEDesignUIFactor), string(model.JiraFieldFETechnicalComplexity), string(model.JiraFieldFENonFunctionalRequirements), string(model.JiraFieldRatingAcceptanceCriteria), string(model.JiraFieldRatingSupportingMaterialsRefs), string(model.JiraFieldRatingActionability), string(model.JiraFieldRatingLanguageAndStructure)}
	fields := strings.Join(fieldsArr, ",")

	issues, err := u.getAndBuildJiraIssues(ctx, jql, fields, startDate, endDate)
	if err != nil {
		return nil, err
	}
	return &dto.GetJiraReportResponse{
		Issues: issues,
	}, nil
}

// getAndBuildJiraIssues processes the worklogIssueResp and returns a slice of JiraIssue
func (u *Usecase) getAndBuildJiraIssues(ctx context.Context, jql string, fields string, startDate, endDate string) ([]dto.JiraIssue, error) {
	worklogIssueResp, err := u.jira.SearchIssue(ctx, model.SearchIssueRequest{
		JQL:       jql,
		Fields:    fields,
		StartDate: startDate,
		EndDate:   endDate,
	})
	if err != nil {
		return nil, err
	}

	issues := []dto.JiraIssue{}
	startTime, err := time.Parse(time.DateOnly, worklogIssueResp.Params.StartDate)
	if err != nil {
		return nil, err
	}
	endTime, err := time.Parse(time.DateOnly, worklogIssueResp.Params.EndDate)
	if err != nil {
		return nil, err
	}
	startTime = timex.StartOfDay(startTime)
	endTime = timex.EndOfDay(endTime)
	const maxWorklogIssues = 20
	for _, issue := range worklogIssueResp.Issues {
		if issue.Fields.Worklog.Total > maxWorklogIssues {
			worklogs, err := u.jira.GetIssueWorklogs(ctx, issue.Key)
			if err == nil && worklogs != nil {
				issue.Fields.Worklog = *worklogs
			}
		}
		for _, worklog := range issue.Fields.Worklog.Worklogs {
			workLogStartedTime, errParseTime := time.Parse(timex.JiraDateTimeLayout, worklog.Started)
			if errParseTime != nil {
				return nil, errParseTime
			}
			if workLogStartedTime.Before(startTime) || workLogStartedTime.After(endTime) {
				continue
			}
			issues = append(issues, dto.JiraIssue{
				Name:                           worklog.Author.DisplayName,
				UserID:                         worklog.Author.AccountID,
				Type:                           issue.Fields.Issuetype.Name,
				IssueName:                      issue.Fields.Summary,
				LinkTicket:                     issue.TicketLink(),
				SP:                             issue.Fields.SP(),
				TicketCreatedAt:                issue.Fields.Created,
				WorkLogDate:                    worklog.Started,
				WorkLogTimeSpentHour:           (time.Duration(worklog.TimeSpentSeconds) * time.Second).Hours(),
				WorkLogTimeSpentHuman:          worklog.TimeSpent,
				WorkLogComment:                 worklog.FormattedComment(),
				TypeTask:                       issue.Fields.TypeTask.Value,
				BEDepedenciesOrExternalFactors: issue.Fields.BEDepedenciesOrExternalFactors.Value,
				BETechnicalComplexity:          issue.Fields.BETechnicalComplexity.Value,
				BENonFunctionalRequirements:    issue.Fields.BENonFunctionalRequirements.Value,
				RequirementClarity:             issue.Fields.RequirementClarity.Value,
				SizeOfWorks:                    issue.Fields.SizeOfWorks.Value,
				FEDesignUIFactor:               issue.Fields.FEDesignUIFactor.Value,
				FETechnicalComplexity:          issue.Fields.FETechnicalComplexity.Value,
				FENonFunctionalRequirements:    issue.Fields.FENonFunctionalRequirements.Value,
				RatingAcceptanceCriteria:       issue.Fields.RatingAcceptanceCriteria.Value,
				RatingActionability:            issue.Fields.RatingActionability.Value,
				RatingSupportingMaterialsRefs:  issue.Fields.RatingSupportingMaterialsRefs.Value,
				RatingLanguageAndStructure:     issue.Fields.RatingLanguageAndStructure.Value,
			})
		}
	}
	return issues, nil
}
