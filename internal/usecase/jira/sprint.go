package jira

import (
	"context"
	"time"

	"github.com/Lionparcel/pentools/internal/dto"
)

func (u *Usecase) GetSprints(ctx context.Context, req dto.GetSprintRequest) (*dto.GetSprintResponse, error) {
	sprints, err := u.jira.GetSprints(ctx, req.BoardID)
	if err != nil {
		return nil, err
	}

	// Filter sprints based on date range if provided
	var filteredSprints []dto.JiraSprint

	for _, sprint := range sprints.Values {
		// Convert to DTO
		dtoSprint := dto.JiraSprint{
			ID:           sprint.ID,
			Name:         sprint.Name,
			State:        sprint.State,
			StartDate:    sprint.StartDate,
			EndDate:      sprint.EndDate,
			CompleteDate: sprint.CompleteDate,
			BoardID:      sprint.BoardID,
			Goal:         sprint.Goal,
		}

		// If date range is provided, filter sprints
		if req.StartDate != "" && req.EndDate != "" {
			if u.isSprintInDateRange(sprint.StartDate, sprint.EndDate, req.StartDate, req.EndDate) {
				filteredSprints = append(filteredSprints, dtoSprint)
			}
		} else {
			// If no date range provided, include all sprints
			filteredSprints = append(filteredSprints, dtoSprint)
		}
	}

	return &dto.GetSprintResponse{
		Sprints: filteredSprints,
	}, nil
}

// Helper function to check if sprint overlaps with the given date range
func (u *Usecase) isSprintInDateRange(sprintStart, sprintEnd, rangeStart, rangeEnd string) bool {
	// Parse dates
	layout := "2006-01-02T15:04:05.000Z"
	dateLayout := "2006-01-02"

	var sprintStartTime, sprintEndTime, rangeStartTime, rangeEndTime time.Time
	var err error

	// Parse sprint start date
	if sprintStart != "" {
		sprintStartTime, err = time.Parse(layout, sprintStart)
		if err != nil {
			// Try alternative layout
			sprintStartTime, err = time.Parse(dateLayout, sprintStart)
			if err != nil {
				return false
			}
		}
	}

	// Parse sprint end date
	if sprintEnd != "" {
		sprintEndTime, err = time.Parse(layout, sprintEnd)
		if err != nil {
			// Try alternative layout
			sprintEndTime, err = time.Parse(dateLayout, sprintEnd)
			if err != nil {
				return false
			}
		}
	}

	// Parse range dates
	rangeStartTime, err = time.Parse(dateLayout, rangeStart)
	if err != nil {
		return false
	}

	rangeEndTime, err = time.Parse(dateLayout, rangeEnd)
	if err != nil {
		return false
	}

	// Check for overlap:
	// Sprint overlaps if it starts before range ends AND ends after range starts
	if sprintStart == "" || sprintEnd == "" {
		return false
	}

	return sprintStartTime.Before(rangeEndTime.AddDate(0, 0, 1)) &&
		sprintEndTime.After(rangeStartTime.AddDate(0, 0, -1))
}
