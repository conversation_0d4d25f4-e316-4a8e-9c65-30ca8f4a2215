package jira

import (
	"context"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/internal/model"
)

func (u *Usecase) GetCount(ctx context.Context, req dto.GetCountRequest) (*dto.GetCountResponse, error) {
	countResp, err := u.jira.CountIssue(ctx, model.CountIssueRequest{
		JQL: req.JQL,
	})
	if err != nil {
		return nil, err
	}

	return &dto.GetCountResponse{
		Count: countResp.Count,
	}, nil
}
