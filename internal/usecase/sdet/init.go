package sdet

import (
	"context"

	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/config"
)

type SDET interface {
	GetRegressionReport(ctx context.Context, startDate, endDate string) ([]model.RegressionReport, error)
	GetRegressionExecutionTimeReport(ctx context.Context, startDate, endDate string) ([]model.RegressionExecutionTimeReport, error)
	GetAdditionTestCaseReport(ctx context.Context, startDate, endDate string) ([]model.TestCaseReport, error)
	GetAdjustmentTestCaseReport(ctx context.Context, startDate, endDate string) ([]model.TestCaseReport, error)
}
type Usecase struct {
	cfg  *config.Config
	sdet SDET
}

func New(cfg *config.Config, sdet SDET) *Usecase {
	return &Usecase{
		cfg:  cfg,
		sdet: sdet,
	}
}
