package sdet

import (
	"context"
	"log"
	"sync"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/timex"
)

func (u *Usecase) GetReport(ctx context.Context, startDate, endDate string) (dto.GetSDETReportResponse, error) {
	var (
		regressionReport              []model.RegressionReport
		regressionExecutionTimeReport []model.RegressionExecutionTimeReport
		additionTestCaseReport        []model.TestCaseReport
		adjustmentTestCaseReport      []model.TestCaseReport
	)
	startDate, endDate = timex.GetStartDateEndDateOrDefault(startDate, endDate)
	const numReportGoroutines = 4
	wg := &sync.WaitGroup{}
	wg.Add(numReportGoroutines)
	go func() {
		defer func() {
			if err := recover(); err != nil {
				log.Printf("goroutine panic recovered: %v", err)
			}
			wg.Done()
		}()
		regressionReport, _ = u.sdet.GetRegressionReport(ctx, startDate, endDate)
	}()

	go func() {
		defer func() {
			if err := recover(); err != nil {
				log.Printf("goroutine panic recovered: %v", err)
			}
			wg.Done()
		}()
		regressionExecutionTimeReport, _ = u.sdet.GetRegressionExecutionTimeReport(ctx, startDate, endDate)
	}()

	go func() {
		defer func() {
			if err := recover(); err != nil {
				log.Printf("goroutine panic recovered: %v", err)
			}
			wg.Done()
		}()
		additionTestCaseReport, _ = u.sdet.GetAdditionTestCaseReport(ctx, startDate, endDate)
	}()

	go func() {
		defer func() {
			if err := recover(); err != nil {
				log.Printf("goroutine panic recovered: %v", err)
			}
			wg.Done()
		}()
		adjustmentTestCaseReport, _ = u.sdet.GetAdjustmentTestCaseReport(ctx, startDate, endDate)
	}()
	wg.Wait()
	return dto.GetSDETReportResponse{
		Params:                        dto.GetSDETReportRequest{StartDate: startDate, EndDate: endDate},
		RegressionReport:              regressionReport,
		RegressionExecutionTimeReport: regressionExecutionTimeReport,
		AdditionTestCaseReport:        additionTestCaseReport,
		AdjustmentTestCaseReport:      adjustmentTestCaseReport,
	}, nil
}
