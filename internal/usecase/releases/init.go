package releases

import (
	"context"
	"errors"
	"strings"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/internal/model"
	osrepo "github.com/Lionparcel/pentools/internal/repository/opensearch"
	"github.com/Lionparcel/pentools/pkg/config"
)

// Usecase is the contract for releases business logic.
type Usecase interface {
	Create(ctx context.Context, req dto.CreateReleaseRequest) (*dto.CreateReleaseResponse, error)
	Get(ctx context.Context, req dto.GetReleaseRequest) (*dto.ReleaseDoc, error)
	Update(ctx context.Context, req dto.UpdateReleaseRequest) error
	Delete(ctx context.Context, req dto.DeleteReleaseRequest) (*dto.DeleteReleaseResponse, error)
	List(ctx context.Context, req dto.ListReleasesRequest) ([]dto.ReleaseDoc, error)
}

// ErrNotFound signals missing release document.
var ErrNotFound = errors.New("not found")

// UsecaseService is the concrete implementation of Usecase.
type UsecaseService struct {
	cfg  *config.Config
	repo osrepo.ReleasesRepository
}

// New creates a new releases usecase.
func New(cfg *config.Config, repo osrepo.ReleasesRepository) *UsecaseService {
	return &UsecaseService{cfg: cfg, repo: repo}
}

func (u *UsecaseService) Create(ctx context.Context, req dto.CreateReleaseRequest) (*dto.CreateReleaseResponse, error) {
	if strings.TrimSpace(req.Title) == "" {
		return nil, errors.New("title is required")
	}
	doc := model.ReleaseDocument{
		Title: req.Title, ReleaseDate: req.ReleaseDate, ReleaseType: req.ReleaseType,
		Squads: toModelSquads(req.Squads), ReleaseSummary: toModelSummaries(req.ReleaseSummary),
		Repo: toModelRepos(req.Repo), Todos: toModelTodos(req.Todos),
	}
	id, err := u.repo.Create(ctx, doc)
	if err != nil {
		return nil, err
	}
	return &dto.CreateReleaseResponse{ID: id}, nil
}

func (u *UsecaseService) Get(ctx context.Context, req dto.GetReleaseRequest) (*dto.ReleaseDoc, error) {
	doc, err := u.repo.Get(ctx, req.ID)
	if err != nil {
		if errors.Is(err, osrepo.ErrNotFound) {
			return nil, ErrNotFound
		}
		return nil, err
	}
	if doc == nil {
		return nil, ErrNotFound
	}
	out := toDTO(*doc)
	out.ID = req.ID
	return &out, nil
}

func (u *UsecaseService) Update(ctx context.Context, req dto.UpdateReleaseRequest) error {
	existing, err := u.repo.Get(ctx, req.ID)
	if err != nil {
		return err
	}
	if existing == nil {
		return errors.New("not found")
	}
	// Merge fields
	if req.Title != nil {
		existing.Title = *req.Title
	}
	if req.ReleaseDate != nil {
		existing.ReleaseDate = *req.ReleaseDate
	}
	if req.ReleaseType != nil {
		existing.ReleaseType = *req.ReleaseType
	}
	if req.Squads != nil {
		existing.Squads = toModelSquads(*req.Squads)
	}
	if req.ReleaseSummary != nil {
		existing.ReleaseSummary = toModelSummaries(*req.ReleaseSummary)
	}
	if req.Repo != nil {
		existing.Repo = toModelRepos(*req.Repo)
	}
	if req.Todos != nil {
		existing.Todos = toModelTodos(*req.Todos)
	}
	return u.repo.Update(ctx, req.ID, *existing)
}

func (u *UsecaseService) Delete(ctx context.Context, req dto.DeleteReleaseRequest) (*dto.DeleteReleaseResponse, error) {
	ok, err := u.repo.Delete(ctx, req.ID)
	if err != nil {
		if errors.Is(err, osrepo.ErrNotFound) {
			return &dto.DeleteReleaseResponse{Deleted: false}, nil
		}
		return nil, err
	}
	return &dto.DeleteReleaseResponse{Deleted: ok}, nil
}

func (u *UsecaseService) List(ctx context.Context, req dto.ListReleasesRequest) ([]dto.ReleaseDoc, error) {
	hits, err := u.repo.Search(ctx, req.Query, req.Limit, req.Offset)
	if err != nil {
		return nil, err
	}
	out := make([]dto.ReleaseDoc, 0, len(hits))
	for _, h := range hits {
		d := toDTO(h.Doc)
		d.ID = h.ID
		out = append(out, d)
	}
	return out, nil
}

// helpers
func toModelSquads(in []dto.ReleaseSquad) []model.ReleaseSquad {
	out := make([]model.ReleaseSquad, len(in))
	for i, v := range in {
		out[i] = model.ReleaseSquad{Name: v.Name, ReleaseVersion: v.ReleaseVersion}
	}
	return out
}
func toModelSummaries(in []dto.ReleaseSummary) []model.ReleaseSummary {
	out := make([]model.ReleaseSummary, len(in))
	for i, v := range in {
		out[i] = model.ReleaseSummary{Type: v.Type, TaskLink: v.TaskLink, Title: v.Title}
	}
	return out
}
func toModelRepos(in []dto.ReleaseRepo) []model.ReleaseRepo {
	out := make([]model.ReleaseRepo, len(in))
	for i, v := range in {
		out[i] = model.ReleaseRepo{RepoName: v.RepoName, ReleaseVersion: v.ReleaseVersion, PR: v.PR, ConfigManagement: v.ConfigManagement}
	}
	return out
}
func toModelTodos(in []dto.ReleaseTodo) []model.ReleaseTodo {
	out := make([]model.ReleaseTodo, len(in))
	for i, v := range in {
		out[i] = model.ReleaseTodo{Task: v.Task, TaskURL: v.TaskURL, Priority: v.Priority, Owner: v.Owner, Status: v.Status, StartDate: v.StartDate, EndDate: v.EndDate, Notes: v.Notes}
	}
	return out
}
func toDTO(in model.ReleaseDocument) dto.ReleaseDoc {
	out := dto.ReleaseDoc{Title: in.Title, ReleaseDate: in.ReleaseDate, ReleaseType: in.ReleaseType}
	for _, v := range in.Squads {
		out.Squads = append(out.Squads, dto.ReleaseSquad{Name: v.Name, ReleaseVersion: v.ReleaseVersion})
	}
	for _, v := range in.ReleaseSummary {
		out.ReleaseSummary = append(out.ReleaseSummary, dto.ReleaseSummary{Type: v.Type, TaskLink: v.TaskLink, Title: v.Title})
	}
	for _, v := range in.Repo {
		out.Repo = append(out.Repo, dto.ReleaseRepo{RepoName: v.RepoName, ReleaseVersion: v.ReleaseVersion, PR: v.PR, ConfigManagement: v.ConfigManagement})
	}
	for _, v := range in.Todos {
		out.Todos = append(out.Todos, dto.ReleaseTodo{Task: v.Task, TaskURL: v.TaskURL, Priority: v.Priority, Owner: v.Owner, Status: v.Status, StartDate: v.StartDate, EndDate: v.EndDate, Notes: v.Notes})
	}
	return out
}
