package eng

import (
	"context"
	"fmt"
	"log"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/pkg/timex"
)

type RawDataResponse struct {
	Data []Data `json:"data"`
}

type Metrics struct {
	TotalSP       float64 `json:"total_sp"`
	TotalManpower uint    `json:"total_manpower"`
	SPPerManpower float64 `json:"sp_per_manpower"`
	TotalHours    float64 `json:"total_hours"`
	HoursPerSP    float64 `json:"hours_per_sp"`
}

type RoleMetrics struct {
	Metrics
	RoleName string `json:"role_name"`
	Member   []MemberMetrics
}

type MemberMetrics struct {
	Metrics
	MemberName string `json:"member_name"`
}

type Data struct {
	ReleaseDate string `json:"release_date"`
	TotalEpic   uint   `json:"total_epic"`
	Metrics
	RoleMetrics []RoleMetrics `json:"role_metrics"`
}

func (u *Usecase) GetRawData(ctx context.Context, req dto.GetEngRequest) (*RawDataResponse, error) {
	var ret RawDataResponse
	startDate, endDate := timex.GetStartDateAndPlusOne(req.StartDate, req.EndDate)

	baseJql := fmt.Sprintf(`
		"Main Project[Checkboxes]" = Yes AND 
		Status = Done AND
		fixVersion is not empty and
		"Expected End Date[Date]" >= %s AND
		"Expected End Date[Date]" <= %s`,
		startDate, endDate,
	)
	resp, err := u.jiraService.GetRawData(ctx, baseJql, NewJqlConfig(
		[]JiraFieldOption{
			Assignee,
			Priority,
			EnvironmentBug,
			Created,
		}, []JiraExpandOption{Changelog}))
	if err != nil {
		return nil, err
	}
	log.Println(resp)
	return &ret, nil
}
