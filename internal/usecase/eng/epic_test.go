package eng_test

import (
	"runtime"
	"testing"
	"time"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/internal/usecase/eng"
	"github.com/Lionparcel/pentools/pkg/timex"
	"github.com/stretchr/testify/suite"
)

var sp = 1.0
var doneStatus = string(model.IssueStatusDone)
var readyToTestStatus = string(model.IssueStatusReadyToTest)
var retestingStatus = string(model.IssueStatusRetesting)
var inQaStatus = string(model.IssueStatusInQA)
var inProgressStatus = string(model.IssueStatusInProgress)

type SuiteUsecaseEpic struct {
	suite.Suite
	usecase eng.Usecase
	jira    *MockJiraRepo
	req     dto.GetEngEpicRequest
}

func (s *SuiteUsecaseEpic) MockGetQA() {
	s.jira.MockGetQA(true, MockGroupMemberResponse{Member: []model.Author{
		mockQA,
	}, Err: nil})
}

func (s *SuiteUsecaseEpic) MockGetEngineer(err ...error) {
	var errResp error
	if len(err) > 0 {
		errResp = err[0]
	}
	s.jira.MockGetEngineer(true, MockGroupMemberResponse{Member: []model.Author{
		mockEngineer,
	}, Err: errResp})
}

func (s *SuiteUsecaseEpic) MockGetEpicIssue(err ...error) {
	var errResp error
	if len(err) > 0 {
		errResp = err[0]
	}
	startDate, endDate := timex.GetStartDateEndDateOrDefault(s.req.StartDate, s.req.EndDate)
	s.jira.MockGetEpicIssue(startDate, endDate, s.req.IsActiveOnly, eng.NewJqlConfig(
		[]eng.JiraFieldOption{
			eng.Summary,
			eng.Reporter,
			eng.Timeframe,
			eng.FixVersion,
			eng.Status,
		},
		nil, // no expand options
	),
		MockSearchIssueResponse{
			Issues: []model.Issue{
				mockEpic,
			},
			Err: errResp,
		})
}

func (s *SuiteUsecaseEpic) MockGetChildTaskIssuesLvStory(err ...error) {
	var errResp error
	if len(err) > 0 {
		errResp = err[0]
	}
	s.jira.MockGetChildTaskIssues([]string{mockEpic.Key}, eng.NewJqlConfig(
		[]eng.JiraFieldOption{
			eng.Summary,
			eng.Parent,
			eng.Timeframe,
			eng.SP,
			eng.Assignee,
			eng.IssueLinks,
			eng.AdditionalTask,
			eng.Status,
		},
		[]eng.JiraExpandOption{
			eng.Changelog,
		},
	),
		MockSearchIssueResponse{
			Issues: []model.Issue{
				mockStory,
				mockTaskQA,
			},
			Err: errResp,
		})
}

func (s *SuiteUsecaseEpic) MockGetChildTaskIssuesLvSubTask(err ...error) {
	var errResp error
	if len(err) > 0 {
		errResp = err[0]
	}
	s.jira.MockGetChildTaskIssues([]string{mockStory.Key, mockTaskQA.Key},
		eng.NewJqlConfig([]eng.JiraFieldOption{
			eng.Summary,
			eng.Parent,
			eng.Timeframe,
			eng.SP,
			eng.Assignee,
			eng.IssueLinks,
			eng.AdditionalTask,
			eng.Status,
		}, []eng.JiraExpandOption{eng.Changelog}),
		MockSearchIssueResponse{
			Issues: []model.Issue{
				mockSubTask,
				mockSubTaskFromProduct,
				mockSubTaskFromSA,
			},
			Err: errResp,
		})
}

func (s *SuiteUsecaseEpic) MockGetRelatedBugsByTasks(err ...error) {
	var errResp error
	if len(err) > 0 {
		errResp = err[0]
	}
	s.jira.MockGetRelatedBugsByTasks([]string{mockBug.Key},
		eng.NewJqlConfig(
			[]eng.JiraFieldOption{
				eng.AccidentBug,
			},
			[]eng.JiraExpandOption{
				eng.Changelog,
			},
		),
		MockSearchIssueResponse{
			Issues: []model.Issue{
				mockBug,
			},
			Err: errResp,
		})
}

func (s *SuiteUsecaseEpic) SetupTest() {
	s.req = dto.GetEngEpicRequest{}
	s.jira = new(MockJiraRepo)
	js := eng.NewJiraService(s.jira)
	s.usecase = *eng.New(js)
}

func (s *SuiteUsecaseEpic) TestGetEpic() {
	s.MockGetEpicIssue()
	s.MockGetChildTaskIssuesLvStory()
	s.MockGetChildTaskIssuesLvSubTask()
	s.MockGetEngineer()
	s.MockGetQA()
	s.MockGetRelatedBugsByTasks()

	resp, err := s.usecase.GetEpic(s.T().Context(), s.req)
	expected := s.getExpected()

	s.Equal(expected, resp)
	s.NoError(err)
}

func (*SuiteUsecaseEpic) getTaskExpected() []dto.Task {
	return []dto.Task{
		{
			Key:               mockSubTask.Key,
			Summary:           mockSubTask.Fields.Summary,
			Status:            doneStatus,
			Assignee:          mockSubTask.Fields.Assignee.DisplayName,
			SP:                mockSubTask.Fields.SP(),
			ExpectedStartDate: mockSubTask.Fields.EngExpectedStartDateAsDate(),
			ActualStartDate:   mockSubTask.ActualStartDate(false),
			ExpectedEndDate:   mockSubTask.Fields.EngExpectedEndDateAsDate(),
			ActualEndDate:     mockSubTask.ActualEndDate(false),
			Ontime:            "Ontime",
			Hours:             1,
			HoursPerSP:        1,
			Additional:        "",
			Bug:               1,
			URL:               mockSubTask.TicketLink(),
		},
		{
			Key:               mockSubTaskFromProduct.Key,
			Summary:           mockSubTaskFromProduct.Fields.Summary,
			Assignee:          mockSubTaskFromProduct.Fields.Assignee.DisplayName,
			SP:                mockSubTaskFromProduct.Fields.SP(),
			ExpectedStartDate: mockSubTaskFromProduct.Fields.EngExpectedStartDateAsDate(),
			ActualStartDate:   mockSubTaskFromProduct.ActualStartDate(false),
			ExpectedEndDate:   mockSubTaskFromProduct.Fields.EngExpectedEndDateAsDate(),
			ActualEndDate:     mockSubTaskFromProduct.ActualEndDate(false),
			Ontime:            "Ontime",
			Hours:             1,
			HoursPerSP:        1,
			Additional:        "From Product",
			Bug:               0,
			URL:               mockSubTaskFromProduct.TicketLink(),
		},
		{
			Key:               mockSubTaskFromSA.Key,
			Summary:           mockSubTaskFromSA.Fields.Summary,
			Assignee:          mockSubTaskFromSA.Fields.Assignee.DisplayName,
			SP:                mockSubTaskFromSA.Fields.SP(),
			ExpectedStartDate: mockSubTaskFromSA.Fields.EngExpectedStartDateAsDate(),
			ActualStartDate:   mockSubTaskFromSA.ActualStartDate(false),
			ExpectedEndDate:   mockSubTaskFromSA.Fields.EngExpectedEndDateAsDate(),
			ActualEndDate:     mockSubTaskFromSA.ActualEndDate(false),
			Ontime:            "Late",
			Hours:             1,
			HoursPerSP:        1,
			Additional:        "From SA",
			Bug:               0,
			URL:               mockSubTaskFromSA.TicketLink(),
		},
		{
			Key:               mockTaskQA.Key,
			Status:            doneStatus,
			Summary:           mockTaskQA.Fields.Summary,
			Assignee:          mockTaskQA.Fields.Assignee.DisplayName,
			SP:                mockTaskQA.Fields.SP(),
			ExpectedStartDate: mockTaskQA.Fields.EngExpectedStartDateAsDate(),
			ActualStartDate:   mockTaskQA.ActualStartDate(true),
			ExpectedEndDate:   mockTaskQA.Fields.EngExpectedEndDateAsDate(),
			ActualEndDate:     mockTaskQA.ActualEndDate(true),
			Ontime:            "Ontime",
			Hours:             2,
			HoursPerSP:        2,
			Additional:        "",
			Bug:               0,
			URL:               mockTaskQA.TicketLink(),
		},
	}
}

func (s *SuiteUsecaseEpic) getEpicExpected() []dto.Epic {

	return []dto.Epic{
		{
			Name:                mockEpic.Fields.Summary,
			Reporter:            mockEpic.Fields.Reporter.DisplayName,
			GroomingDate:        mockEpic.Fields.EngExpectedStartDateAsDate(),
			ExpectedReleaseDate: mockEpic.Fields.EngExpectedEndDateAsDate(),
			TotalTicket:         4,
			AddFromProduct:      1,
			AddFromSA:           1,
			TotalSP:             4,
			TotalHours:          5,
			HoursPerSP:          1.25,
			TotalBug:            1,
			RetestHours:         1,
			URL:                 mockEpic.TicketLink(),
			SPEngineer:          3,
			SPQA:                1,
			OntimeTask:          3,
			TestingHours:        2,
			Status:              "In Progress",
			FixVersion:          "Fix Version",
			Tasks:               s.getTaskExpected(),
		},
	}
}

func (s *SuiteUsecaseEpic) getExpected() *dto.GetEngEpicResponse {
	expected := &dto.GetEngEpicResponse{
		TotalTicket:     4,
		TotalSP:         4,
		TotalSPEngineer: 3,
		TotalSPQA:       1,
		OntimePercent:   75,
		AvgRetestHour:   50,
		Epics:           s.getEpicExpected(),
	}
	return expected
}

func TestSuiteUsecaseEpic(t *testing.T) {
	suite.Run(t, new(SuiteUsecaseEpic))
}

func BenchmarkGetEpic(b *testing.B) {
	limit := GetLimitBenchmark()
	SimulatedJiraLatency = time.Duration(limit.MaxAllowedDuration) * time.Nanosecond
	defer func() { SimulatedJiraLatency = 0 }()
	limit.MaxAllowedDuration *= 5

	var totalAllocs, totalBytes uint64
	b.ReportAllocs()

	jira := new(MockJiraRepo)
	js := eng.NewJiraService(jira)
	uc := *eng.New(js)

	s := &SuiteUsecaseEpic{
		jira:    jira,
		req:     dto.GetEngEpicRequest{},
		usecase: uc,
	}

	// Setup necessary mocks
	s.MockGetEpicIssue()
	s.MockGetChildTaskIssuesLvStory()
	s.MockGetChildTaskIssuesLvSubTask()
	s.MockGetEngineer()
	s.MockGetQA()
	s.MockGetRelatedBugsByTasks()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var memStart, memEnd runtime.MemStats
		runtime.ReadMemStats(&memStart)

		start := time.Now()
		_, err := uc.GetEpic(b.Context(), s.req)
		duration := time.Since(start)

		if err != nil {
			b.Fatalf("unexpected error: %v", err)
		}
		if duration.Nanoseconds() > limit.MaxAllowedDuration {
			b.Fatalf("too slow: %d ns/op exceeds limit of %d ns/op", duration.Nanoseconds(), limit.MaxAllowedDuration)
		}

		runtime.ReadMemStats(&memEnd)
		totalBytes += memEnd.TotalAlloc - memStart.TotalAlloc
		totalAllocs += memEnd.Mallocs - memStart.Mallocs
	}

	avgAllocs := int(totalAllocs / uint64(b.N))
	avgBytes := int(totalBytes / uint64(b.N))

	if avgAllocs > limit.MaxAllocsPerOp {
		b.Fatalf("too many allocs: %d allocs/op exceeds limit of %d", avgAllocs, limit.MaxAllocsPerOp)
	}
	if avgBytes > limit.MaxBytesPerOp {
		b.Fatalf("too much memory: %d B/op exceeds limit of %d", avgBytes, limit.MaxBytesPerOp)
	}
}
