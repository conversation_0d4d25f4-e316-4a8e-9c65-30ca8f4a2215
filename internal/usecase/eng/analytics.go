package eng

import (
	"context"
	"fmt"
	"sort"
	"sync"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/numeric"
	"github.com/Lionparcel/pentools/pkg/timex"
	"golang.org/x/sync/errgroup"
)

type analyticsRollup struct {
	RetData            *dto.EngAnalytics
	Summary            *dto.EngSummaryAnalytics
	summaryManpowerMap map[string]memberType
	MemberByID         map[string]memberType
	MpMap              map[string]memberType

	EngRole    *dto.EngAnalyticsPerRole
	EngMembers map[string]dto.EngAnalyticsPerMember

	QARole    *dto.EngAnalyticsPerRole
	QAMembers map[string]dto.EngAnalyticsPerMember

	BugsByKey map[string]model.Issue
}

// BEFORE the loop calling processEpicTasksForAnalytics
func buildBugIndex(bugs *[]model.Issue) map[string]model.Issue {
	m := make(map[string]model.Issue, len(*bugs))
	for _, b := range *bugs {
		m[b.Key] = b
	}
	return m
}

func (u *Usecase) GetEngAnalytics(ctx context.Context,
	req dto.GetEngRequest) (*dto.GetEngAnalyticsResponse, error) {
	var (
		ret dto.GetEngAnalyticsResponse
	)
	ret.Summary.Bugs = make(map[string]int)
	summaryManpowerMap := make(map[string]memberType)
	memberMap, bugs, dataMap, keys, err := u.prepareEAnalyticsData(ctx, req)
	if err != nil {
		return nil, err
	}

	for _, i := range keys {
		retData := dto.EngAnalytics{
			ReleaseDate: i,
			TotalEpic:   len(dataMap[i]),
		}
		perEngineer := dto.EngAnalyticsPerRole{
			RoleName: "Engineer",
		}
		perQA := dto.EngAnalyticsPerRole{
			RoleName: "QA",
		}
		mpMap := make(map[string]memberType)
		engMap := make(map[string]dto.EngAnalyticsPerMember)
		qaMap := make(map[string]dto.EngAnalyticsPerMember)

		rollup := &analyticsRollup{
			RetData:            &retData,
			Summary:            &ret.Summary,
			summaryManpowerMap: summaryManpowerMap,
			MemberByID:         memberMap,
			MpMap:              mpMap,

			EngRole:    &perEngineer,
			EngMembers: engMap,

			QARole:    &perQA,
			QAMembers: qaMap,

			BugsByKey: buildBugIndex(bugs),
		}

		u.processEpicTasksForAnalytics(dataMap[i], rollup)

		retData.PerRoles = append(retData.PerRoles, perEngineer, perQA)

		u.finalizeRoleAndMemberAnalytics(&retData, mpMap, engMap, qaMap)

		ret.Data = append(ret.Data, retData)
	}

	u.finalizeSummaryAnalytics(&ret, summaryManpowerMap)

	return &ret, nil
}

func (*Usecase) finalizeSummaryAnalytics(ret *dto.GetEngAnalyticsResponse,
	summaryManpowerMap map[string]memberType,
) {
	ret.Summary.TotalManpower = len(summaryManpowerMap)
	ret.Summary.SPPerManpower = numeric.SafeDivideAndRound(
		ret.Summary.TotalSP, float64(ret.Summary.TotalManpower), 2,
	)
	ret.Summary.HoursPerSP = numeric.SafeDivideAndRound(
		ret.Summary.TotalHours, ret.Summary.TotalSP, 2,
	)
	ret.Summary.TotalHours = numeric.RoundToDecimal(ret.Summary.TotalHours, 2)
	for _, m := range summaryManpowerMap {
		switch m {
		case ENG:
			ret.Summary.TotalEng++
		case QA:
			ret.Summary.TotalQA++
		}
	}

	ret.Summary.SPPerEng = numeric.SafeDivideAndRound(
		ret.Summary.TotalSPEng, float64(ret.Summary.TotalEng), 2,
	)
	ret.Summary.SPPerQA = numeric.SafeDivideAndRound(
		ret.Summary.TotalSPQA, float64(ret.Summary.TotalQA), 2,
	)

	ret.Summary.EngHoursPerSP = numeric.SafeDivideAndRound(
		ret.Summary.TotalEngHours, ret.Summary.TotalSPEng, 2,
	)
	ret.Summary.QAHoursPerSP = numeric.SafeDivideAndRound(
		ret.Summary.TotalQAHours, ret.Summary.TotalSPQA, 2,
	)

	ret.Summary.TotalEngHours = numeric.RoundToDecimal(ret.Summary.TotalEngHours, 2)
	ret.Summary.TotalQAHours = numeric.RoundToDecimal(ret.Summary.TotalQAHours, 2)
}

func (*Usecase) finalizeRoleAndMemberAnalytics(retData *dto.EngAnalytics,
	mpMap map[string]memberType,
	engMap map[string]dto.EngAnalyticsPerMember,
	qaMap map[string]dto.EngAnalyticsPerMember,
) {
	retData.HourPerSp = numeric.SafeDivideAndRound(retData.TotalHour, retData.TotalSP, 2)
	retData.TotalHour = numeric.RoundToDecimal(retData.TotalHour, 2)
	retData.TotalManpower = len(mpMap)
	retData.SPPerManpower = numeric.SafeDivideAndRound(
		retData.TotalSP, float64(retData.TotalManpower), 2)

	retData.PerRoles[0].TotalManpower = len(engMap)
	retData.PerRoles[0].SPPerManpower = numeric.SafeDivideAndRound(
		retData.PerRoles[0].TotalSP, float64(retData.PerRoles[0].TotalManpower), 2)
	retData.PerRoles[0].HourPerSp = numeric.SafeDivideAndRound(retData.PerRoles[0].TotalHour,
		retData.PerRoles[0].TotalSP, 2)
	retData.PerRoles[0].TotalHour = numeric.RoundToDecimal(retData.PerRoles[0].TotalHour, 2)
	for _, em := range engMap {
		em.TotalHour = numeric.RoundToDecimal(em.TotalHour, 2)
		retData.PerRoles[0].PerMembers = append(retData.PerRoles[0].PerMembers, em)
	}

	retData.PerRoles[1].TotalManpower = len(qaMap)
	retData.PerRoles[1].SPPerManpower = numeric.SafeDivideAndRound(retData.PerRoles[1].TotalSP,
		float64(retData.PerRoles[1].TotalManpower), 2)
	retData.PerRoles[1].HourPerSp = numeric.SafeDivideAndRound(
		retData.PerRoles[1].TotalHour, retData.PerRoles[1].TotalSP, 2,
	)
	retData.PerRoles[1].TotalHour = numeric.RoundToDecimal(retData.PerRoles[1].TotalHour, 2)
	for _, qm := range qaMap {
		qm.TotalHour = numeric.RoundToDecimal(qm.TotalHour, 2)
		retData.PerRoles[1].PerMembers = append(retData.PerRoles[1].PerMembers, qm)
	}
}

func (*Usecase) processEpicTasksForAnalytics(
	epicsPerDate []model.Issue, r *analyticsRollup,
) {
	for _, epic := range epicsPerDate {
		r.Summary.TotalEpic++
		for _, task := range *epic.ChildIssue {
			accountID := task.Fields.Assignee.AccountID
			if _, ok := r.MemberByID[accountID]; !ok {
				continue
			}

			sp := task.Fields.SP()
			r.RetData.TotalSP += sp
			r.Summary.TotalSP += sp

			r.MpMap[accountID] = r.MemberByID[accountID]
			r.summaryManpowerMap[accountID] = r.MemberByID[accountID]

			switch r.MemberByID[accountID] {
			case ENG:
				h := task.StatusDurationHours(model.IssueStatusInProgress)
				r.RetData.TotalHour += h

				r.Summary.TotalHours += h
				r.Summary.TotalSPEng += sp
				r.Summary.TotalEngHours += h

				r.EngRole.TotalSP += sp
				r.EngRole.TotalHour += h

				em := r.EngMembers[accountID]
				em.MemberName = task.Fields.Assignee.DisplayName
				em.TotalSP += sp
				em.TotalHour += h
				em.HourPerSp = numeric.SafeDivideAndRound(em.TotalHour, em.TotalSP, 2)
				r.EngMembers[accountID] = em

			case QA:
				var h float64
				h += task.StatusDurationHours(model.IssueStatusInQA)

				// iterate only linked bugs; O(#linked bugs)
				for bugKey := range task.BugList() {
					if bug, ok := r.BugsByKey[bugKey]; ok {
						r.Summary.Bugs[bug.Fields.AccidentBug.Value]++
						h += bug.StatusDurationHours(model.IssueStatusRetesting)
					}
				}

				r.RetData.TotalHour += h
				r.Summary.TotalHours += h
				r.Summary.TotalQAHours += h

				r.QARole.TotalSP += sp
				r.QARole.TotalHour += h
				r.Summary.TotalSPQA += sp

				qm := r.QAMembers[accountID]
				qm.MemberName = task.Fields.Assignee.DisplayName
				qm.TotalSP += sp
				qm.TotalHour += h
				qm.HourPerSp = numeric.SafeDivideAndRound(qm.TotalHour, qm.TotalSP, 2)
				r.QAMembers[accountID] = qm
			}
		}
	}
}

func (u *Usecase) prepareEAnalyticsData(ctx context.Context,
	req dto.GetEngRequest) (map[string]memberType, *[]model.Issue, map[string][]model.Issue,
	[]string, error) {
	epics, memberMap, err := u.getEpicDataAnalytics(ctx, req)
	if err != nil {
		return nil, nil, nil, nil, err
	}

	if err := u.getChildTasks(ctx, epics, NewJqlConfig([]JiraFieldOption{
		SP, Timeframe, Parent, Assignee, IssueLinks,
	}, []JiraExpandOption{Changelog})); err != nil {
		return nil, nil, nil, nil, err
	}

	bugs, err := u.getRelatedBugsForEpicTasks(ctx, epics)
	if err != nil {
		return nil, nil, nil, nil, err
	}

	dataMap, keys := u.groupAndSortEpicsByReleaseDate(epics)
	return memberMap, bugs, dataMap, keys, nil
}

func (u *Usecase) getRelatedBugsForEpicTasks(ctx context.Context, epics *[]model.Issue) (
	*[]model.Issue, error) {
	var tasks []model.Issue
	for _, epic := range *epics {
		if epic.ChildIssue != nil {
			tasks = append(tasks, *epic.ChildIssue...)
		}
	}

	bugs, err := u.jiraService.GetRelatedBugsByTasks(
		ctx, tasks, NewJqlConfig([]JiraFieldOption{Summary, AccidentBug},
			[]JiraExpandOption{Changelog}))
	if err != nil {
		return nil, err
	}
	return bugs, nil
}

func (*Usecase) groupAndSortEpicsByReleaseDate(epics *[]model.Issue) (map[string][]model.Issue,
	[]string) {
	dataMap := make(map[string][]model.Issue)
	for _, epic := range *epics {
		releaseDate := epic.Fields.EngExpectedEndDate
		dataMap[releaseDate] = append(dataMap[releaseDate], epic)
	}

	// Sort keys (ReleaseDate strings)
	keys := make([]string, 0, len(dataMap))
	for k := range dataMap {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	return dataMap, keys
}

func (u *Usecase) getEpicDataAnalytics(ctx context.Context, req dto.GetEngRequest) (*[]model.Issue,
	map[string]memberType, error) {
	startDate, endDate := timex.GetStartDateEndDateOrDefault(req.StartDate, req.EndDate)
	var (
		epics     *[]model.Issue
		mu        sync.Mutex
		memberMap = make(map[string]memberType, 0)
	)

	g, gCtx := errgroup.WithContext(ctx)

	g.Go(func() error {
		var err error
		baseJql := fmt.Sprintf(`
		"Main Project[Checkboxes]" = Yes AND 
		Status = Done AND
		fixVersion is not empty and
		"Expected End Date[Date]" >= %s AND
		"Expected End Date[Date]" <= %s`,
			startDate, endDate,
		)
		epics, err = u.jiraService.GetRawData(gCtx, baseJql,
			NewJqlConfig(
				[]JiraFieldOption{Timeframe}, nil,
			),
		)
		return err
	})

	g.Go(func() error {
		engineers, err := u.jiraService.GetEngineer(gCtx, true)
		if err != nil {
			return err
		}
		mu.Lock()
		for _, eng := range *engineers {
			memberMap[eng.AccountID] = ENG
		}
		mu.Unlock()
		return nil
	})

	g.Go(func() error {
		qas, err := u.jiraService.GetQa(gCtx, true)
		if err != nil {
			return err
		}
		mu.Lock()
		for _, qa := range *qas {
			memberMap[qa.AccountID] = QA
		}
		mu.Unlock()
		return nil
	})

	if err := g.Wait(); err != nil {
		return nil, nil, err
	}
	return epics, memberMap, nil
}
