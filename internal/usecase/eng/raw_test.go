package eng_test

import (
	"log"
	"testing"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/internal/repository/jira"
	"github.com/Lionparcel/pentools/internal/usecase/eng"
	"github.com/Lionparcel/pentools/pkg/config"
	"github.com/stretchr/testify/suite"
)

type SuiteRawData struct {
	suite.Suite
	usecase eng.Usecase
	jira    *jira.API
	req     dto.GetEngRequest
}

func (s *SuiteRawData) SetupTest() {
	cfg, _ := config.New("../../../config/app.toml")
	s.req = dto.GetEngRequest{
		StartDate: "2025-07-01",
		EndDate:   "2025-07-31",
	}
	s.jira = jira.NewAPI(cfg)
	js := eng.NewJiraService(s.jira)
	s.usecase = *eng.New(js)
}

func (s *SuiteRawData) TestRawData() {
	resp, _ := s.usecase.GetEngAnalytics(s.T().Context(), s.req)

	log.Println(resp)
}

func TestSuiteRawData(t *testing.T) {
	suite.Run(t, new(SuiteRawData))
}
