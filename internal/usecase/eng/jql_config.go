package eng

import (
	"sort"
	"strings"
)

// JqlConfig defines the fields and expand options to include in JIRA queries.
type jqlConfig struct {
	fields map[JiraFieldOption]bool
	expand map[JiraExpandOption]bool
}

type JqlConfig = jqlConfig

func NewJqlConfig(fieldOpts []JiraFieldOption, expandOpts []JiraExpandOption) JqlConfig {
	fields := make(map[JiraFieldOption]bool, len(fieldOpts))
	for _, f := range fieldOpts {
		fields[f] = true
	}

	expand := make(map[JiraExpandOption]bool, len(expandOpts))
	for _, e := range expandOpts {
		expand[e] = true
	}

	return JqlConfig{
		fields: fields,
		expand: expand,
	}
}

func (jq *JqlConfig) AddFields(fieldOpts []JiraFieldOption) {
	for _, f := range fieldOpts {
		jq.fields[f] = true
	}
}

type JiraExpandOption string

const (
	Changelog JiraExpandOption = "changelog"
)

type JiraFieldOption string

const (
	Status      JiraFieldOption = "status"
	FixVersion  JiraFieldOption = "fixVersions"
	Assignee    JiraFieldOption = "assignee"
	Summary     JiraFieldOption = "summary"
	Reporter    JiraFieldOption = "reporter"
	Created     JiraFieldOption = "created"
	Parent      JiraFieldOption = "parent"
	IssueLinks  JiraFieldOption = "issuelinks"
	Worklog     JiraFieldOption = "worklog"
	MainProject JiraFieldOption = "customfield_10526"
	Priority    JiraFieldOption = "priority"

	FunctionBug    JiraFieldOption = "customfield_10525"
	AccidentBug    JiraFieldOption = "customfield_10156"
	SeverityBug    JiraFieldOption = "customfield_10150"
	SquadBug       JiraFieldOption = "customfield_10155"
	EnvironmentBug JiraFieldOption = "customfield_10132"
	DevAssignee    JiraFieldOption = "customfield_10165"
	AdditionalTask JiraFieldOption = "customfield_10492"
	PEAssigned     JiraFieldOption = "customfield_10033"

	Timeframe JiraFieldOption = "timeframe" // expands to 10195 + 10196
	SP        JiraFieldOption = "sp"        // expands to 5 custom fields
)

// BuildField returns a comma-separated list of JIRA fields based on the flags set.
func (jq JqlConfig) BuildField() string {
	var keys []JiraFieldOption
	for k, v := range jq.fields {
		if v {
			keys = append(keys, k)
		}
	}
	sort.SliceStable(keys, func(i, j int) bool {
		return keys[i] < keys[j]
	})

	var list []string
	for _, opt := range keys {
		switch opt {
		case SP:
			list = append(list,
				"customfield_10024",
				"customfield_10080",
				"customfield_10101",
				"customfield_10016",
				"customfield_10087",
			)
		case Timeframe:
			list = append(list, "customfield_10195", "customfield_10196")
		default:
			list = append(list, string(opt))
		}
	}

	return strings.Join(list, ", ")
}

// BuildExpand returns a comma-separated list of expand options.
func (jq JqlConfig) BuildExpand() string {
	var list []string

	for opt, enabled := range jq.expand {
		if !enabled {
			continue
		}

		switch opt {
		default:
			list = append(list, string(opt))
		}
	}

	return strings.Join(list, ", ")
}
