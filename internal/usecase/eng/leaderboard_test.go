package eng_test

import (
	"runtime"
	"testing"
	"time"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/internal/usecase/eng"
	"github.com/Lionparcel/pentools/pkg/timex"
	"github.com/stretchr/testify/suite"
)

type SuiteUsecaseLeaderboard struct {
	suite.Suite
	usecase eng.Usecase
	jira    *MockJiraRepo
	req     dto.GetEngRequest
}

func (s *SuiteUsecaseLeaderboard) MockGetEngineer() {
	s.jira.MockGetEngineer(false, MockGroupMemberResponse{Member: []model.Author{
		mockEngineer,
	}, Err: nil})
}

func (s *SuiteUsecaseLeaderboard) MockGetQA() {
	s.jira.MockGetQA(false, MockGroupMemberResponse{Member: []model.Author{
		mockQA,
	}, Err: nil})
}

func (s *SuiteUsecaseLeaderboard) MockGetDevBugByDevAssignee() {
	start, end := timex.GetStartDateAndPlusOne(s.req.StartDate, s.req.EndDate)
	s.jira.MockGetDevBugByDevAssignee(start, end, []string{mockEngineer.AccountID}, eng.JqlConfig{}, MockSearchIssueResponse{
		Issues: []model.Issue{mockBug},
		Err:    nil,
	})
}

func (s *SuiteUsecaseLeaderboard) SetupTest() {
	s.req = dto.GetEngRequest{}
	s.jira = new(MockJiraRepo)
	js := eng.NewJiraService(s.jira)
	s.usecase = *eng.New(js)
}
func (s *SuiteUsecaseLeaderboard) MockGetTaskIssuesByEngineer() {
	start, end := timex.GetStartDateEndDateOrDefault(s.req.StartDate, s.req.EndDate)
	s.jira.MockGetTaskIssuesByAssignee(start, end, []string{mockEngineer.AccountID},
		eng.NewJqlConfig(
			[]eng.JiraFieldOption{
				eng.SP,
				eng.Summary,
				eng.Timeframe,
				eng.Status,
			},
			[]eng.JiraExpandOption{
				eng.Changelog,
			},
		),
		MockSearchIssueResponse{
			Issues: []model.Issue{mockSubTask},
			Err:    nil,
		})
}
func (s *SuiteUsecaseLeaderboard) MockGetTaskIssuesByQA() {
	start, end := timex.GetStartDateEndDateOrDefault(s.req.StartDate, s.req.EndDate)
	s.jira.MockGetTaskIssuesByAssignee(start, end, []string{mockQA.AccountID},
		eng.NewJqlConfig(
			[]eng.JiraFieldOption{
				eng.SP,
				eng.Summary,
				eng.IssueLinks,
				eng.Timeframe,
				eng.Status,
			},
			[]eng.JiraExpandOption{
				eng.Changelog,
			},
		),
		MockSearchIssueResponse{
			Issues: []model.Issue{mockTaskQA},
			Err:    nil,
		})
}
func (s *SuiteUsecaseLeaderboard) MockGetRelatedBugsByTasks() {
	s.jira.MockGetRelatedBugsByTasks([]string{mockBug.Key},
		eng.NewJqlConfig(nil, []eng.JiraExpandOption{eng.Changelog}),
		MockSearchIssueResponse{
			Issues: []model.Issue{mockBug},
			Err:    nil,
		})
}

func (s *SuiteUsecaseLeaderboard) TestGetLeaderBoard() {
	s.MockGetEngineer()
	s.MockGetTaskIssuesByEngineer()
	s.MockGetDevBugByDevAssignee()
	s.MockGetQA()
	s.MockGetTaskIssuesByQA()
	s.MockGetRelatedBugsByTasks()

	expected := &dto.GetEngLeaderboardResponse{
		Engineers: []dto.EngLeaderBoard{
			{
				MemberLeaderBoard: dto.MemberLeaderBoard{
					Name:       mockEngineer.DisplayName,
					SP:         1,
					Hours:      1,
					HoursPerSP: 1,
					Tasks: []dto.TaskLeaderBoard{
						{
							Summary:           mockSubTask.Fields.Summary,
							SP:                mockSubTask.Fields.SP(),
							Hours:             1,
							HoursPerSP:        1,
							URL:               mockSubTask.TicketLink(),
							ExpectedStartDate: mockSubTask.Fields.EngExpectedStartDateAsDate(),
							ActualStartDate:   mockSubTask.ActualStartDate(false),
							ExpectedEndDate:   mockSubTask.Fields.EngExpectedEndDateAsDate(),
							ActualEndDate:     mockSubTask.ActualEndDate(false),
							Status:            mockSubTask.Fields.Status.Name,
						},
					},
				},
				Bug: 1,
			},
		},
		QAs: []dto.MemberLeaderBoard{
			{
				Name:       mockQA.DisplayName,
				SP:         1,
				Hours:      2,
				HoursPerSP: 2,
				Tasks: []dto.TaskLeaderBoard{
					{
						Summary:           mockTaskQA.Fields.Summary,
						SP:                1,
						Hours:             2,
						HoursPerSP:        2,
						URL:               mockTaskQA.TicketLink(),
						ExpectedStartDate: mockTaskQA.Fields.EngExpectedStartDateAsDate(),
						ActualStartDate:   mockTaskQA.ActualStartDate(true),
						ExpectedEndDate:   mockTaskQA.Fields.EngExpectedEndDateAsDate(),
						ActualEndDate:     mockTaskQA.ActualEndDate(true),
						Status:            mockTaskQA.Fields.Status.Name,
					},
				},
			},
		},
	}

	resp, err := s.usecase.GetLeaderboard(s.T().Context(), s.req)

	s.Equal(expected, resp)
	s.NoError(err)
}

func TestSuiteUsecaseLeaderboard(t *testing.T) {
	suite.Run(t, new(SuiteUsecaseLeaderboard))
}

func BenchmarkGetLeaderboard(b *testing.B) {
	limit := GetLimitBenchmark()
	SimulatedJiraLatency = time.Duration(limit.MaxAllowedDuration) * time.Nanosecond
	defer func() { SimulatedJiraLatency = 0 }()

	limit.MaxAllowedDuration *= 4

	var totalAllocs, totalBytes uint64
	b.ReportAllocs()

	jira := new(MockJiraRepo)
	js := eng.NewJiraService(jira)
	uc := *eng.New(js)

	s := &SuiteUsecaseLeaderboard{
		jira:    jira,
		req:     dto.GetEngRequest{},
		usecase: uc,
	}

	// Setup necessary mocks
	s.MockGetEngineer()
	s.MockGetTaskIssuesByEngineer()
	s.MockGetDevBugByDevAssignee()
	s.MockGetQA()
	s.MockGetTaskIssuesByQA()
	s.MockGetRelatedBugsByTasks()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var memStart, memEnd runtime.MemStats
		runtime.ReadMemStats(&memStart)

		start := time.Now()
		_, err := uc.GetLeaderboard(b.Context(), s.req)
		duration := time.Since(start)

		if err != nil {
			b.Fatalf("unexpected error: %v", err)
		}
		if duration.Nanoseconds() > limit.MaxAllowedDuration {
			b.Fatalf("too slow: %d ns/op exceeds limit of %d ns/op", duration.Nanoseconds(), limit.MaxAllowedDuration)
		}

		runtime.ReadMemStats(&memEnd)
		totalBytes += memEnd.TotalAlloc - memStart.TotalAlloc
		totalAllocs += memEnd.Mallocs - memStart.Mallocs
	}

	avgAllocs := int(totalAllocs / uint64(b.N))
	avgBytes := int(totalBytes / uint64(b.N))

	if avgAllocs > limit.MaxAllocsPerOp {
		b.Fatalf("too many allocs: %d allocs/op exceeds limit of %d", avgAllocs, limit.MaxAllocsPerOp)
	}
	if avgBytes > limit.MaxBytesPerOp {
		b.Fatalf("too much memory: %d B/op exceeds limit of %d", avgBytes, limit.MaxBytesPerOp)
	}
}
