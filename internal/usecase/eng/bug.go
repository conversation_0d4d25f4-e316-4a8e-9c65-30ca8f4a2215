package eng

import (
	"context"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/numeric"
	"github.com/Lionparcel/pentools/pkg/timex"
	"golang.org/x/sync/errgroup"
)

func (u *Usecase) GetBug(ctx context.Context, req dto.GetEngRequest) (*dto.GetEngBugResponse, error) {
	response := u.initiateResponse()

	prodBugs, devBugs, err := u.getAllBugs(ctx, req)
	if err != nil {
		return nil, err
	}

	u.transform(prodBugs, devBugs, &response)

	return &response, nil
}

func (*Usecase) transform(prodBugs *[]model.Issue, devBugs *[]model.Issue, response *dto.GetEngBugResponse) {
	setUndefined := func(s string) string {
		if s == "" {
			return "undefined"
		}
		return s
	}

	for _, prodBug := range *prodBugs {
		response.ProdBug.AccidentType[setUndefined(prodBug.Fields.AccidentBug.Value)]++
		response.ProdBug.Severity[setUndefined(prodBug.Fields.SeverityBug.Value)]++
		response.ProdBug.Squad[setUndefined(prodBug.Fields.Squad.Value)]++
		response.ProdBug.Function[setUndefined(prodBug.Fields.FunctionBug.Value)]++
		response.ProdBug.Bugs = append(response.ProdBug.Bugs, dto.ProdBugDetail{
			BugDetail: dto.BugDetail{
				Summary:     prodBug.Fields.Summary,
				Severity:    prodBug.Fields.SeverityBug.Value,
				AccidentBug: prodBug.Fields.AccidentBug.Value,
				ResolutionTime: numeric.SafeDivideAndRound(
					float64(prodBug.ResolutionTimeInSeconds()), 60, 2),
				Squad:       prodBug.Fields.Squad.Value,
				CreatedDate: prodBug.Fields.CreatedAsDate(),
				Url:         prodBug.TicketLink(),
			},
			FixingTime: numeric.SafeDivideAndRound(
				float64(prodBug.ResolutionFixingTimeInSeconds()), 60, 2),
			TestingTime: numeric.SafeDivideAndRound(
				float64(prodBug.ResolutionTestingTimeInSeconds()), 60, 2),
			Function: prodBug.Fields.FunctionBug.Value,
		})
	}
	response.ProdBug.Total = len(*prodBugs)

	for _, devBug := range *devBugs {
		response.DevBug.Severity[setUndefined(devBug.Fields.SeverityBug.Value)]++
		response.DevBug.Squad[setUndefined(devBug.Fields.Squad.Value)]++
		response.DevBug.AccidentType[setUndefined(devBug.Fields.AccidentBug.Value)]++
		response.DevBug.Engineer[setUndefined(devBug.Fields.DevAssignee.DisplayName)]++
		response.DevBug.Bugs = append(response.DevBug.Bugs, dto.DevBugDetail{
			BugDetail: dto.BugDetail{
				Summary:     devBug.Fields.Summary,
				Severity:    devBug.Fields.SeverityBug.Value,
				AccidentBug: devBug.Fields.AccidentBug.Value,
				ResolutionTime: numeric.SafeDivideAndRound(
					float64(devBug.ResolutionTimeInSeconds()), 60, 2),
				Squad:       devBug.Fields.Squad.Value,
				CreatedDate: devBug.Fields.CreatedAsDate(),
				Url:         devBug.TicketLink(),
			},
			Assignee: devBug.Fields.DevAssignee.DisplayName,
		})
	}

	response.DevBug.Total = len(*devBugs)
}

func (*Usecase) initiateResponse() dto.GetEngBugResponse {
	response := dto.GetEngBugResponse{
		ProdBug: dto.ProdBugResponse{
			BugResponse: dto.BugResponse{
				AccidentType: make(map[string]int),
				Severity:     make(map[string]int),
				Squad:        make(map[string]int),
			},
			Function: make(map[string]int),
		},
		DevBug: dto.DevBugResponse{
			BugResponse: dto.BugResponse{
				Severity:     make(map[string]int),
				Squad:        make(map[string]int),
				AccidentType: make(map[string]int),
			},
			Engineer: make(map[string]int),
		},
	}
	return response
}

func (u *Usecase) getAllBugs(ctx context.Context, req dto.GetEngRequest) (*[]model.Issue, *[]model.Issue, error) {
	startDate, endDate := timex.GetStartDateAndPlusOne(req.StartDate, req.EndDate)
	g, gCtx := errgroup.WithContext(ctx)
	var (
		prodBugs *[]model.Issue
		devBugs  *[]model.Issue
	)
	g.Go(func() error {
		var err error
		prodBugs, err = u.jiraService.GetProductionBug(gCtx, startDate, endDate, NewJqlConfig(
			[]JiraFieldOption{
				AccidentBug,
				SeverityBug,
				SquadBug,
				Summary,
				Worklog,
				FunctionBug,
				EnvironmentBug,
				Created,
			},
			nil,
		))
		return err
	})

	g.Go(func() error {
		var err error
		devBugs, err = u.jiraService.GetDevBugByDevAssignee(gCtx, startDate, endDate, []string{},
			NewJqlConfig(
				[]JiraFieldOption{
					SeverityBug,
					SquadBug,
					AccidentBug,
					DevAssignee,
					Summary,
					Created,
					EnvironmentBug,
				},
				[]JiraExpandOption{
					Changelog,
				},
			))
		return err
	})

	if err := g.Wait(); err != nil {
		return nil, nil, err
	}
	return prodBugs, devBugs, nil
}
