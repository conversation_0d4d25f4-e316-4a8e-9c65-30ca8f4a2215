package eng

import (
	"context"
	"sync"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/numeric"
	"github.com/Lionparcel/pentools/pkg/timex"
	"golang.org/x/sync/errgroup"
)

func (u *Usecase) GetLeaderboard(ctx context.Context, req dto.GetEngRequest) (*dto.GetEngLeaderboardResponse, error) {
	response := dto.GetEngLeaderboardResponse{}

	g, gCtx := errgroup.WithContext(ctx)

	g.Go(func() error {
		return u.getLBperRole(gCtx, req, &response, false)
	})
	g.Go(func() error {
		return u.getLBperRole(ctx, req, &response, true)
	})

	if err := g.Wait(); err != nil {
		return nil, err
	}

	return &response, nil
}

func (u *Usecase) getLBperRole(ctx context.Context, req dto.GetEngRequest, response *dto.GetEngLeaderboardResponse, isQa bool) error {
	var (
		members *[]model.Author
		err     error
	)
	if isQa {
		members, err = u.jiraService.GetQa(ctx, false)
		if err != nil {
			return err
		}
	} else {
		members, err = u.jiraService.GetEngineer(ctx, false)
		if err != nil {
			return err
		}
	}

	g, gCtx := errgroup.WithContext(ctx)
	var mu sync.Mutex

	for _, member := range *members {
		memberCopy := member
		g.Go(func() error {
			sp, hours, taskLB, bugs, err := u.getTaskAndBug(gCtx, req, memberCopy, isQa)
			if err != nil {
				return err
			}

			if isQa {
				mu.Lock()
				response.QAs = append(response.QAs, dto.MemberLeaderBoard{
					Name:       member.DisplayName,
					SP:         sp,
					Hours:      numeric.RoundToDecimal(hours, 2),
					HoursPerSP: numeric.SafeDivideAndRound(hours, sp, 2),
					Tasks:      taskLB,
				})
				mu.Unlock()
			} else {
				mu.Lock()
				response.Engineers = append(response.Engineers, dto.EngLeaderBoard{
					MemberLeaderBoard: dto.MemberLeaderBoard{
						Name:       member.DisplayName,
						SP:         sp,
						Hours:      numeric.RoundToDecimal(hours, 2),
						HoursPerSP: numeric.SafeDivideAndRound(hours, sp, 2),
						Tasks:      taskLB,
					},
					Bug: bugs,
				})
				mu.Unlock()
			}

			return nil
		})

	}

	if err := g.Wait(); err != nil {
		return err
	}
	return nil
}

func (u *Usecase) getTaskAndBug(ctx context.Context, req dto.GetEngRequest, eng model.Author, isQa bool) (float64, float64, []dto.TaskLeaderBoard, int, error) {
	var (
		sp, hours float64
		bugNumber int
		taskLB    []dto.TaskLeaderBoard
	)

	g, gCtx := errgroup.WithContext(ctx)

	g.Go(func() error {
		var err error
		sp, hours, taskLB, err = u.getTaskMetrics(gCtx, req, eng, isQa)
		return err
	})

	if !isQa {
		g.Go(func() error {
			start, end := timex.GetStartDateAndPlusOne(req.StartDate, req.EndDate)
			bugs, err := u.jiraService.GetDevBugByDevAssignee(
				ctx, start, end, []string{eng.AccountID}, JqlConfig{})
			if err != nil {
				return err
			}
			bugNumber = len(*bugs)
			return nil
		})
	}

	if err := g.Wait(); err != nil {
		return 0, 0, nil, 0, err
	}

	return sp, hours, taskLB, bugNumber, nil
}

func (u *Usecase) getTaskMetrics(ctx context.Context, req dto.GetEngRequest, members model.Author, isQa bool) (float64, float64, []dto.TaskLeaderBoard, error) {
	var (
		sp, hours float64
		taskLB    []dto.TaskLeaderBoard
		mu        sync.Mutex
	)
	startDate, endDate := timex.GetStartDateEndDateOrDefault(req.StartDate, req.EndDate)
	fields := []JiraFieldOption{SP, Summary, Timeframe, Status, PEAssigned}
	if isQa {
		fields = append(fields, IssueLinks)
	}

	tasks, err := u.jiraService.GetTaskIssuesByAssignee(
		ctx, startDate, endDate, []string{members.AccountID},
		NewJqlConfig(fields, []JiraExpandOption{Changelog}),
	)
	if err != nil {
		return 0, 0, nil, err
	}

	g, gCtx := errgroup.WithContext(ctx)

	for _, task := range *tasks {
		t := task
		sp += t.Fields.SP()

		if isQa {
			taskHours := t.StatusDurationHours(model.IssueStatusInQA)
			g.Go(func() error {
				retestHours, err := u.getRetestingHours(gCtx, t)
				if err != nil {
					return err
				}
				taskHours += retestHours

				record := makeLeaderboardEntry(t, taskHours, isQa)
				mu.Lock()
				hours += taskHours
				taskLB = append(taskLB, record)
				mu.Unlock()
				return nil
			})
		} else {
			taskHours := t.StatusDurationHours(model.IssueStatusInProgress)
			record := makeLeaderboardEntry(t, taskHours, isQa)
			hours += taskHours
			taskLB = append(taskLB, record)
		}
	}

	if isQa {
		if err := g.Wait(); err != nil {
			return 0, 0, nil, err
		}
	}

	return sp, hours, taskLB, nil
}

func (u *Usecase) getRetestingHours(ctx context.Context, task model.Issue) (float64, error) {
	taskBugs, err := u.jiraService.GetRelatedBugsByTasks(ctx, []model.Issue{task},
		NewJqlConfig(nil, []JiraExpandOption{Changelog}),
	)
	if err != nil {
		return 0, err
	}

	var bugHours float64
	for _, bug := range *taskBugs {
		bugHours += bug.StatusDurationHours(model.IssueStatusRetesting)
	}
	return bugHours, nil
}

func makeLeaderboardEntry(task model.Issue, hours float64, isQa bool) dto.TaskLeaderBoard {
	sp := task.Fields.SP()
	return dto.TaskLeaderBoard{
		Summary:           task.Fields.Summary,
		SP:                sp,
		Hours:             numeric.RoundToDecimal(hours, 2),
		HoursPerSP:        numeric.SafeDivideAndRound(hours, sp, 2),
		URL:               task.TicketLink(),
		ExpectedStartDate: task.Fields.EngExpectedStartDateAsDate(),
		ActualStartDate:   task.ActualStartDate(isQa),
		ExpectedEndDate:   task.Fields.EngExpectedEndDateAsDate(),
		ActualEndDate:     task.ActualEndDate(isQa),
		Status:            task.Fields.Status.Name,
		PEName:            task.Fields.PEAssigned.DisplayName,
	}
}
