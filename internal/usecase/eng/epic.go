package eng

import (
	"context"
	"sync"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/numeric"
	"github.com/Lionparcel/pentools/pkg/timex"
	"golang.org/x/sync/errgroup"
)

type memberType string

const (
	ENG memberType = "ENG"
	QA  memberType = "QA"
)

type taskResponseParam struct {
	SP, SPEng, SPQA        *float64
	DevHours, TestingHours *float64
	RetestHours            *float64
	OntimeTask             *int
	TaskResponse           *dto.Task
	FromProduct, FromSA    *int
}

func (u *Usecase) GetEpic(ctx context.Context, req dto.GetEngEpicRequest) (*dto.GetEngEpicResponse, error) {
	response := &dto.GetEngEpicResponse{}

	epics, memberMap, err := u.getEpicData(ctx, req)
	if err != nil {
		return nil, err
	}

	if err := u.getChildTasks(ctx, epics, NewJqlConfig(
		[]JiraFieldOption{
			Summary,
			Parent,
			Timeframe,
			SP,
			Assignee,
			IssueLinks,
			AdditionalTask,
			Status,
		}, []JiraExpandOption{Changelog},
	)); err != nil {
		return nil, err
	}

	if err := u.calculateMetricsPerEpic(ctx, epics, memberMap, response); err != nil {
		return nil, err
	}

	if err := u.calculateMetrics(response); err != nil {
		return nil, err
	}

	return response, nil
}

func (u *Usecase) calculateMetricsPerEpic(
	ctx context.Context,
	epics *[]model.Issue,
	memberMap map[string]memberType,
	response *dto.GetEngEpicResponse,
) error {
	g, gCtx := errgroup.WithContext(ctx)

	for _, epic := range *epics {
		epicGo := epic // capture loop variable
		g.Go(func() error {
			var bugs *[]model.Issue
			var err error
			if epic.ChildIssue != nil {
				bugs, err = u.jiraService.GetRelatedBugsByTasks(gCtx, *epic.ChildIssue,
					NewJqlConfig(
						[]JiraFieldOption{AccidentBug},
						[]JiraExpandOption{Changelog},
					),
				)
				if err != nil {
					return err
				}
			}
			return u.generateEpicMetrics(epicGo, memberMap, *bugs, response)
		})
	}
	if err := g.Wait(); err != nil {
		return err
	}
	return nil
}

func (u *Usecase) generateEpicMetrics(
	epic model.Issue,
	memberMap map[string]memberType,
	bugs []model.Issue,
	response *dto.GetEngEpicResponse,
) error {
	var (
		tasks                                                []model.Issue
		totalTicket, fromProduct, fromSA, ontimeTask, devBug int
		sp, devHours, testingHours, spEng, spQA, retestHours float64
	)

	if epic.ChildIssue != nil {
		totalTicket = len(*epic.ChildIssue)
		tasks = append(tasks, *epic.ChildIssue...)
	}

	tasksResponse, err := u.buildTasksResponse(
		tasks,
		memberMap,
		bugs, &taskResponseParam{
			SP:           &sp,
			SPEng:        &spEng,
			SPQA:         &spQA,
			DevHours:     &devHours,
			TestingHours: &testingHours,
			RetestHours:  &retestHours,
			OntimeTask:   &ontimeTask,
			FromProduct:  &fromProduct,
			FromSA:       &fromSA,
		})
	if err != nil {
		return err
	}

	for _, bug := range bugs {
		if bug.IsEngineeringBug() {
			devBug++
		}
	}
	var fixVersion string
	if len(epic.Fields.FixVersion) > 0 {
		fixVersion = epic.Fields.FixVersion[0].Name
	}

	response.Epics = append(response.Epics, dto.Epic{
		Name:                epic.Fields.Summary,
		Reporter:            epic.Fields.Reporter.DisplayName,
		GroomingDate:        epic.Fields.EngExpectedStartDateAsDate(),
		ExpectedReleaseDate: epic.Fields.EngExpectedEndDateAsDate(),
		TotalTicket:         totalTicket,
		AddFromProduct:      fromProduct,
		AddFromSA:           fromSA,
		TotalSP:             sp,
		TotalHours:          numeric.RoundToDecimal(devHours+testingHours+retestHours, 2),
		HoursPerSP:          numeric.SafeDivideAndRound(devHours+testingHours+retestHours, sp, 2),
		TotalBug:            devBug,
		RetestHours:         numeric.RoundToDecimal(retestHours, 2),
		TestingHours:        numeric.RoundToDecimal(testingHours+retestHours, 2),
		URL:                 epic.TicketLink(),
		SPEngineer:          spEng,
		SPQA:                spQA,
		OntimeTask:          ontimeTask,
		Status:              epic.Fields.Status.Name,
		FixVersion:          fixVersion,
		Tasks:               tasksResponse,
	})
	return nil
}

func (u *Usecase) buildTasksResponse(tasks []model.Issue, memberMap map[string]memberType, bugs []model.Issue, param *taskResponseParam) ([]dto.Task, error) {
	tasksResponse := []dto.Task{}
	for _, task := range tasks {

		taskResponse := u.generateTaskResponse(task)
		param.TaskResponse = &taskResponse
		*param.SP += task.Fields.SP()

		if err := u.metricsByMemberType(
			task,
			memberMap,
			bugs,
			param,
		); err != nil {
			return nil, err
		}

		u.metricsAdditionalTask(&taskResponse, task, param.FromProduct, param.FromSA)

		taskResponse.Hours = numeric.RoundToDecimal(taskResponse.Hours, 2)

		tasksResponse = append(tasksResponse, taskResponse)
	}

	return tasksResponse, nil
}

func (*Usecase) metricsAdditionalTask(taskResponse *dto.Task, task model.Issue, fromProduct *int, fromSA *int) {
	taskResponse.Additional = task.Fields.AdditionalTask.Value
	switch taskResponse.Additional {
	case "From Product":
		*fromProduct++
	case "From SA":
		*fromSA++
	}
}

func (*Usecase) metricsByMemberType(
	task model.Issue,
	memberMap map[string]memberType,
	bugs []model.Issue,
	param *taskResponseParam,
) error {
	bugList := task.BugList()
	var isQa bool
	switch memberMap[task.Fields.Assignee.AccountID] {
	case ENG:
		*param.SPEng += task.Fields.SP()
		*param.DevHours += task.StatusDurationHours(model.IssueStatusInProgress)
		param.TaskResponse.Hours = task.StatusDurationHours(model.IssueStatusInProgress)
		for _, bug := range bugs {
			if bugList[bug.Key] && bug.IsEngineeringBug() {
				param.TaskResponse.Bug++
			}
		}
	case QA:
		isQa = true
		*param.SPQA += task.Fields.SP()
		*param.TestingHours += task.StatusDurationHours(model.IssueStatusInQA)
		param.TaskResponse.Hours = task.StatusDurationHours(model.IssueStatusInQA)
		for _, bug := range bugs {
			if bugList[bug.Key] {
				param.TaskResponse.Hours += bug.StatusDurationHours(model.IssueStatusRetesting)
				*param.RetestHours += bug.StatusDurationHours(model.IssueStatusRetesting)
			}
		}
	}
	param.TaskResponse.ActualStartDate = task.ActualStartDate(isQa)
	param.TaskResponse.ActualEndDate = task.ActualEndDate(isQa)
	param.TaskResponse.HoursPerSP = numeric.SafeDivideAndRound(param.TaskResponse.Hours, param.TaskResponse.SP, 2)
	ontime, err := task.EngOntimeStatus(isQa)
	if err != nil {
		return err
	}
	if *ontime {
		*param.OntimeTask++
		param.TaskResponse.Ontime = "Ontime"
	} else {
		param.TaskResponse.Ontime = "Late"
	}
	return err
}

func (*Usecase) generateTaskResponse(task model.Issue) dto.Task {
	taskResponse := dto.Task{
		Key:               task.Key,
		Status:            task.Fields.Status.Name,
		Summary:           task.Fields.Summary,
		Assignee:          task.Fields.Assignee.DisplayName,
		SP:                task.Fields.SP(),
		ExpectedStartDate: task.Fields.EngExpectedStartDateAsDate(),
		ExpectedEndDate:   task.Fields.EngExpectedEndDateAsDate(),
		URL:               task.TicketLink(),
	}
	return taskResponse
}

func (u *Usecase) getEpicData(ctx context.Context, req dto.GetEngEpicRequest) (*[]model.Issue, map[string]memberType, error) {
	startDate, endDate := timex.GetStartDateEndDateOrDefault(req.StartDate, req.EndDate)
	var (
		epics     *[]model.Issue
		mu        sync.Mutex
		memberMap = make(map[string]memberType, 0)
	)

	g, gCtx := errgroup.WithContext(ctx)

	g.Go(func() error {
		var err error
		epics, err = u.jiraService.GetEpicIssue(gCtx, startDate, endDate, req.IsActiveOnly,
			NewJqlConfig(
				[]JiraFieldOption{Summary, Reporter, Timeframe, Status, FixVersion}, nil,
			),
		)
		return err
	})

	g.Go(func() error {
		engineers, err := u.jiraService.GetEngineer(gCtx, true)
		if err != nil {
			return err
		}
		mu.Lock()
		for _, eng := range *engineers {
			memberMap[eng.AccountID] = ENG
		}
		mu.Unlock()
		return nil
	})

	g.Go(func() error {
		qas, err := u.jiraService.GetQa(gCtx, true)
		if err != nil {
			return err
		}
		mu.Lock()
		for _, qa := range *qas {
			memberMap[qa.AccountID] = QA
		}
		mu.Unlock()
		return nil
	})

	if err := g.Wait(); err != nil {
		return nil, nil, err
	}
	return epics, memberMap, nil
}

func (u *Usecase) calculateMetrics(
	response *dto.GetEngEpicResponse,
) error {
	var (
		ontimeTask                                    int
		retestHours, testingHours, percentRetestHours float64
	)
	for _, epic := range response.Epics {
		response.TotalTicket += epic.TotalTicket
		response.TotalSP += epic.TotalSP
		response.TotalSPEngineer += epic.SPEngineer
		response.TotalSPQA += epic.SPQA
		ontimeTask += epic.OntimeTask
		retestHours += epic.RetestHours
		testingHours += epic.TestingHours
		percentRetestHours += numeric.SafeDivideAndRound(
			epic.RetestHours*100,
			epic.TestingHours, 2)
	}

	response.OntimePercent = numeric.SafeDivideAndRound(
		float64(ontimeTask*100), float64(response.TotalTicket), 2)
	response.AvgRetestHour = numeric.SafeDivideAndRound(
		percentRetestHours, float64(len(response.Epics)), 2)
	return nil
}

func (u *Usecase) getChildTasks(ctx context.Context, epics *[]model.Issue, jqlConfig JqlConfig) error {
	var parentKeys []string
	childMaps := make(map[string][]model.Issue)
	for _, epic := range *epics {
		parentKeys = append(parentKeys, epic.Key)
	}

	err := u.getChildChunked(ctx, parentKeys, childMaps, jqlConfig)
	if err != nil {
		return err
	}

	u.attachChildToParent(epics, childMaps)
	return nil
}

func (u *Usecase) getChildChunked(ctx context.Context, parentKeys []string, childMaps map[string][]model.Issue, jqlConfig JqlConfig) error {
	depth := 0
	const maxDepth = 2
	for len(parentKeys) > 0 && depth < maxDepth {
		currentKeys := parentKeys
		parentKeys = []string{}

		var mu sync.Mutex
		var g errgroup.Group

		// Chunk if needed to avoid API limits
		for i := 0; i < len(currentKeys); i += chunkSize {
			end := min(i+chunkSize, len(currentKeys))
			keysChunk := currentKeys[i:end]

			g.Go(func() error {
				childs, err := u.jiraService.GetChildTaskIssues(ctx, keysChunk, jqlConfig)
				if err != nil {
					return err
				}

				mu.Lock()
				defer mu.Unlock()
				u.prepareNextIteration(childs, childMaps, &parentKeys)
				return nil
			})
		}

		if err := g.Wait(); err != nil {
			return err
		}

		depth++
	}
	return nil
}

func (*Usecase) attachChildToParent(epics *[]model.Issue, childMaps map[string][]model.Issue) {
	for i := range *epics {
		epic := &(*epics)[i]
		stories := childMaps[epic.Key]

		var child []model.Issue
		for j := range stories {
			story := &stories[j]
			if story.IsValidTask() {
				child = append(child, *story)
			}
			subtasks := childMaps[story.Key]
			for k := range subtasks {
				subtask := &subtasks[k]
				if subtask.IsValidTask() {
					child = append(child, *subtask)
				}
			}
		}

		epic.ChildIssue = &child
	}
}

func (*Usecase) prepareNextIteration(childs *[]model.Issue, childMaps map[string][]model.Issue, parentKeys *[]string) {
	for _, child := range *childs {
		parentKey := child.Fields.Parentx().Key
		childMaps[parentKey] = append(childMaps[parentKey], child)
		*parentKeys = append(*parentKeys, child.Key)
	}
}
