package eng_test

import "testing"

func TestEngSuite(t *testing.T) {
	TestSuiteUsecaseSummary(t)
	TestSuiteUsecaseEpic(t)
	TestSuiteUsecaseLeaderboard(t)
	TestSuiteTestGetBug(t)
}

func BenchmarkEngUsecase(b *testing.B) {
	BenchmarkGetSummary(b)
	BenchmarkGetEpic(b)
	BenchmarkGetLeaderboard(b)
	BenchmarkGetBug(b)
}

type BenchmarkLimit struct {
	MaxAllowedDuration int64 // nanoseconds
	MaxAllocsPerOp     int
	MaxBytesPerOp      int
}

func GetLimitBenchmark() BenchmarkLimit {
	return BenchmarkLimit{
		MaxAllowedDuration: 100_000_000, // 1 second per step
		MaxAllocsPerOp:     5000,        // reasonable upper bound for mocked logic
		MaxBytesPerOp:      1_000_000,   // 1 MB per op (safe margin for complex structs)
	}
}
