package auth

import (
	"context"
	"errors"
	"time"

	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/auth"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// LoginWithPassword authenticates a user with username and password
func (uc *UseCase) LoginWithPassword(ctx context.Context, req model.PasswordLoginRequest) (*model.PasswordLoginResponse, error) {
	ctx, span := uc.tracer.Start(ctx, "auth.LoginWithPassword")
	defer span.End()

	span.SetAttributes(
		attribute.String("username", req.Username),
	)

	// Get user by username
	user, err := uc.authRepo.GetUserByUsername(ctx, req.Username)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.SetStatus(codes.Error, "user not found")
			return nil, errors.New("invalid username or password")
		}
		span.RecordError(err)
		span.SetStatus(codes.Error, "failed to get user")
		return nil, errors.New("login failed")
	}

	// Verify password first - this should be done before checking account status
	// to avoid revealing information about account state for invalid credentials
	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password))
	if err != nil {
		// Increment failed login attempts
		if updateErr := uc.incrementFailedLoginAttempts(ctx, user.ID); updateErr != nil {
			span.RecordError(updateErr)
		}

		span.SetStatus(codes.Error, "invalid password")
		return nil, errors.New("invalid username or password")
	}

	// Now check account status only after password is verified
	// Check if account is locked
	if user.LockedUntil != nil && user.LockedUntil.After(time.Now()) {
		span.SetStatus(codes.Error, "account locked")
		return nil, errors.New("account temporarily locked due to too many failed attempts")
	}

	// Check if account is active
	if !user.IsActive {
		span.SetStatus(codes.Error, "account inactive")
		return nil, errors.New("account is disabled")
	}

	// Check if user is approved by admin
	if !user.IsApproved {
		span.SetStatus(codes.Error, "account not approved")
		return nil, errors.New("account pending admin approval")
	}

	// Reset failed login attempts and update last login time
	if err := uc.resetFailedLoginAttempts(ctx, user.ID); err != nil {
		span.RecordError(err)
	}

	// Generate JWT tokens
	accessToken, err := uc.jwtManager.GenerateAccessToken(auth.JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     string(user.Role),
	})
	if err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, "failed to generate access token")
		return nil, errors.New("login failed")
	}

	refreshToken, err := uc.jwtManager.GenerateRefreshToken(auth.JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     string(user.Role),
	})
	if err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, "failed to generate refresh token")
		return nil, errors.New("login failed")
	}

	span.SetAttributes(
		attribute.Bool("webauthn_enabled", user.WebAuthnEnabled),
		attribute.String("user_role", string(user.Role)),
	)

	return &model.PasswordLoginResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		User:         *user,
	}, nil
}

// RegisterUser registers a new user with username and password (requires admin approval)
func (uc *UseCase) RegisterUser(ctx context.Context, req model.UserRegistrationRequest) (*model.User, error) {
	ctx, span := uc.tracer.Start(ctx, "auth.RegisterUser")
	defer span.End()

	span.SetAttributes(
		attribute.String("username", req.Username),
		attribute.String("display_name", req.DisplayName),
	)

	// Check if username already exists
	existingUser, err := uc.authRepo.GetUserByUsername(ctx, req.Username)
	if err == nil && existingUser != nil {
		span.SetStatus(codes.Error, "username already exists")
		return nil, errors.New("username already exists")
	}
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		span.RecordError(err)
		span.SetStatus(codes.Error, "failed to check username")
		return nil, errors.New("registration failed")
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, "failed to hash password")
		return nil, errors.New("registration failed")
	}

	// Create user (not approved by default, admin needs to approve)
	user := &model.User{
		Username:     req.Username,
		DisplayName:  req.DisplayName,
		PasswordHash: string(hashedPassword),
		Role:         model.RoleUser,
		IsApproved:   false, // Requires admin approval
		IsActive:     true,
	}

	// Save user
	if err := uc.authRepo.CreateUser(ctx, user); err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, "failed to create user")
		return nil, errors.New("registration failed")
	}

	span.SetAttributes(
		attribute.Int("user_id", int(user.ID)),
		attribute.Bool("requires_approval", true),
	)

	return user, nil
}

// GetUserByUsername retrieves a user by username
func (uc *UseCase) GetUserByUsername(ctx context.Context, username string) (*model.User, error) {
	ctx, span := uc.tracer.Start(ctx, "auth.GetUserByUsername")
	defer span.End()

	span.SetAttributes(attribute.String("username", username))

	return uc.authRepo.GetUserByUsername(ctx, username)
}

// incrementFailedLoginAttempts increments failed login attempts and locks account if needed
func (uc *UseCase) incrementFailedLoginAttempts(ctx context.Context, userID uint) error {
	ctx, span := uc.tracer.Start(ctx, "auth.incrementFailedLoginAttempts")
	defer span.End()

	const maxFailedAttempts = 5
	const lockDuration = 15 * time.Minute

	return uc.authRepo.WithTx(ctx, func(tx *gorm.DB) error {
		// Get current failed attempts
		user, err := uc.authRepo.GetUserByIDWithTx(ctx, tx, userID)
		if err != nil {
			return err
		}

		newAttempts := user.FailedLoginAttempts + 1
		var lockedUntil *time.Time

		// Lock account if too many failed attempts
		if newAttempts >= maxFailedAttempts {
			lockTime := time.Now().Add(lockDuration)
			lockedUntil = &lockTime
		}

		return uc.authRepo.UpdateUserSecurityFieldsWithTx(ctx, tx, userID, newAttempts, lockedUntil)
	})
}

// resetFailedLoginAttempts resets failed login attempts and updates last login time
func (uc *UseCase) resetFailedLoginAttempts(ctx context.Context, userID uint) error {
	ctx, span := uc.tracer.Start(ctx, "auth.resetFailedLoginAttempts")
	defer span.End()

	now := time.Now()
	return uc.authRepo.WithTx(ctx, func(tx *gorm.DB) error {
		// Reset failed attempts, clear lock, and update last login
		if err := uc.authRepo.UpdateUserSecurityFieldsWithTx(ctx, tx, userID, 0, nil); err != nil {
			return err
		}
		return uc.authRepo.UpdateUserLastLoginWithTx(ctx, tx, userID, &now)
	})
}

// ChangePassword allows a user to change their password (requires current password)
func (uc *UseCase) ChangePassword(ctx context.Context, userID uint, currentPassword, newPassword string) error {
	ctx, span := uc.tracer.Start(ctx, "auth.ChangePassword")
	defer span.End()

	span.SetAttributes(attribute.Int("user_id", int(userID)))

	// Get user
	user, err := uc.authRepo.GetUserByID(ctx, userID)
	if err != nil {
		span.RecordError(err)
		return errors.New("user not found")
	}

	// Verify current password
	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(currentPassword))
	if err != nil {
		span.SetStatus(codes.Error, "invalid current password")
		return errors.New("current password is incorrect")
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		span.RecordError(err)
		return errors.New("failed to process new password")
	}

	// Update password
	return uc.authRepo.UpdateUserPassword(ctx, userID, string(hashedPassword))
}

// EnableWebAuthn enables WebAuthn for a user after password authentication
func (uc *UseCase) EnableWebAuthn(ctx context.Context, userID uint) error {
	ctx, span := uc.tracer.Start(ctx, "auth.EnableWebAuthn")
	defer span.End()

	span.SetAttributes(attribute.Int("user_id", int(userID)))

	return uc.authRepo.UpdateUserWebAuthnStatus(ctx, userID, true)
}

// DisableWebAuthn disables WebAuthn for a user and removes all credentials
func (uc *UseCase) DisableWebAuthn(ctx context.Context, userID uint) error {
	ctx, span := uc.tracer.Start(ctx, "auth.DisableWebAuthn")
	defer span.End()

	span.SetAttributes(attribute.Int("user_id", int(userID)))

	return uc.authRepo.WithTx(ctx, func(tx *gorm.DB) error {
		// Remove all WebAuthn credentials
		if err := uc.authRepo.DeleteAllUserCredentialsWithTx(ctx, tx, userID); err != nil {
			return err
		}

		// Disable WebAuthn
		return uc.authRepo.UpdateUserWebAuthnStatusWithTx(ctx, tx, userID, false)
	})
}

// AdminChangeUserPassword allows admin to change any user's password (no current password required)
func (uc *UseCase) AdminChangeUserPassword(ctx context.Context, userID uint, newPassword string) error {
	ctx, span := uc.tracer.Start(ctx, "auth.AdminChangeUserPassword")
	defer span.End()

	span.SetAttributes(attribute.Int("user_id", int(userID)))

	// Check if user exists
	_, err := uc.authRepo.GetUserByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.SetStatus(codes.Error, "user not found")
			return errors.New("user not found")
		}
		span.RecordError(err)
		return errors.New("failed to get user")
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		span.RecordError(err)
		return errors.New("failed to process new password")
	}

	// Update password
	return uc.authRepo.UpdateUserPassword(ctx, userID, string(hashedPassword))
}
