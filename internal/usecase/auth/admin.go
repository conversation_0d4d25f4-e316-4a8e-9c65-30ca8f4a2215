package auth

import (
	"context"
	"errors"

	"github.com/Lionparcel/pentools/internal/model"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"
)

// ApproveUser approves a user for login (admin only)
func (uc *UseCase) ApproveUser(ctx context.Context, userID uint) error {
	ctx, span := uc.tracer.Start(ctx, "auth.ApproveUser")
	defer span.End()

	span.SetAttributes(attribute.Int("user_id", int(userID)))

	// Check if user exists
	user, err := uc.authRepo.GetUserByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.SetStatus(codes.Error, "user not found")
			return errors.New("user not found")
		}
		span.RecordError(err)
		span.SetStatus(codes.Error, "failed to get user")
		return errors.New("failed to approve user")
	}

	// Update approval status
	updates := map[string]interface{}{
		"is_approved": true,
	}

	if err := uc.authRepo.UpdateUser(ctx, userID, updates); err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, "failed to update user")
		return errors.New("failed to approve user")
	}

	span.SetAttributes(
		attribute.String("username", user.Username),
		attribute.Bool("approved", true),
	)

	return nil
}

// RejectUser rejects a user registration (admin only)
func (uc *UseCase) RejectUser(ctx context.Context, userID uint) error {
	ctx, span := uc.tracer.Start(ctx, "auth.RejectUser")
	defer span.End()

	span.SetAttributes(attribute.Int("user_id", int(userID)))

	// Check if user exists
	user, err := uc.authRepo.GetUserByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.SetStatus(codes.Error, "user not found")
			return errors.New("user not found")
		}
		span.RecordError(err)
		span.SetStatus(codes.Error, "failed to get user")
		return errors.New("failed to reject user")
	}

	// Update approval status - reject means both not approved and not active
	updates := map[string]interface{}{
		"is_approved": false,
		"is_active":   false,
	}

	if err := uc.authRepo.UpdateUser(ctx, userID, updates); err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, "failed to update user")
		return errors.New("failed to reject user")
	}

	span.SetAttributes(
		attribute.String("username", user.Username),
		attribute.Bool("approved", false),
		attribute.Bool("active", false),
	)

	return nil
}

// GetPendingUsers returns users pending admin approval
func (uc *UseCase) GetPendingUsers(ctx context.Context) ([]model.User, error) {
	ctx, span := uc.tracer.Start(ctx, "auth.GetPendingUsers")
	defer span.End()

	users, err := uc.authRepo.GetUsersByApprovalStatus(ctx, false)
	if err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, "failed to get pending users")
		return nil, errors.New("failed to get pending users")
	}

	span.SetAttributes(attribute.Int("pending_count", len(users)))

	return users, nil
}

// CheckUserApprovalStatus checks if a specific user is approved
func (uc *UseCase) CheckUserApprovalStatus(ctx context.Context, userID uint) (bool, error) {
	ctx, span := uc.tracer.Start(ctx, "auth.CheckUserApprovalStatus")
	defer span.End()

	span.SetAttributes(attribute.Int("user_id", int(userID)))

	user, err := uc.authRepo.GetUserByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.SetStatus(codes.Error, "user not found")
			return false, errors.New("user not found")
		}
		span.RecordError(err)
		span.SetStatus(codes.Error, "failed to get user")
		return false, errors.New("failed to check approval status")
	}

	span.SetAttributes(
		attribute.String("username", user.Username),
		attribute.Bool("is_approved", user.IsApproved),
	)

	return user.IsApproved, nil
}