package auth

import (
	"context"

	authRepo "github.com/Lionparcel/pentools/internal/repository/auth"
	"github.com/Lionparcel/pentools/pkg/auth"
	"github.com/Lionparcel/pentools/pkg/config"
	"github.com/Lionparcel/pentools/pkg/webauthn"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
)

// UseCase handles authentication business logic
type UseCase struct {
	authRepo    *authRepo.Repository
	jwtManager  *auth.JWTManager
	webAuthn    *webauthn.Service
	config      *config.Config
	tracer      trace.Tracer
}

// New creates a new authentication use case
func New(
	authRepo *authRepo.Repository,
	jwtManager *auth.JWTManager,
	webAuthn *webauthn.Service,
	config *config.Config,
) *UseCase {
	return &UseCase{
		authRepo:   authRepo,
		jwtManager: jwtManager,
		webAuthn:   webAuthn,
		config:     config,
		tracer:     otel.Tracer("auth-usecase"),
	}
}

// GetJWTManager returns the JWT manager for use in middleware
func (uc *UseCase) GetJWTManager() *auth.JWTManager {
	return uc.jwtManager
}

// GetWebAuthnService returns the WebAuthn service
func (uc *UseCase) GetWebAuthnService() *webauthn.Service {
	return uc.webAuthn
}

// WithTx executes a function within a database transaction
func (uc *UseCase) WithTx(ctx context.Context, fn func(ctx context.Context) error) error {
	return uc.authRepo.WithTx(ctx, func(tx *gorm.DB) error {
		return fn(ctx)
	})
}