package auth

import (
	"context"
	"encoding/hex"
	"fmt"
	"net/http"

	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/auth"
	"github.com/Lionparcel/pentools/pkg/webauthn"
	"github.com/go-webauthn/webauthn/protocol"
	waWebauthn "github.com/go-webauthn/webauthn/webauthn"
	"go.opentelemetry.io/otel/attribute"
)

// BeginLogin initiates WebAuthn authentication for a user
func (uc *UseCase) BeginLogin(ctx context.Context, username string) (*protocol.CredentialAssertion, *webauthn.SessionData, error) {
	ctx, span := uc.tracer.Start(ctx, "usecase.BeginLogin")
	defer span.End()

	span.SetAttributes(attribute.String("username", username))

	// Get user with credentials
	user, err := uc.authRepo.GetUserWithCredentials(ctx, username)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get user: %w", err)
	}

	if len(user.Credentials) == 0 {
		return nil, nil, fmt.Errorf("user has no registered credentials")
	}

	// Begin WebAuthn authentication
	assertion, sessionData, err := uc.webAuthn.BeginLogin(ctx, user)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to begin WebAuthn login: %w", err)
	}

	span.SetAttributes(
		attribute.Int("user_id", int(user.ID)),
		attribute.Int("credentials_count", len(user.Credentials)),
		attribute.String("session_id", sessionData.SessionID),
	)

	return assertion, sessionData, nil
}

// FinishLogin completes WebAuthn authentication
func (uc *UseCase) FinishLogin(ctx context.Context, username string, sessionData *webauthn.SessionData, request *http.Request) (*model.LoginResponse, error) {
	ctx, span := uc.tracer.Start(ctx, "usecase.FinishLogin")
	defer span.End()

	span.SetAttributes(
		attribute.String("username", username),
		attribute.String("session_id", sessionData.SessionID),
	)

	// Get user with credentials
	user, err := uc.authRepo.GetUserWithCredentials(ctx, username)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Finish login
	credential, err := uc.webAuthn.FinishLogin(ctx, user, sessionData, request)
	if err != nil {
		return nil, fmt.Errorf("failed to finish WebAuthn login: %w", err)
	}

	// Update credential usage statistics including backup flags
	err = uc.authRepo.UpdateCredentialUsageWithFlags(ctx, credential.ID, credential.Authenticator.SignCount, credential.Flags.BackupEligible, credential.Flags.BackupState)
	if err != nil {
		// Log error but don't fail the login
		span.SetAttributes(attribute.String("credential_update_error", err.Error()))
	}

	// Generate JWT tokens
	accessToken, err := uc.jwtManager.GenerateAccessToken(auth.JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     string(user.Role),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	refreshToken, err := uc.jwtManager.GenerateRefreshToken(auth.JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     string(user.Role),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	span.SetAttributes(
		attribute.Int("user_id", int(user.ID)),
		attribute.String("user_role", string(user.Role)),
		attribute.String("credential_id", hex.EncodeToString(credential.ID)),
	)

	return &model.LoginResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		User:         *user,
	}, nil
}

// BeginDiscoverableLogin initiates usernameless WebAuthn authentication
func (uc *UseCase) BeginDiscoverableLogin(ctx context.Context) (*protocol.CredentialAssertion, *webauthn.SessionData, error) {
	ctx, span := uc.tracer.Start(ctx, "usecase.BeginDiscoverableLogin")
	defer span.End()

	// Begin discoverable login (no specific user)
	assertion, sessionData, err := uc.webAuthn.BeginDiscoverableLogin(ctx)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to begin discoverable login: %w", err)
	}

	span.SetAttributes(attribute.String("session_id", sessionData.SessionID))

	return assertion, sessionData, nil
}

// FinishDiscoverableLogin completes usernameless WebAuthn authentication
func (uc *UseCase) FinishDiscoverableLogin(ctx context.Context, sessionData *webauthn.SessionData, request *http.Request) (*model.LoginResponse, error) {
	ctx, span := uc.tracer.Start(ctx, "usecase.FinishDiscoverableLogin")
	defer span.End()

	span.SetAttributes(attribute.String("session_id", sessionData.SessionID))

	// Create user handler for discoverable login
	userHandler := func(rawID, userHandle []byte) (waWebauthn.User, error) {
		// Try to find credential by rawID
		credential, err := uc.authRepo.GetCredentialByCredentialID(ctx, rawID)
		if err != nil {
			return nil, fmt.Errorf("credential not found: %w", err)
		}

		// Get user associated with the credential
		user, err := uc.authRepo.GetUserByID(ctx, credential.UserID)
		if err != nil {
			return nil, fmt.Errorf("user not found: %w", err)
		}

		return user, nil
	}

	// Complete discoverable login
	credential, err := uc.webAuthn.FinishDiscoverableLogin(ctx, sessionData, request, userHandler)
	if err != nil {
		return nil, fmt.Errorf("failed to finish discoverable login: %w", err)
	}

	// Get the user for this credential
	dbCredential, err := uc.authRepo.GetCredentialByCredentialID(ctx, credential.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get credential: %w", err)
	}

	user, err := uc.authRepo.GetUserByID(ctx, dbCredential.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Update credential usage statistics including backup flags
	err = uc.authRepo.UpdateCredentialUsageWithFlags(ctx, credential.ID, credential.Authenticator.SignCount, credential.Flags.BackupEligible, credential.Flags.BackupState)
	if err != nil {
		// Log error but don't fail the login
		span.SetAttributes(attribute.String("credential_update_error", err.Error()))
	}

	// Generate JWT tokens
	accessToken, err := uc.jwtManager.GenerateAccessToken(auth.JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     string(user.Role),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	refreshToken, err := uc.jwtManager.GenerateRefreshToken(auth.JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     string(user.Role),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	span.SetAttributes(
		attribute.Int("user_id", int(user.ID)),
		attribute.String("username", user.Username),
		attribute.String("user_role", string(user.Role)),
		attribute.String("credential_id", hex.EncodeToString(credential.ID)),
	)

	return &model.LoginResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		User:         *user,
	}, nil
}

// ValidateToken validates a JWT token and returns user information
func (uc *UseCase) ValidateToken(ctx context.Context, tokenString string) (*model.User, error) {
	ctx, span := uc.tracer.Start(ctx, "usecase.ValidateToken")
	defer span.End()

	// Validate JWT token
	claims, err := uc.jwtManager.ValidateToken(tokenString)
	if err != nil {
		return nil, fmt.Errorf("invalid token: %w", err)
	}

	// Get user from database to ensure they still exist and are active
	user, err := uc.authRepo.GetUserByID(ctx, claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	// Verify role matches (in case it was changed)
	if string(user.Role) != claims.Role {
		return nil, fmt.Errorf("user role has changed, please re-authenticate")
	}

	span.SetAttributes(
		attribute.Int("user_id", int(user.ID)),
		attribute.String("username", user.Username),
		attribute.String("user_role", string(user.Role)),
	)

	return user, nil
}

// RefreshToken generates a new token for a valid existing token
func (uc *UseCase) RefreshToken(ctx context.Context, tokenString string) (string, error) {
	ctx, span := uc.tracer.Start(ctx, "usecase.RefreshToken")
	defer span.End()

	// Validate current token first
	user, err := uc.ValidateToken(ctx, tokenString)
	if err != nil {
		return "", fmt.Errorf("cannot refresh invalid token: %w", err)
	}

	// Generate new token
	newToken, err := uc.jwtManager.RefreshToken(tokenString)
	if err != nil {
		return "", fmt.Errorf("failed to refresh token: %w", err)
	}

	span.SetAttributes(
		attribute.Int("user_id", int(user.ID)),
		attribute.String("username", user.Username),
	)

	return newToken, nil
}

// GetLoginOptions returns default WebAuthn login options
func (uc *UseCase) GetLoginOptions() []waWebauthn.LoginOption {
	config := uc.webAuthn.GetConfig()
	return config.GetDefaultLoginOptions()
}
