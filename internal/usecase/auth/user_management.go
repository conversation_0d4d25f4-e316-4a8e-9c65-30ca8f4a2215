package auth

import (
	"context"
	"fmt"
	"math"

	"github.com/Lionparcel/pentools/internal/model"
	"go.opentelemetry.io/otel/attribute"
	"golang.org/x/crypto/bcrypt"
)

// Create<PERSON>ser creates a new user (admin only)
func (uc *UseCase) CreateUser(ctx context.Context, req *model.CreateUserRequest) (*model.User, error) {
	ctx, span := uc.tracer.Start(ctx, "usecase.CreateUser")
	defer span.End()

	span.SetAttributes(
		attribute.String("username", req.Username),
		attribute.String("display_name", req.DisplayName),
		attribute.String("role", string(req.Role)),
	)

	// Check if username already exists
	exists, err := uc.authRepo.CheckUsernameExists(ctx, req.Username)
	if err != nil {
		return nil, fmt.Errorf("failed to check username: %w", err)
	}

	if exists {
		return nil, fmt.<PERSON><PERSON><PERSON>("username already exists")
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create user
	user := &model.User{
		Username:     req.Username,
		DisplayName:  req.DisplayName,
		Role:         req.Role,
		PasswordHash: string(hashedPassword),
		IsActive:     true,
		IsApproved:   true, // Admin-created users are approved by default
	}

	err = uc.authRepo.CreateUser(ctx, user)
	if err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	span.SetAttributes(attribute.Int("created_user_id", int(user.ID)))

	return user, nil
}

// GetUsers retrieves a paginated list of users (admin only)
func (uc *UseCase) GetUsers(ctx context.Context, page, limit int) (*model.UserListResponse, error) {
	ctx, span := uc.tracer.Start(ctx, "usecase.GetUsers")
	defer span.End()

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20 // Default limit
	}

	offset := (page - 1) * limit

	span.SetAttributes(
		attribute.Int("page", page),
		attribute.Int("limit", limit),
		attribute.Int("offset", offset),
	)

	users, total, err := uc.authRepo.GetUsers(ctx, offset, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get users: %w", err)
	}

	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	response := &model.UserListResponse{
		Users:      users,
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}

	span.SetAttributes(
		attribute.Int64("total_users", total),
		attribute.Int("returned_users", len(users)),
		attribute.Int("total_pages", totalPages),
	)

	return response, nil
}

// GetUser retrieves a single user by ID
func (uc *UseCase) GetUser(ctx context.Context, userID uint) (*model.User, error) {
	ctx, span := uc.tracer.Start(ctx, "usecase.GetUser")
	defer span.End()

	span.SetAttributes(attribute.Int("user_id", int(userID)))

	user, err := uc.authRepo.GetUserByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	span.SetAttributes(
		attribute.String("username", user.Username),
		attribute.String("user_role", string(user.Role)),
		attribute.Int("credentials_count", len(user.Credentials)),
	)

	return user, nil
}

// UpdateUser updates an existing user (admin only)
func (uc *UseCase) UpdateUser(ctx context.Context, userID uint, req *model.UpdateUserRequest) (*model.User, error) {
	ctx, span := uc.tracer.Start(ctx, "usecase.UpdateUser")
	defer span.End()

	span.SetAttributes(attribute.Int("user_id", int(userID)))

	// Build updates map
	updates := make(map[string]interface{})

	if req.DisplayName != nil {
		updates["display_name"] = *req.DisplayName
		span.SetAttributes(attribute.String("new_display_name", *req.DisplayName))
	}

	if req.Role != nil {
		updates["role"] = *req.Role
		span.SetAttributes(attribute.String("new_role", string(*req.Role)))
	}

	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
		span.SetAttributes(attribute.Bool("new_is_active", *req.IsActive))
	}

	if len(updates) == 0 {
		return nil, fmt.Errorf("no updates provided")
	}

	// Update user
	err := uc.authRepo.UpdateUser(ctx, userID, updates)
	if err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	// Return updated user
	user, err := uc.authRepo.GetUserByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated user: %w", err)
	}

	return user, nil
}

// DeleteUser soft deletes a user (admin only)
func (uc *UseCase) DeleteUser(ctx context.Context, userID uint) error {
	ctx, span := uc.tracer.Start(ctx, "usecase.DeleteUser")
	defer span.End()

	span.SetAttributes(attribute.Int("user_id", int(userID)))

	err := uc.authRepo.DeleteUser(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}

	return nil
}

// UpdateUserRole updates a user's role (admin only)
func (uc *UseCase) UpdateUserRole(ctx context.Context, userID uint, role model.UserRole) error {
	ctx, span := uc.tracer.Start(ctx, "usecase.UpdateUserRole")
	defer span.End()

	span.SetAttributes(
		attribute.Int("user_id", int(userID)),
		attribute.String("new_role", string(role)),
	)

	err := uc.authRepo.UpdateUserRole(ctx, userID, role)
	if err != nil {
		return fmt.Errorf("failed to update user role: %w", err)
	}

	return nil
}

// GetUserDevices retrieves all devices/credentials for a user
func (uc *UseCase) GetUserDevices(ctx context.Context, userID uint) ([]model.WebAuthnCredential, error) {
	ctx, span := uc.tracer.Start(ctx, "usecase.GetUserDevices")
	defer span.End()

	span.SetAttributes(attribute.Int("user_id", int(userID)))

	credentials, err := uc.authRepo.GetCredentialsByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user devices: %w", err)
	}

	span.SetAttributes(attribute.Int("devices_count", len(credentials)))

	return credentials, nil
}

// DeleteUserDevice removes a specific device/credential for a user
func (uc *UseCase) DeleteUserDevice(ctx context.Context, userID, credentialID uint) error {
	ctx, span := uc.tracer.Start(ctx, "usecase.DeleteUserDevice")
	defer span.End()

	span.SetAttributes(
		attribute.Int("user_id", int(userID)),
		attribute.Int("credential_id", int(credentialID)),
	)

	// Get credential to verify it belongs to the user
	credential, err := uc.authRepo.GetCredentialByCredentialIDHex(ctx, fmt.Sprintf("%d", credentialID))
	if err != nil {
		// Try to get by ID directly from credentials table
		credentials, err := uc.authRepo.GetCredentialsByUserID(ctx, userID)
		if err != nil {
			return fmt.Errorf("failed to verify credential ownership: %w", err)
		}

		found := false
		for _, cred := range credentials {
			if cred.ID == credentialID {
				found = true
				break
			}
		}

		if !found {
			return fmt.Errorf("credential not found or does not belong to user")
		}
	} else if credential.UserID != userID {
		return fmt.Errorf("credential does not belong to user")
	}

	// Check if user has other credentials (don't allow deleting last credential)
	allCredentials, err := uc.authRepo.GetCredentialsByUserID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to check user credentials: %w", err)
	}

	if len(allCredentials) <= 1 {
		return fmt.Errorf("cannot delete last credential - user would be locked out")
	}

	// Delete the credential
	err = uc.authRepo.DeleteCredential(ctx, credentialID)
	if err != nil {
		return fmt.Errorf("failed to delete credential: %w", err)
	}

	return nil
}

// GetCurrentUserProfile gets the current user's profile information
func (uc *UseCase) GetCurrentUserProfile(ctx context.Context, userID uint) (*model.User, error) {
	ctx, span := uc.tracer.Start(ctx, "usecase.GetCurrentUserProfile")
	defer span.End()

	span.SetAttributes(attribute.Int("user_id", int(userID)))

	return uc.GetUser(ctx, userID)
}

// UpdateCurrentUserProfile allows users to update their own profile
func (uc *UseCase) UpdateCurrentUserProfile(ctx context.Context, userID uint, displayName string) (*model.User, error) {
	ctx, span := uc.tracer.Start(ctx, "usecase.UpdateCurrentUserProfile")
	defer span.End()

	span.SetAttributes(
		attribute.Int("user_id", int(userID)),
		attribute.String("new_display_name", displayName),
	)

	updates := map[string]interface{}{
		"display_name": displayName,
	}

	err := uc.authRepo.UpdateUser(ctx, userID, updates)
	if err != nil {
		return nil, fmt.Errorf("failed to update profile: %w", err)
	}

	// Return updated user
	return uc.authRepo.GetUserByID(ctx, userID)
}

// GetCredentialStats returns statistics about WebAuthn credentials
func (uc *UseCase) GetCredentialStats(ctx context.Context) (map[string]interface{}, error) {
	ctx, span := uc.tracer.Start(ctx, "usecase.GetCredentialStats")
	defer span.End()

	stats, err := uc.authRepo.GetCredentialStats(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get credential stats: %w", err)
	}

	return stats, nil
}
