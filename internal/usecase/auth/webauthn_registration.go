package auth

import (
	"context"
	"encoding/hex"
	"fmt"

	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/auth"
	webauthnx "github.com/Lionparcel/pentools/pkg/webauthn"
	"github.com/go-webauthn/webauthn/protocol"
	"github.com/go-webauthn/webauthn/webauthn"
	"go.opentelemetry.io/otel/attribute"
	"gorm.io/gorm"
)

// BeginRegistration initiates WebAuthn registration for a new user
func (uc *UseCase) BeginRegistration(ctx context.Context, username, displayName string) (*protocol.CredentialCreation, *webauthnx.SessionData, error) {
	ctx, span := uc.tracer.Start(ctx, "usecase.BeginRegistration")
	defer span.End()

	span.SetAttributes(
		attribute.String("username", username),
		attribute.String("display_name", displayName),
	)

	// Check if username already exists
	exists, err := uc.authRepo.CheckUsernameExists(ctx, username)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to check username: %w", err)
	}

	if exists {
		return nil, nil, fmt.Errorf("username already exists")
	}

	// Create temporary user for WebAuthn registration
	user := &model.User{
		Username:    username,
		DisplayName: displayName,
		Role:        model.RoleUser, // Default role
		IsActive:    true,
	}

	// Begin WebAuthn registration
	creation, sessionData, err := uc.webAuthn.BeginRegistration(ctx, user)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to begin WebAuthn registration: %w", err)
	}

	span.SetAttributes(
		attribute.String("session_id", sessionData.SessionID),
		attribute.String("challenge", sessionData.Challenge),
	)

	return creation, sessionData, nil
}

// FinishRegistration completes WebAuthn registration and creates the user
func (uc *UseCase) FinishRegistration(ctx context.Context, username string, sessionData *webauthnx.SessionData, parsedResponse *protocol.ParsedCredentialCreationData) (*model.LoginResponse, error) {
	ctx, span := uc.tracer.Start(ctx, "usecase.FinishRegistration")
	defer span.End()

	span.SetAttributes(
		attribute.String("username", username),
		attribute.String("session_id", sessionData.SessionID),
	)

	var user *model.User
	var credential *webauthn.Credential

	// Execute in transaction
	err := uc.authRepo.WithTx(ctx, func(tx *gorm.DB) error {
		// Check username availability again
		exists, err := uc.authRepo.CheckUsernameExists(ctx, username)
		if err != nil {
			return fmt.Errorf("failed to check username: %w", err)
		}

		if exists {
			return fmt.Errorf("username already exists")
		}

		// Create user first
		user = &model.User{
			Username:    username,
			DisplayName: sessionData.Username, // Use display name from session
			Role:        model.RoleUser,
			IsActive:    true,
		}

		err = uc.authRepo.CreateUser(ctx, user)
		if err != nil {
			return fmt.Errorf("failed to create user: %w", err)
		}

		// Complete WebAuthn registration
		credential, err = uc.webAuthn.CreateCredential(ctx, user, sessionData, parsedResponse)
		if err != nil {
			return fmt.Errorf("failed to create WebAuthn credential: %w", err)
		}

		// Store credential in database
		webauthnCred := &model.WebAuthnCredential{
			UserID:          user.ID,
			CredentialID:    credential.ID,
			CredentialIDHex: hex.EncodeToString(credential.ID),
			PublicKey:       credential.PublicKey,
			AttestationType: credential.AttestationType,
			SignCount:       credential.Authenticator.SignCount,
			DeviceName:      "Default Device",         // Default name, can be updated later
			DeviceType:      model.DeviceTypePlatform, // Default type
			BackupEligible:  credential.Flags.BackupEligible,
			BackupState:     credential.Flags.BackupState,
		}

		if len(credential.Authenticator.AAGUID) > 0 {
			webauthnCred.AAGUID = credential.Authenticator.AAGUID
		}

		err = uc.authRepo.CreateCredential(ctx, webauthnCred)
		if err != nil {
			return fmt.Errorf("failed to store credential: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Generate JWT tokens for the new user
	accessToken, err := uc.jwtManager.GenerateAccessToken(auth.JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     string(user.Role),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	refreshToken, err := uc.jwtManager.GenerateRefreshToken(auth.JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     string(user.Role),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	span.SetAttributes(
		attribute.Int("user_id", int(user.ID)),
		attribute.String("user_role", string(user.Role)),
	)

	return &model.LoginResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		User:         *user,
	}, nil
}

// BeginDeviceRegistration initiates registration of an additional device for existing user
func (uc *UseCase) BeginDeviceRegistration(ctx context.Context, userID uint, deviceName string) (*protocol.CredentialCreation, *webauthnx.SessionData, error) {
	ctx, span := uc.tracer.Start(ctx, "usecase.BeginDeviceRegistration")
	defer span.End()

	span.SetAttributes(
		attribute.Int("user_id", int(userID)),
		attribute.String("device_name", deviceName),
	)

	// Get user with existing credentials
	user, err := uc.authRepo.GetUserByID(ctx, userID)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Begin WebAuthn registration for additional device
	creation, sessionData, err := uc.webAuthn.BeginRegistration(ctx, user)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to begin device registration: %w", err)
	}

	// Store device name in session for later use
	sessionData.Username = deviceName // Temporarily store device name

	span.SetAttributes(
		attribute.String("session_id", sessionData.SessionID),
		attribute.String("challenge", sessionData.Challenge),
	)

	return creation, sessionData, nil
}

// FinishDeviceRegistration completes registration of an additional device
func (uc *UseCase) FinishDeviceRegistration(ctx context.Context, userID uint, deviceName string, sessionData *webauthnx.SessionData, parsedResponse *protocol.ParsedCredentialCreationData) error {
	ctx, span := uc.tracer.Start(ctx, "usecase.FinishDeviceRegistration")
	defer span.End()

	span.SetAttributes(
		attribute.Int("user_id", int(userID)),
		attribute.String("device_name", deviceName),
		attribute.String("session_id", sessionData.SessionID),
	)

	// Get user with existing credentials
	user, err := uc.authRepo.GetUserByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Complete WebAuthn registration
	credential, err := uc.webAuthn.CreateCredential(ctx, user, sessionData, parsedResponse)
	if err != nil {
		return fmt.Errorf("failed to create WebAuthn credential: %w", err)
	}

	// Determine device type based on credential properties
	deviceType := model.DeviceTypePlatform
	if !credential.Flags.BackupEligible {
		deviceType = model.DeviceTypeCrossPlatform
	}

	// Store new credential in database
	webauthnCred := &model.WebAuthnCredential{
		UserID:          userID,
		CredentialID:    credential.ID,
		CredentialIDHex: hex.EncodeToString(credential.ID),
		PublicKey:       credential.PublicKey,
		AttestationType: credential.AttestationType,
		SignCount:       credential.Authenticator.SignCount,
		DeviceName:      deviceName,
		DeviceType:      deviceType,
		BackupEligible:  credential.Flags.BackupEligible,
		BackupState:     credential.Flags.BackupState,
	}

	if len(credential.Authenticator.AAGUID) > 0 {
		webauthnCred.AAGUID = credential.Authenticator.AAGUID
	}

	err = uc.authRepo.CreateCredential(ctx, webauthnCred)
	if err != nil {
		return fmt.Errorf("failed to store credential: %w", err)
	}

	span.SetAttributes(
		attribute.String("credential_id", hex.EncodeToString(credential.ID)),
		attribute.String("device_type", string(deviceType)),
	)

	return nil
}

// GetRegistrationOptions returns default WebAuthn registration options
func (uc *UseCase) GetRegistrationOptions() []webauthn.RegistrationOption {
	config := uc.webAuthn.GetConfig()
	return config.GetDefaultRegistrationOptions()
}
