package sa

import (
	"context"

	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/config"
)

type Jira interface {
	SearchIssue(ctx context.Context, req model.SearchIssueRequest) (*model.SearchIssueResponse, error)
}
type Usecase struct {
	cfg  *config.Config
	jira Jira
}

func New(cfg *config.Config, jira Jira) *Usecase {
	return &Usecase{
		cfg:  cfg,
		jira: jira,
	}
}
