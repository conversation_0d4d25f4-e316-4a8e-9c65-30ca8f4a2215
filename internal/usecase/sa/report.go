package sa

import (
	"context"
	"fmt"
	"time"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/timex"
)

func (u *Usecase) GetReport(ctx context.Context, req dto.GetSAReportRequest) (*dto.GetSAReportResponse, error) {
	startDate, endDate := timex.GetStartDateEndDateOrDefault(req.StartDate, req.EndDate)
	jql := fmt.Sprintf("project = Principal AND worklogDate >= %s AND worklogDate <= %s", startDate, endDate)
	fields := u.cfg.Jira.DefaultField

	issues, err := u.getAndBuildJiraIssues(ctx, jql, fields, startDate, endDate)
	if err != nil {
		return nil, err
	}
	return &dto.GetSAReportResponse{
		Issues: issues,
	}, nil
}

// getAndBuildJiraIssues processes the worklogIssueResp and returns a slice of JiraIssue
func (u *Usecase) getAndBuildJiraIssues(ctx context.Context, jql string, fields string, startDate, endDate string) ([]dto.JiraIssue, error) {
	worklogIssueResp, err := u.jira.SearchIssue(ctx, model.SearchIssueRequest{
		JQL:       jql,
		Fields:    fields,
		StartDate: startDate,
		EndDate:   endDate,
	})
	if err != nil {
		return nil, err
	}

	issues := []dto.JiraIssue{}
	startTime, err := time.Parse(time.DateOnly, worklogIssueResp.Params.StartDate)
	if err != nil {
		return nil, err
	}
	endTime, err := time.Parse(time.DateOnly, worklogIssueResp.Params.EndDate)
	if err != nil {
		return nil, err
	}
	startTime = timex.StartOfDay(startTime)
	endTime = timex.EndOfDay(endTime)
	for _, issue := range worklogIssueResp.Issues {
		for _, worklog := range issue.Fields.Worklog.Worklogs {
			workLogStartedTime, errParseTime := time.Parse(timex.JiraDateTimeLayout, worklog.Started)
			if errParseTime != nil {
				return nil, errParseTime
			}
			if workLogStartedTime.Before(startTime) || workLogStartedTime.After(endTime) {
				continue
			}
			issues = append(issues, dto.JiraIssue{
				Name:                  worklog.Author.DisplayName,
				UserID:                worklog.Author.AccountID,
				Type:                  issue.Fields.Issuetype.Name,
				IssueName:             issue.Fields.Summary,
				LinkTicket:            issue.TicketLink(),
				SP:                    issue.Fields.SP(),
				TicketCreatedAt:       issue.Fields.Created,
				WorkLogDate:           worklog.Started,
				WorkLogTimeSpentHour:  (time.Duration(worklog.TimeSpentSeconds) * time.Second).Hours(),
				WorkLogTimeSpentHuman: worklog.TimeSpent,
				WorkLogComment:        worklog.FormattedComment(),
			})
		}
	}
	return issues, nil
}
