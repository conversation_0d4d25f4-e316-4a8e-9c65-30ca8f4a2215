# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Backend (Go)

- `make run`: Generate Swagger docs and run the Go server
- `make generate`: Generate Swagger documentation from code annotations using swag
- `make test-race`: Run tests with race condition detection using `GOMAXPROCS=4 go test -v -race -run=TestEngSuite ./internal/usecase/eng`
- `go run ./cmd/http/main.go`: Run the HTTP server directly
- Tests are located in `internal/usecase/eng/*_test.go`

### Frontend (Next.js)

- `make fe-install`: Install frontend dependencies using pnpm
- `make fe-run`: Run Next.js development server with Turbopack (`cd frontend && pnpm run dev`)
- `make fe-build`: Build frontend and copy to `cmd/http/dist` for embedding (`cd frontend && pnpm build && rm -rf ../cmd/http/dist && cp -r out ../cmd/http/dist`)
- `make fe-lint`: Run ESLint with auto-fix (`cd frontend && pnpm run lint`)
- `make run-all`: Build frontend and run full application

### Full Stack

- `make run-all`: Complete build and run pipeline (frontend build + backend run)

## Architecture Overview

### Backend Structure

This is a Go web application using Echo framework with clean architecture:

- **cmd/http/main.go**: Main entry point with embedded frontend files and API routing
- **internal/delivery/**: HTTP handlers organized by domain (eng, jira, sdet, sa)
- **internal/usecase/**: Business logic layer with domain-specific use cases
- **internal/repository/**: Data access layer (Jira API, PostgreSQL)
- **internal/dto/**: Data transfer objects for API communication
- **pkg/**: Shared utilities (config, database, logging, telemetry)

### Frontend Structure

Next.js 15 application with App Router (located in `/frontend/`):

- **src/app/**: App Router pages and layouts with domain-specific routing (eng, sdet, jira, sa)
- **src/components/**: Reusable React components with shadcn/ui, including:
  - **auth/**: Authentication components (auth-guard, login-form)
  - **performance-analysis/**: Team metrics and analytics components
  - **jira-task-maker/**: Task creation wizards and forms
  - **json-formatter/**: JSON formatting utilities
  - **ui/**: Complete shadcn/ui component library
- **src/types/**: TypeScript type definitions organized by domain (eng, bug, epic)
- **src/hooks/**: Custom React hooks for common functionality
- **src/lib/**: Utility functions and navigation helpers
- **Redux Toolkit** + **Redux Persist** for state management (auth, date filters)
- **TanStack Query** for server state and API calls
- **Tailwind CSS v4** with PostCSS for styling
- **CodeMirror** for JSON editing and syntax highlighting

### Key Integrations

- **Jira API**: Primary data source for engineering metrics, work logs, and reporting
- **PostgreSQL**: Database for SDET-specific data with GORM ORM
- **OpenTelemetry**: Comprehensive observability with Prometheus metrics, logging, and tracing
- **Swagger**: API documentation auto-generated from code annotations using swag
- **Echo Framework**: High-performance Go web framework for REST APIs
- **Go-Cache**: In-memory caching for performance optimization

### Domain Areas

- **eng/**: Engineering team performance analytics (bugs, epics, leaderboards, analytics)
- **pe/**: Product Engineering team performance analysis with team overview
- **force/**: Force team performance analysis with detailed member tracking
- **sdet/**: Software Development Engineer in Test metrics, sprint analysis, and task reporting
- **sre/**: Site Reliability Engineering team performance analysis
- **jira/**: Direct Jira integration, task creation, and reporting capabilities
- **sa/**: System Administrator tools and metrics
- **json-formatter/**: JSON formatting and validation utilities
- **login/**: Authentication and user management

### Configuration

- Configuration via TOML files in `config/` directory
- Environment-specific configs supported (local, production, etc.)
- Database connections, Jira credentials, and app settings configurable

### Testing

- Go unit tests with testify framework and comprehensive mocking
- Test suites in `internal/usecase/eng/` with full coverage of business logic
- Race condition testing with `GOMAXPROCS=4` via `make test-race`
- Test files: `*_test.go` pattern with suite-based testing approach
- Mock implementations for external dependencies (Jira API, database)
