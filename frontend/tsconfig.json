{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "noImplicitAny": false, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}}, "include": ["**/*.ts", "**/*.tsx", "../cmd/http/dist/types/**/*.ts", ".next/types/**/*.ts", "next-env.d.ts", "./../cmd/http/dist/types/**/*.ts"], "exclude": ["node_modules"]}