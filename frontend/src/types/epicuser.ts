export interface EpicUser {
  epics: EpicUserEpic[];
}

export interface EpicUserEpic {
  reporter: string;
  epic_name: string;
  epic_link: string;
  grooming_date: string;
  done_break_down_date: string;
  sp: number;
  backlog_sp: number;
  break_down_duration_hour: number;
  break_down_duration_second: number;
  break_down_duration_human: string;
  detailing_duration_hour: number;
  detailing_duration_second: number;
  detailing_duration_human: string;
  break_down_status: string;
  operation_hour: number;
  operation_hour_second: number;
  operation_hour_human: string;
  expected_start_date: string;
  expected_end_date: string;
  issues: EpicUserIssue[];
  worklogs: EpicUserWorklog[];
}

export interface EpicUserIssue {
  name: string;
  handoverTo: string;
  user_id: string;
  type: string;
  issue_name: string;
  link_ticket: string;
  status: string;
  ticket_status?: string;
  time_spent_hour: number;
  time_spent_hour_second: number;
  time_spent_human: string;
  time_spent_detailing_hour: number;
  time_spent_detailing_hour_second: number;
  time_spent_detailing_human: string;
  time_spent_operation_hour: number;
  time_spent_operation_hour_second: number;
  time_spent_operation_human: string;
  last_activity_at: string;
  expected_start_date: string;
  expected_end_date: string;
  sp: number;
  worklogs: EpicUserWorklog[];
}

export interface EpicUserWorklog {
  created_at: string;
  description: string;
  name: string;
  time_spent_hour: number;
  time_spent_human: string;
  time_spent_second: number;
  type: string;
  user_id: string;
}
