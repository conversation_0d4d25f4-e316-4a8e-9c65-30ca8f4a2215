export type TaskLeaderBoard = {
    summary: string;
    sp: number;
    hours: number;
    hours_per_sp: number;
    url: string;
    expected_start_date: string;      // ISO 8601 date string (e.g., "2025-07-05T00:00:00Z")
    actual_start_date: string | null; // nullable date
    expected_end_date: string;
    actual_end_date: string | null;
    status:            string;    
  };
  
  export type MemberLeaderBoard = {
    name: string;
    sp: number;
    hours: number;
    hours_per_sp: number;
    tasks: TaskLeaderBoard[];
  };
  
  export type EngLeaderBoard = MemberLeaderBoard & {
    bug: number;
  };
  
  export type GetEngLeaderboardResponse = {
    engineers: EngLeaderBoard[];
    qas: MemberLeaderBoard[];
  };
  