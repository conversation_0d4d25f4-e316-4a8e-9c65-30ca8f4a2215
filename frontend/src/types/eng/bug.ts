export type GetEngBugResponse = {
    prod_bug: ProdBugResponse;
    dev_bug: DevBugResponse;
  };
  
  export type ProdBugResponse = BugResponse & {
    function: Record<string, number>;
    bugs: ProdBugDetail[];
  };
  
  export type DevBugResponse = BugResponse & {
    engineer: Record<string, number>;
    bugs: DevBugDetail[];
  };
  
  export type BugResponse = {
    total: number;
    severity: Record<string, number>;
    squad: Record<string, number>;
    accident_type: Record<string, number>;
  };
  
  export type BugDetail = {
    summary: string;
    severity: string;
    accident_bug: string;
    resolution_time: number;
    squad: string;
    created_date: string;
    url: string;
  };
  
  export type DevBugDetail = BugDetail & {
    assignee: string;
  };
  
  export type ProdBugDetail = BugDetail & {
    function: string;
    fixing_time: number;
    testing_time: number;
  };
  