// types/eng/epic.ts

export type GetEngEpicResponse = {
    total_ticket: number;
    total_sp: number;
    total_sp_engineer: number;
    total_sp_qa: number;
    ontime_percent: number;
    avg_retest_hour: number;
    epics: Epic[];
  };
  
  export type Epic = {
    name: string;
    reporter: string;
    grooming_date: string; // ISO date string (from Go `time.Time`)
    expected_release_date: string;      // ISO 8601 date string (e.g., "2025-07-05T00:00:00Z")
    total_ticket: number;
    add_from_product: number;
    add_from_sa: number;
    total_sp: number;
    total_hours: number;
    hours_per_sp: number;
    total_bug: number;
    retest_hours: number;
    url: string;
    status: string;
    fix_version: string;
    tasks: Task[];
  };

  export interface Task {
    key: string;
    status: string;
    summary: string;
    assignee: string;
    sp: number;
    expected_start_date: string;      // ISO 8601 date string (e.g., "2025-07-05T00:00:00Z")
    actual_start_date: string | null; // nullable date
    expected_end_date: string;
    actual_end_date: string | null;
    ontime: string;
    hours: number;
    hour_per_sp: number;
    additional: string;
    bug: number;
    url: string;
  }
  
  