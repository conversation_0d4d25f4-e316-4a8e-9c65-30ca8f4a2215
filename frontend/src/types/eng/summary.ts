// types/eng/summary.ts

export interface GetEngSummaryResponse {
    production_bug: ProductionBug;
    epic: Epic;
    member: Member;
  }
  
  export interface ProductionBug {
    total: number;
    from_product: number;
    from_sa: number;
    from_engineer: number;
    others: number;
    not_defined: number;
  }
  
  export interface Epic {
    total: number;
    in_development: number;
    in_testing: number;
    ready_to_release: number;
  }
  
  export interface Member {
    engineer: MemberMetrics;
    qa: MemberMetrics;
  }
  
  export interface MemberMetrics {
    total: number;
    idle: number;
    hour_per_sp: number;
  }
  