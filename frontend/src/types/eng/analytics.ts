export interface GetEngAnalyticsResponse {
    summary: EngSummaryAnalytics;
    data: EngAnalytics[];
  }

  export interface EngSummaryAnalytics {
    total_epic: number;
    total_sp: number;
    total_manpower: number;
    sp_per_manpower: number;
    total_hours: number;
    hours_per_sp: number;
    total_sp_eng: number;
    total_eng: number;
    sp_per_eng: number;
    total_eng_hours: number;
    eng_hours_per_sp: number;
    total_sp_qa: number;
    total_qa: number;
    sp_per_qa: number;
    total_qa_hours: number;
    qa_hours_per_sp: number;
    bugs: Record<string, number>;
  }
  
  
  export interface EngAnalytics {
    release_date: string;
    total_epic: number;
    total_manpower: number;
    sp_per_manpower: number;
    total_sp: number;
    total_hour: number;
    hour_per_sp: number;
    per_role: EngAnalyticsPerRole[];
  }
  
  export interface EngAnalyticsPerRole {
    role_name: string;
    total_manpower: number;
    sp_per_manpower: number;
    total_sp: number;
    total_hour: number;
    hour_per_sp: number;
    per_members: EngAnalyticsPerMember[];
  }
  
  export interface EngAnalyticsPerMember {
    member_name: string;
    total_sp: number;
    total_hour: number;
    hour_per_sp: number;
  }
  