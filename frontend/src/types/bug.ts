export interface BugResponse {
  bugs: Bug[];
}

export interface Bug {
  bug_name: string;
  bug_link: string;
  qa_assignee: string;
  priority_feature: string;
  squad: string;
  environment_bug: string;
  test_case_type: string;
  accident_bug: string;
  severity_bug: string;
  total_test_case_medium: number;
  total_test_case_high: number;
  classification_issue: string;
  issue_trigger_flow: string;
  trigger_action_issue: string;
  impact_issue: string;
  issue_from_role: string;
  reporter: string;
  assignee: string;
  parent_name: string;
  parent_link: string;
  tag_release: string;
  pe_name: string;
  pe_user_id: string;
  ticket_created_at: string;
  labels: string[];
  bug_segmentation: string;
}
