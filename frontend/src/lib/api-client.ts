/**
 * Centralized API client with automatic token rotation
 * Handles 401 responses by refreshing tokens and retrying requests
 */

import { refreshAccessToken } from "./webauthn";

interface ApiResponse<T = any> {
  ok: boolean;
  status: number;
  data: T | null;
  error?: string;
}

class ApiClient {
  private isRefreshing = false;
  private refreshPromise: Promise<string> | null = null;

  /**
   * Get the current access token from localStorage
   */
  private getAccessToken(): string | null {
    return localStorage.getItem("accessToken");
  }

  /**
   * Get the current refresh token from localStorage
   */
  private getRefreshToken(): string | null {
    return localStorage.getItem("refreshToken");
  }

  /**
   * Update tokens in localStorage
   */
  private updateTokens(accessToken: string, refreshToken?: string): void {
    localStorage.setItem("accessToken", accessToken);
    if (refreshToken) {
      localStorage.setItem("refreshToken", refreshToken);
    }
  }

  /**
   * Clear tokens and redirect to login
   */
  private clearTokensAndRedirect(): void {
    localStorage.removeItem("accessToken");
    localStorage.removeItem("refreshToken");
    localStorage.removeItem("user");

    // Redirect to login page
    if (typeof window !== "undefined") {
      window.location.href = "/login";
    }
  }

  /**
   * Refresh the access token using the refresh token
   */
  private async refreshToken(): Promise<string> {
    if (this.isRefreshing && this.refreshPromise) {
      // If already refreshing, wait for the existing promise
      return this.refreshPromise;
    }

    this.isRefreshing = true;
    this.refreshPromise = this.performTokenRefresh();

    try {
      const newAccessToken = await this.refreshPromise;
      return newAccessToken;
    } finally {
      this.isRefreshing = false;
      this.refreshPromise = null;
    }
  }

  /**
   * Perform the actual token refresh
   */
  private async performTokenRefresh(): Promise<string> {
    const refreshToken = this.getRefreshToken();

    if (!refreshToken) {
      throw new Error("No refresh token available");
    }

    try {
      const response = await refreshAccessToken(refreshToken);
      this.updateTokens(response.accessToken);
      return response.accessToken;
    } catch {
      // If refresh fails, clear tokens and redirect to login
      this.clearTokensAndRedirect();
      throw new Error("Token refresh failed");
    }
  }

  /**
   * Make an authenticated API request with automatic token rotation
   */
  async request<T = any>(
    url: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const accessToken = this.getAccessToken();

    if (!accessToken) {
      this.clearTokensAndRedirect();
      return {
        ok: false,
        status: 401,
        data: null,
        error: "No access token available",
      };
    }

    // Prepare headers with authorization
    const headers = {
      "Content-Type": "application/json",
      ...options.headers,
      Authorization: `Bearer ${accessToken}`,
    };

    try {
      // Make the initial request
      const response = await fetch(url, {
        ...options,
        headers,
      });

      // If unauthorized, try to refresh token and retry
      if (response.status === 401) {
        try {
          const newAccessToken = await this.refreshToken();

          // Retry with new token
          const retryResponse = await fetch(url, {
            ...options,
            headers: {
              ...headers,
              Authorization: `Bearer ${newAccessToken}`,
            },
          });

          return this.handleResponse<T>(retryResponse);
        } catch {
          // If refresh fails, return unauthorized
          return {
            ok: false,
            status: 401,
            data: null,
            error: "Authentication failed",
          };
        }
      }

      return this.handleResponse<T>(response);
    } catch (error) {
      return {
        ok: false,
        status: 0,
        data: null,
        error: error instanceof Error ? error.message : "Network error",
      };
    }
  }

  /**
   * Handle the response and extract data/errors
   */
  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    try {
      const data = await response.json();

      if (!response.ok) {
        return {
          ok: false,
          status: response.status,
          data: null,
          error: data.error || data.message || "Request failed",
        };
      }

      return {
        ok: true,
        status: response.status,
        data,
      };
    } catch {
      return {
        ok: false,
        status: response.status,
        data: null,
        error: "Failed to parse response",
      };
    }
  }

  /**
   * Convenience methods for different HTTP methods
   */
  async get<T = any>(
    url: string,
    options?: RequestInit
  ): Promise<ApiResponse<T>> {
    return this.request<T>(url, { ...options, method: "GET" });
  }

  async post<T = any>(
    url: string,
    body?: any,
    options?: RequestInit
  ): Promise<ApiResponse<T>> {
    return this.request<T>(url, {
      ...options,
      method: "POST",
      body: body ? JSON.stringify(body) : undefined,
    });
  }

  async put<T = any>(
    url: string,
    body?: any,
    options?: RequestInit
  ): Promise<ApiResponse<T>> {
    return this.request<T>(url, {
      ...options,
      method: "PUT",
      body: body ? JSON.stringify(body) : undefined,
    });
  }

  async delete<T = any>(
    url: string,
    options?: RequestInit
  ): Promise<ApiResponse<T>> {
    return this.request<T>(url, { ...options, method: "DELETE" });
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Export the ApiResponse type for use in components
export type { ApiResponse };
