/**
 * WebAuthn Browser Utilities
 * Provides client-side WebAuthn functionality for registration and authentication
 */

import { API_BASE_URL } from "@/constants";

// API endpoints
const ENDPOINTS = {
  // Password authentication
  LOGIN_PASSWORD: `${API_BASE_URL}/api/auth/login`,
  // WebAuthn endpoints
  REGISTER_BEGIN: `${API_BASE_URL}/api/auth/webauthn/register/begin`,
  REGISTER_FINISH: `${API_BASE_URL}/api/auth/webauthn/register/finish`,
  LOGIN_BEGIN: `${API_BASE_URL}/api/auth/webauthn/login/begin`,
  LOGIN_FINISH: `${API_BASE_URL}/api/auth/webauthn/login/finish`,
  LOGIN_DISCOVERABLE: `${API_BASE_URL}/api/auth/webauthn/discoverable/begin`,
  LOGIN_DISCOVERABLE_FINISH: `${API_BASE_URL}/api/auth/webauthn/discoverable/finish`,
  // Token management
  REFRESH_TOKEN: `${API_BASE_URL}/api/auth/token/refresh`,
  LOGOUT: `${API_BASE_URL}/api/auth/logout`,
} as const;

// Types for WebAuthn operations
export interface WebAuthnRegistrationRequest {
  username: string;
  displayName: string;
  deviceName?: string;
}

export interface WebAuthnLoginRequest {
  username: string;
}

export interface PasswordLoginRequest {
  username: string;
  password: string;
}

export interface WebAuthnAuthResponse {
  accessToken: string;
  refreshToken: string;
  user: {
    id: number;
    username: string;
    displayName: string;
    role: string;
  };
}

export interface WebAuthnError {
  error: string;
  details?: string;
}

// WebAuthn Support Detection
export function isWebAuthnSupported(): boolean {
  return (
    typeof window !== "undefined" &&
    window.PublicKeyCredential !== undefined &&
    typeof window.PublicKeyCredential === "function"
  );
}

export async function isPlatformAuthenticatorAvailable(): Promise<boolean> {
  if (!isWebAuthnSupported()) return false;

  try {
    return await window.PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
  } catch {
    return false;
  }
}

// Utility functions for credential conversion
function base64URLToBuffer(base64URL: string): ArrayBuffer {
  const padding = "=".repeat((4 - (base64URL.length % 4)) % 4);
  const base64 = base64URL.replace(/-/g, "+").replace(/_/g, "/") + padding;
  const binary = window.atob(base64);
  const bytes = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i);
  }
  return bytes.buffer;
}

function bufferToBase64URL(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer);
  let binary = "";
  for (let i = 0; i < bytes.length; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return window
    .btoa(binary)
    .replace(/\+/g, "-")
    .replace(/\//g, "_")
    .replace(/=/g, "");
}

// Convert server response to WebAuthn credential creation options
export function parseCredentialCreationOptions(
  options: any
): CredentialCreationOptions {
  // Handle double nesting: if options.publicKey.publicKey exists, use that
  const publicKeyOptions = options.publicKey.publicKey || options.publicKey;

  return {
    publicKey: {
      ...publicKeyOptions,
      challenge: base64URLToBuffer(publicKeyOptions.challenge),
      user: {
        ...publicKeyOptions.user,
        id: base64URLToBuffer(publicKeyOptions.user.id),
      },
      excludeCredentials:
        publicKeyOptions.excludeCredentials &&
        publicKeyOptions.excludeCredentials.length > 0
          ? publicKeyOptions.excludeCredentials.map((cred: any) => ({
              ...cred,
              id: base64URLToBuffer(cred.id),
            }))
          : [],
    },
  };
}

// Convert server response to WebAuthn credential request options
function parseCredentialRequestOptions(options: any): CredentialRequestOptions {
  // Handle double nesting: if options.publicKey.publicKey exists, use that
  const publicKeyOptions = options.publicKey.publicKey || options.publicKey;

  return {
    publicKey: {
      ...publicKeyOptions,
      challenge: base64URLToBuffer(publicKeyOptions.challenge),
      allowCredentials:
        publicKeyOptions.allowCredentials &&
        publicKeyOptions.allowCredentials.length > 0
          ? publicKeyOptions.allowCredentials.map((cred: any) => ({
              ...cred,
              id: base64URLToBuffer(cred.id),
            }))
          : [],
    },
  };
}

// Convert PublicKeyCredential to format expected by server
export function formatCredentialForServer(
  credential: PublicKeyCredential
): any {
  const response = credential.response as
    | AuthenticatorAttestationResponse
    | AuthenticatorAssertionResponse;

  const baseFormat = {
    id: credential.id,
    type: credential.type,
    rawId: bufferToBase64URL(credential.rawId),
    response: {
      clientDataJSON: bufferToBase64URL(response.clientDataJSON),
    },
  };

  if (response instanceof AuthenticatorAttestationResponse) {
    return {
      ...baseFormat,
      response: {
        ...baseFormat.response,
        attestationObject: bufferToBase64URL(response.attestationObject),
      },
    };
  } else {
    return {
      ...baseFormat,
      response: {
        ...baseFormat.response,
        authenticatorData: bufferToBase64URL(
          (response as AuthenticatorAssertionResponse).authenticatorData
        ),
        signature: bufferToBase64URL(
          (response as AuthenticatorAssertionResponse).signature
        ),
        userHandle: (response as AuthenticatorAssertionResponse).userHandle
          ? bufferToBase64URL(
              (response as AuthenticatorAssertionResponse).userHandle!
            )
          : null,
      },
    };
  }
}

// API Helper function
async function makeRequest<T>(
  url: string,
  options: RequestInit = {}
): Promise<T> {
  const response = await fetch(url, {
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    const errorData: WebAuthnError = await response.json().catch(() => ({
      error: `HTTP ${response.status}: ${response.statusText}`,
    }));
    throw new Error(errorData.error || "Request failed");
  }

  return response.json();
}

// WebAuthn Registration Flow
export async function registerWithWebAuthn(
  request: WebAuthnRegistrationRequest
): Promise<WebAuthnAuthResponse> {
  if (!isWebAuthnSupported()) {
    throw new Error("WebAuthn is not supported on this device");
  }

  try {
    // Step 1: Begin registration
    const creationOptions = await makeRequest<any>(ENDPOINTS.REGISTER_BEGIN, {
      method: "POST",
      body: JSON.stringify(request),
    });

    // Step 2: Create credential
    const parsedOptions = parseCredentialCreationOptions(creationOptions);
    const credential = (await navigator.credentials.create(
      parsedOptions
    )) as PublicKeyCredential;

    if (!credential) {
      throw new Error("Failed to create credential");
    }

    // Step 3: Finish registration
    const credentialData = formatCredentialForServer(credential);
    const authResponse = await makeRequest<WebAuthnAuthResponse>(
      ENDPOINTS.REGISTER_FINISH,
      {
        method: "POST",
        body: JSON.stringify(credentialData),
        headers: {
          "X-Session-ID": creationOptions.sessionId || "",
        },
      }
    );

    return authResponse;
  } catch (error) {
    console.error("WebAuthn registration error:", error);
    throw error;
  }
}

// WebAuthn Login Flow
export async function loginWithWebAuthn(
  request: WebAuthnLoginRequest
): Promise<WebAuthnAuthResponse> {
  if (!isWebAuthnSupported()) {
    throw new Error("WebAuthn is not supported on this device");
  }

  try {
    // Step 1: Begin login
    const requestOptions = await makeRequest<any>(ENDPOINTS.LOGIN_BEGIN, {
      method: "POST",
      body: JSON.stringify(request),
    });

    // Step 2: Get credential
    const parsedOptions = parseCredentialRequestOptions(requestOptions);
    const credential = (await navigator.credentials.get(
      parsedOptions
    )) as PublicKeyCredential;

    if (!credential) {
      throw new Error("Failed to get credential");
    }

    // Step 3: Finish login
    const credentialData = formatCredentialForServer(credential);
    const authResponse = await makeRequest<WebAuthnAuthResponse>(
      ENDPOINTS.LOGIN_FINISH,
      {
        method: "POST",
        body: JSON.stringify(credentialData),
        headers: {
          "X-Session-ID": requestOptions.sessionId || "",
        },
      }
    );

    return authResponse;
  } catch (error) {
    console.error("WebAuthn login error:", error);
    throw error;
  }
}

// WebAuthn Discoverable Login Flow (usernameless)
export async function discoverableLogin(): Promise<WebAuthnAuthResponse> {
  if (!isWebAuthnSupported()) {
    throw new Error("WebAuthn is not supported on this device");
  }

  try {
    // Step 1: Begin discoverable login
    const requestOptions = await makeRequest<any>(
      ENDPOINTS.LOGIN_DISCOVERABLE,
      {
        method: "POST",
      }
    );

    // Step 2: Get credential without username
    const parsedOptions = parseCredentialRequestOptions(requestOptions);
    const credential = (await navigator.credentials.get(
      parsedOptions
    )) as PublicKeyCredential;

    if (!credential) {
      throw new Error("Failed to get credential");
    }

    // Step 3: Finish discoverable login
    const credentialData = formatCredentialForServer(credential);
    const authResponse = await makeRequest<WebAuthnAuthResponse>(
      ENDPOINTS.LOGIN_DISCOVERABLE_FINISH,
      {
        method: "POST",
        body: JSON.stringify(credentialData),
        headers: {
          "X-Session-ID": requestOptions.sessionId || "",
        },
      }
    );

    return authResponse;
  } catch (error) {
    console.error("WebAuthn discoverable login error:", error);
    throw error;
  }
}

// Password Authentication Flow
export async function loginWithPassword(
  request: PasswordLoginRequest
): Promise<WebAuthnAuthResponse> {
  try {
    const authResponse = await makeRequest<WebAuthnAuthResponse>(
      ENDPOINTS.LOGIN_PASSWORD,
      {
        method: "POST",
        body: JSON.stringify(request),
      }
    );

    return authResponse;
  } catch (error) {
    console.error("Password login error:", error);
    throw error;
  }
}

// Token Management
export async function refreshAccessToken(
  refreshToken: string
): Promise<{ accessToken: string }> {
  return makeRequest<{ accessToken: string }>(ENDPOINTS.REFRESH_TOKEN, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${refreshToken}`,
    },
  });
}

export async function logout(accessToken: string): Promise<void> {
  await makeRequest<void>(ENDPOINTS.LOGOUT, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
}

// Error handling utilities
export function getWebAuthnErrorMessage(error: any): string {
  if (typeof error === "string") return error;

  const errorName = error?.name || error?.error || "";
  const errorMessage = error?.message || "";

  switch (errorName) {
    case "NotSupportedError":
      return "WebAuthn is not supported on this device or browser";
    case "InvalidStateError":
      return "A credential with this device already exists for this account";
    case "NotAllowedError":
      return "User canceled the operation or the operation timed out";
    case "SecurityError":
      return "Security error occurred. Please ensure you are on a secure connection";
    case "ConstraintError":
      return "The authenticator does not meet the requirements";
    case "UnknownError":
      return "An unknown error occurred. Please try again";
    case "NetworkError":
      return "Network error occurred. Please check your connection";
    case "AbortError":
      return "Operation was aborted";
    default:
      return errorMessage || "Authentication failed. Please try again";
  }
}

// Device capability detection
export async function getDeviceCapabilities(): Promise<{
  webauthnSupported: boolean;
  platformAuthenticatorAvailable: boolean;
  conditionalMediationSupported: boolean;
}> {
  const webauthnSupported = isWebAuthnSupported();

  let platformAuthenticatorAvailable = false;
  let conditionalMediationSupported = false;

  if (webauthnSupported) {
    try {
      platformAuthenticatorAvailable = await isPlatformAuthenticatorAvailable();

      // Check for conditional mediation support (for passive/modal UX)
      conditionalMediationSupported =
        window.PublicKeyCredential &&
        "isConditionalMediationAvailable" in window.PublicKeyCredential &&
        (await (
          window.PublicKeyCredential as any
        ).isConditionalMediationAvailable());
    } catch (error) {
      console.error("Error checking capabilities:", error);
      // Fallback to false if detection fails
    }
  }

  const capabilities = {
    webauthnSupported,
    platformAuthenticatorAvailable,
    conditionalMediationSupported,
  };

  return capabilities;
}
