/**
 * User Management API Service
 * Provides functions for all user management operations
 */

import { API_BASE_URL } from "@/constants";
import { apiClient } from "../api-client";

// Types
export interface User {
  id: number;
  username: string;
  display_name: string;
  role: "admin" | "user";
  is_approved: boolean;
  is_active: boolean;
  webauthn_enabled: boolean;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
  credentials?: WebAuthnCredential[];
}

export interface WebAuthnCredential {
  id: number;
  user_id: number;
  credential_id: string;
  attestation_type: string;
  sign_count: number;
  device_name: string;
  device_type: "platform" | "cross-platform";
  backup_eligible: boolean;
  backup_state: boolean;
  created_at: string;
  last_used_at?: string;
}

export interface UserListResponse {
  users: User[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface CreateUserRequest {
  username: string;
  display_name: string;
  role: "admin" | "user";
  password: string;
}

export interface UpdateUserRequest {
  display_name?: string;
  role?: "admin" | "user";
  is_approved?: boolean;
  is_active?: boolean;
}

export interface PendingUsersResponse {
  users: User[];
  count: number;
}

// User Management API Functions

/**
 * Get all users with pagination
 */
export async function getUsers(
  page = 1,
  limit = 20
): Promise<UserListResponse> {
  const url = new URL(`${API_BASE_URL}/api/users`);
  url.searchParams.set("page", page.toString());
  url.searchParams.set("limit", limit.toString());

  const response = await apiClient.get<UserListResponse>(url.toString());

  if (!response.ok) {
    throw new Error(response.error || "Failed to fetch users");
  }

  return response.data!;
}

/**
 * Get a specific user by ID
 */
export async function getUser(userId: number): Promise<User> {
  const response = await apiClient.get<User>(
    `${API_BASE_URL}/api/users/${userId}`
  );

  if (!response.ok) {
    throw new Error(response.error || "Failed to fetch user");
  }

  return response.data!;
}

/**
 * Create a new user
 */
export async function createUser(userData: CreateUserRequest): Promise<User> {
  const response = await apiClient.post<User>(
    `${API_BASE_URL}/api/users`,
    userData
  );

  if (!response.ok) {
    throw new Error(response.error || "Failed to create user");
  }

  return response.data!;
}

/**
 * Update an existing user
 */
export async function updateUser(
  userId: number,
  userData: UpdateUserRequest
): Promise<User> {
  const response = await apiClient.put<User>(
    `${API_BASE_URL}/api/users/${userId}`,
    userData
  );

  if (!response.ok) {
    throw new Error(response.error || "Failed to update user");
  }

  return response.data!;
}

/**
 * Delete a user (soft delete)
 */
export async function deleteUser(userId: number): Promise<{ message: string }> {
  const response = await apiClient.delete<{ message: string }>(
    `${API_BASE_URL}/api/users/${userId}`
  );

  if (!response.ok) {
    throw new Error(response.error || "Failed to delete user");
  }

  return response.data!;
}

/**
 * Update user role
 */
export async function updateUserRole(
  userId: number,
  role: "admin" | "user"
): Promise<User> {
  const response = await apiClient.put<User>(
    `${API_BASE_URL}/api/users/${userId}/role`,
    { role }
  );

  if (!response.ok) {
    throw new Error(response.error || "Failed to update user role");
  }

  return response.data!;
}

/**
 * Get user's devices (admin only)
 */
export async function getUserDevices(
  userId: number
): Promise<WebAuthnCredential[]> {
  const response = await apiClient.get<WebAuthnCredential[]>(
    `${API_BASE_URL}/api/users/${userId}/devices`
  );

  if (!response.ok) {
    throw new Error(response.error || "Failed to fetch user devices");
  }

  return response.data!;
}

/**
 * Delete user's device (admin only)
 */
export async function deleteUserDevice(
  userId: number,
  deviceId: number
): Promise<{ message: string }> {
  const response = await apiClient.delete<{ message: string }>(
    `${API_BASE_URL}/api/users/${userId}/devices/${deviceId}`
  );

  if (!response.ok) {
    throw new Error(response.error || "Failed to delete user device");
  }

  return response.data!;
}

/**
 * Get pending users (for approval)
 */
export async function getPendingUsers(): Promise<PendingUsersResponse> {
  const response = await apiClient.get<PendingUsersResponse>(
    `${API_BASE_URL}/api/admin/users/pending`
  );

  if (!response.ok) {
    throw new Error(response.error || "Failed to fetch pending users");
  }

  return response.data!;
}

/**
 * Approve a pending user
 */
export async function approveUser(
  userId: number
): Promise<{ message: string }> {
  const response = await apiClient.post<{ message: string }>(
    `${API_BASE_URL}/api/admin/users/${userId}/approve`
  );

  if (!response.ok) {
    throw new Error(response.error || "Failed to approve user");
  }

  return response.data!;
}

/**
 * Reject a pending user
 */
export async function rejectUser(userId: number): Promise<{ message: string }> {
  const response = await apiClient.post<{ message: string }>(
    `${API_BASE_URL}/api/admin/users/${userId}/reject`
  );

  if (!response.ok) {
    throw new Error(response.error || "Failed to reject user");
  }

  return response.data!;
}

/**
 * Get credential statistics (admin only)
 */
export async function getCredentialStats(): Promise<any> {
  const response = await apiClient.get<any>(
    `${API_BASE_URL}/api/admin/stats/credentials`
  );

  if (!response.ok) {
    throw new Error(response.error || "Failed to fetch credential stats");
  }

  return response.data!;
}
