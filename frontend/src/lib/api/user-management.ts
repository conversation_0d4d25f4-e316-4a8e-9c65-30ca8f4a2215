/**
 * User Management API Service
 * Provides functions for all user management operations
 */

import { API_BASE_URL } from "@/constants";

// Types
export interface User {
  id: number;
  username: string;
  display_name: string;
  role: "admin" | "user";
  is_approved: boolean;
  is_active: boolean;
  webauthn_enabled: boolean;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
  credentials?: WebAuthnCredential[];
}

export interface WebAuthnCredential {
  id: number;
  user_id: number;
  credential_id: string;
  attestation_type: string;
  sign_count: number;
  device_name: string;
  device_type: "platform" | "cross-platform";
  backup_eligible: boolean;
  backup_state: boolean;
  created_at: string;
  last_used_at?: string;
}

export interface UserListResponse {
  users: User[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface CreateUserRequest {
  username: string;
  display_name: string;
  role: "admin" | "user";
  password: string;
}

export interface UpdateUserRequest {
  display_name?: string;
  role?: "admin" | "user";
  is_approved?: boolean;
  is_active?: boolean;
}

export interface PendingUsersResponse {
  users: User[];
  count: number;
}

// Helper function to get auth headers
function getAuthHeaders(): HeadersInit {
  const token = localStorage.getItem("accessToken");
  return {
    "Content-Type": "application/json",
    ...(token && { Authorization: `Bearer ${token}` }),
  };
}

// Helper function to handle API responses
async function handleResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.error || `HTTP ${response.status}: ${response.statusText}`
    );
  }
  return response.json();
}

// User Management API Functions

/**
 * Get all users with pagination
 */
export async function getUsers(
  page = 1,
  limit = 20
): Promise<UserListResponse> {
  const url = new URL(`${API_BASE_URL}/api/users`);
  url.searchParams.set("page", page.toString());
  url.searchParams.set("limit", limit.toString());

  const response = await fetch(url.toString(), {
    method: "GET",
    headers: getAuthHeaders(),
  });

  return handleResponse<UserListResponse>(response);
}

/**
 * Get a specific user by ID
 */
export async function getUser(userId: number): Promise<User> {
  const response = await fetch(`${API_BASE_URL}/api/users/${userId}`, {
    method: "GET",
    headers: getAuthHeaders(),
  });

  return handleResponse<User>(response);
}

/**
 * Create a new user
 */
export async function createUser(userData: CreateUserRequest): Promise<User> {
  const response = await fetch(`${API_BASE_URL}/api/users`, {
    method: "POST",
    headers: getAuthHeaders(),
    body: JSON.stringify(userData),
  });

  return handleResponse<User>(response);
}

/**
 * Update an existing user
 */
export async function updateUser(
  userId: number,
  userData: UpdateUserRequest
): Promise<User> {
  const response = await fetch(`${API_BASE_URL}/api/users/${userId}`, {
    method: "PUT",
    headers: getAuthHeaders(),
    body: JSON.stringify(userData),
  });

  return handleResponse<User>(response);
}

/**
 * Delete a user (soft delete)
 */
export async function deleteUser(userId: number): Promise<{ message: string }> {
  const response = await fetch(`${API_BASE_URL}/api/users/${userId}`, {
    method: "DELETE",
    headers: getAuthHeaders(),
  });

  return handleResponse<{ message: string }>(response);
}

/**
 * Update user role
 */
export async function updateUserRole(
  userId: number,
  role: "admin" | "user"
): Promise<User> {
  const response = await fetch(`${API_BASE_URL}/api/users/${userId}/role`, {
    method: "PUT",
    headers: getAuthHeaders(),
    body: JSON.stringify({ role }),
  });

  return handleResponse<User>(response);
}

/**
 * Get user's devices (admin only)
 */
export async function getUserDevices(
  userId: number
): Promise<WebAuthnCredential[]> {
  const response = await fetch(`${API_BASE_URL}/api/users/${userId}/devices`, {
    method: "GET",
    headers: getAuthHeaders(),
  });

  return handleResponse<WebAuthnCredential[]>(response);
}

/**
 * Delete user's device (admin only)
 */
export async function deleteUserDevice(
  userId: number,
  deviceId: number
): Promise<{ message: string }> {
  const response = await fetch(
    `${API_BASE_URL}/api/users/${userId}/devices/${deviceId}`,
    {
      method: "DELETE",
      headers: getAuthHeaders(),
    }
  );

  return handleResponse<{ message: string }>(response);
}

/**
 * Get pending users (for approval)
 */
export async function getPendingUsers(): Promise<PendingUsersResponse> {
  const response = await fetch(`${API_BASE_URL}/api/admin/users/pending`, {
    method: "GET",
    headers: getAuthHeaders(),
  });

  return handleResponse<PendingUsersResponse>(response);
}

/**
 * Approve a pending user
 */
export async function approveUser(
  userId: number
): Promise<{ message: string }> {
  const response = await fetch(
    `${API_BASE_URL}/api/admin/users/${userId}/approve`,
    {
      method: "POST",
      headers: getAuthHeaders(),
    }
  );

  return handleResponse<{ message: string }>(response);
}

/**
 * Reject a pending user
 */
export async function rejectUser(userId: number): Promise<{ message: string }> {
  const response = await fetch(
    `${API_BASE_URL}/api/admin/users/${userId}/reject`,
    {
      method: "POST",
      headers: getAuthHeaders(),
    }
  );

  return handleResponse<{ message: string }>(response);
}

/**
 * Get credential statistics (admin only)
 */
export async function getCredentialStats(): Promise<any> {
  const response = await fetch(`${API_BASE_URL}/api/admin/stats/credentials`, {
    method: "GET",
    headers: getAuthHeaders(),
  });

  return handleResponse<any>(response);
}
