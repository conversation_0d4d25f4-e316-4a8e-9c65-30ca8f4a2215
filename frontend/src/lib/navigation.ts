import {
  Server,
  LucideTestTubeDiagonal,
  Wrench,
  Rocket,
  Computer,
  NotebookTabsIcon,
  NotebookTextIcon,
  Braces,
  Users,
  type LucideIcon,
} from "lucide-react";

export interface NavigationItem {
  id: string;
  name: string;
  icon: LucideIcon;
  href: string;
  active: boolean;
  description?: string;
  keywords?: string[];
  category: string;
}

export const navigationItems: NavigationItem[] = [
  // Performance Analysis
  {
    id: "pe",
    name: "Platform Engineer",
    icon: Server,
    href: "/pe/performance-analysis",
    active: true,
    description: "Platform engineering performance metrics and analysis",
    keywords: [
      "platform",
      "infrastructure",
      "performance",
      "metrics",
      "server",
    ],
    category: "Performance Analysis",
  },
  {
    id: "sdet",
    name: "SDET",
    icon: LucideTestTubeDiagonal,
    href: "/sdet/performance-analysis",
    active: true,
    description: "Software Development Engineer in Test performance analysis",
    keywords: ["testing", "quality", "automation", "sdet", "performance"],
    category: "Performance Analysis",
  },
  {
    id: "sre",
    name: "<PERSON><PERSON>",
    icon: Wrench,
    href: "/sre/performance-analysis",
    active: true,
    description: "Site Reliability Engineering performance metrics",
    keywords: ["reliability", "sre", "monitoring", "performance", "operations"],
    category: "Performance Analysis",
  },
  {
    id: "force",
    name: "Force",
    icon: Rocket,
    href: "/force/performance-analysis",
    active: true,
    description: "Force team performance analysis and metrics",
    keywords: ["force", "team", "performance", "metrics", "rocket"],
    category: "Performance Analysis",
  },
  {
    id: "engineering",
    name: "Engineering",
    icon: Computer,
    href: "/eng/performance-analysis",
    active: true,
    description: "Engineering team performance analysis and insights",
    keywords: [
      "engineering",
      "development",
      "performance",
      "analysis",
      "computer",
    ],
    category: "Performance Analysis",
  },
  // Tools
  {
    id: "jira-task-maker-fe",
    name: "Jira Task Maker FE",
    icon: NotebookTabsIcon,
    href: "/jira-task-maker/frontend",
    active: true,
    description: "Frontend Jira task creation and management tool",
    keywords: ["jira", "task", "frontend", "tickets", "project management"],
    category: "Tools",
  },
  {
    id: "jira-task-maker-be",
    name: "Jira Task Maker BE",
    icon: NotebookTextIcon,
    href: "/jira-task-maker/backend",
    active: true,
    description: "Backend Jira task creation and management tool",
    keywords: ["jira", "task", "backend", "tickets", "project management"],
    category: "Tools",
  },
  {
    id: "json-formatter",
    name: "JSON Formatter",
    icon: Braces,
    href: "/json-formatter",
    active: true,
    description: "Format, validate, and beautify JSON data",
    keywords: ["json", "format", "validate", "beautify", "data", "parser"],
    category: "Tools",
  },
  // Administration
  {
    id: "user-management",
    name: "User Management",
    icon: Users,
    href: "/admin/users",
    active: true,
    description: "Manage user accounts, roles, and permissions",
    keywords: [
      "users",
      "admin",
      "management",
      "roles",
      "permissions",
      "accounts",
    ],
    category: "Administration",
  },
];

export const navigationCategories = Array.from(
  new Set(navigationItems.map((item) => item.category))
);

export function searchNavigationItems(query: string): NavigationItem[] {
  if (!query.trim()) return navigationItems.filter((item) => item.active);

  const searchTerm = query.toLowerCase().trim();

  return navigationItems
    .filter((item) => {
      if (!item.active) return false;

      // Search in name
      if (item.name.toLowerCase().includes(searchTerm)) return true;

      // Search in description
      if (item.description?.toLowerCase().includes(searchTerm)) return true;

      // Search in keywords
      if (
        item.keywords?.some((keyword) =>
          keyword.toLowerCase().includes(searchTerm)
        )
      )
        return true;

      // Search in category
      if (item.category.toLowerCase().includes(searchTerm)) return true;

      return false;
    })
    .sort((a, b) => {
      // Prioritize exact name matches
      const aNameMatch = a.name.toLowerCase().includes(searchTerm);
      const bNameMatch = b.name.toLowerCase().includes(searchTerm);

      if (aNameMatch && !bNameMatch) return -1;
      if (!aNameMatch && bNameMatch) return 1;

      // Then sort by category
      return a.category.localeCompare(b.category);
    });
}
