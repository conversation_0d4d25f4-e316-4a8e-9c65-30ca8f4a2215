"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { toast } from "sonner";
import {
  getUser,
  getUserDevices,
  deleteUserDevice,
} from "@/lib/api/user-management";
import type { User, WebAuthnCredential } from "@/lib/api/user-management";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  ArrowL<PERSON>t,
  Smartphone,
  Monitor,
  Shield,
  ShieldCheck,
  ShieldX,
  Clock,
  Trash2,
  AlertTriangle,
  Loader2,
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

export default function UserDevicesPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const userId = searchParams?.get("userId") || "";
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!userId) {
      router.push("/admin/users");
    }
  }, [userId, router]);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deviceToDelete, setDeviceToDelete] =
    useState<WebAuthnCredential | null>(null);

  // Validate userId
  const userIdNumber = userId ? parseInt(userId, 10) : null;
  const isValidUserId = Boolean(
    userIdNumber && !isNaN(userIdNumber) && userIdNumber > 0
  );

  // Fetch user details
  const { data: user, isLoading: userLoading } = useQuery<User>({
    queryKey: ["user", userIdNumber],
    queryFn: () => getUser(userIdNumber!),
    enabled: isValidUserId,
    retry: 2,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch devices
  const {
    data: devices,
    isLoading,
    error,
    refetch,
  } = useQuery<WebAuthnCredential[]>({
    queryKey: ["user-devices", userIdNumber],
    queryFn: () => getUserDevices(userIdNumber!),
    enabled: isValidUserId,
    retry: 2,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  // Delete device mutation
  const deleteDeviceMutation = useMutation({
    mutationFn: async (deviceId: number) => {
      if (!userIdNumber) {
        throw new Error("Invalid user ID");
      }
      return deleteUserDevice(userIdNumber, deviceId);
    },
    onSuccess: () => {
      // Use the validated userIdNumber for cache invalidation
      queryClient.invalidateQueries({
        queryKey: ["user-devices", userIdNumber],
      });
      toast.success("Device deleted successfully");
      setDeleteDialogOpen(false);
      setDeviceToDelete(null);
    },
    onError: (error) => {
      toast.error(
        error instanceof Error ? error.message : "Failed to delete device"
      );
      setDeleteDialogOpen(false);
      setDeviceToDelete(null);
    },
  });

  const handleDeleteClick = (device: WebAuthnCredential) => {
    setDeviceToDelete(device);
    setDeleteDialogOpen(true);
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    // Small delay to ensure proper cleanup
    setTimeout(() => {
      setDeviceToDelete(null);
    }, 100);
  };

  const handleDeleteConfirm = () => {
    if (deviceToDelete) {
      deleteDeviceMutation.mutate(deviceToDelete.id);
    }
  };

  // Early return for invalid userId
  if (!isValidUserId) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">Invalid User ID</h1>
          <p className="text-muted-foreground mt-2">
            Please provide a valid user ID.
          </p>
          <Button onClick={() => router.push("/admin/users")} className="mt-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Users
          </Button>
        </div>
      </div>
    );
  }

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType.toLowerCase()) {
      case "cross-platform":
        return <Smartphone className="w-4 h-4 text-blue-600" />;
      case "platform":
        return <Monitor className="w-4 h-4 text-green-600" />;
      default:
        return <Shield className="w-4 h-4 text-gray-600" />;
    }
  };

  const getDeviceTypeBadge = (deviceType: string) => {
    const type = deviceType.toLowerCase();
    if (type === "cross-platform") {
      return (
        <Badge
          variant="secondary"
          className="bg-blue-50 text-blue-700 border-blue-200"
        >
          Cross-Platform
        </Badge>
      );
    }
    if (type === "platform") {
      return (
        <Badge
          variant="secondary"
          className="bg-green-50 text-green-700 border-green-200"
        >
          Platform
        </Badge>
      );
    }
    return (
      <Badge variant="outline" className="text-gray-600">
        {deviceType}
      </Badge>
    );
  };

  const getBackupBadge = (backupEligible: boolean, backupState: boolean) => {
    if (!backupEligible) {
      return (
        <Badge
          variant="secondary"
          className="bg-gray-50 text-gray-600 border-gray-200"
        >
          <ShieldX className="w-3 h-3 mr-1" />
          No Backup
        </Badge>
      );
    }
    if (backupState) {
      return (
        <Badge
          variant="secondary"
          className="bg-green-50 text-green-700 border-green-200"
        >
          <ShieldCheck className="w-3 h-3 mr-1" />
          Backed Up
        </Badge>
      );
    }
    return (
      <Badge
        variant="secondary"
        className="bg-amber-50 text-amber-700 border-amber-200"
      >
        <Shield className="w-3 h-3 mr-1" />
        Not Backed Up
      </Badge>
    );
  };

  // Show loading state for user or devices
  if (userLoading || (isValidUserId && isLoading)) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center space-y-4">
            <Loader2 className="w-8 h-8 animate-spin mx-auto" />
            <p className="text-muted-foreground">Loading user information...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">User Not Found</h1>
          <p className="text-muted-foreground mt-2">
            The requested user could not be found.
          </p>
          <Button onClick={() => router.push("/admin/users")} className="mt-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Users
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={() => router.push("/admin/users")}
            className="p-2"
          >
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Smartphone className="w-6 h-6" />
              {user.username}'s Devices
            </h1>
            <p className="text-muted-foreground">
              Manage WebAuthn devices for {user.display_name}
            </p>
          </div>
        </div>
      </div>

      {/* Device Count */}
      <div className="bg-card rounded-lg border p-4">
        <div className="text-sm text-muted-foreground">Total Devices</div>
        <div className="text-2xl font-bold">
          {devices?.length || 0} device{devices?.length !== 1 ? "s" : ""}{" "}
          registered
        </div>
      </div>

      {/* Devices Table */}
      <div className="bg-card rounded-lg border">
        {isLoading ? (
          <div className="p-8">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[350px]">Device</TableHead>
                  <TableHead className="w-[160px]">Type</TableHead>
                  <TableHead className="w-[160px]">Backup Status</TableHead>
                  <TableHead className="w-[140px]">Last Used</TableHead>
                  <TableHead className="w-[140px]">Registered</TableHead>
                  <TableHead className="w-[80px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {[...Array(3)].map((_, i) => (
                  <TableRow key={`skeleton-${i}`}>
                    <TableCell>
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-3 w-20" />
                      </div>
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-6 w-20" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-6 w-24" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-20" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-20" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-8 w-8" />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <AlertTriangle className="w-12 h-12 text-amber-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Failed to Load Devices</h3>
            <p className="text-sm text-muted-foreground mb-4">
              {error instanceof Error ? error.message : "An error occurred"}
            </p>
            <Button onClick={() => refetch()} variant="outline">
              Try Again
            </Button>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[350px]">Device</TableHead>
                <TableHead className="w-[160px]">Type</TableHead>
                <TableHead className="w-[160px]">Backup Status</TableHead>
                <TableHead className="w-[140px]">Last Used</TableHead>
                <TableHead className="w-[140px]">Registered</TableHead>
                <TableHead className="w-[80px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {!devices || devices.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={6}
                    className="text-center py-12 text-muted-foreground"
                  >
                    <div className="flex flex-col items-center space-y-3">
                      <Smartphone className="w-16 h-16 text-gray-300" />
                      <div className="space-y-1">
                        <div className="font-medium">No devices registered</div>
                        <div className="text-sm">
                          This user hasn't set up any WebAuthn devices yet.
                        </div>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                devices.map((device) => (
                  <TableRow key={device.id} className="hover:bg-muted/50">
                    <TableCell className="py-4">
                      <div className="flex items-start gap-3">
                        <div className="mt-1">
                          {getDeviceIcon(device.device_type)}
                        </div>
                        <div className="space-y-1">
                          <div className="font-medium text-sm leading-none">
                            {device.device_name}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Sign count: {device.sign_count.toLocaleString()}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="py-4">
                      {getDeviceTypeBadge(device.device_type)}
                    </TableCell>
                    <TableCell className="py-4">
                      {getBackupBadge(
                        device.backup_eligible,
                        device.backup_state
                      )}
                    </TableCell>
                    <TableCell className="py-4">
                      {device.last_used_at ? (
                        <div className="flex items-center gap-1.5 text-sm">
                          <Clock className="w-3 h-3 text-muted-foreground" />
                          <span>
                            {format(
                              new Date(device.last_used_at),
                              "MMM d, yyyy"
                            )}
                          </span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground text-sm">
                          Never
                        </span>
                      )}
                    </TableCell>
                    <TableCell className="py-4">
                      <span className="text-sm text-muted-foreground">
                        {format(new Date(device.created_at), "MMM d, yyyy")}
                      </span>
                    </TableCell>
                    <TableCell className="py-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteClick(device)}
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="w-4 h-4" />
                        <span className="sr-only">Delete device</span>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={deleteDialogOpen}
        onOpenChange={(open) => {
          if (!open) {
            handleDeleteCancel();
          }
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete WebAuthn Device</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{deviceToDelete?.device_name}"?
              This action cannot be undone and the user will need to re-register
              this device.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleDeleteCancel}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-red-600 hover:bg-red-700"
              disabled={deleteDeviceMutation.isPending}
            >
              {deleteDeviceMutation.isPending ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete Device"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
