"use client";

import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import {
  UserCheck,
  UserX,
  Calendar,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Users,
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { API_BASE_URL } from "@/constants";
import { apiClient } from "@/lib/api-client";

interface User {
  id: number;
  username: string;
  display_name: string;
  role: string;
  is_approved: boolean;
  is_active: boolean;
  created_at: string;
}

interface PendingUsersResponse {
  users: User[];
  count: number;
}

export function PendingUsers() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [actionType, setActionType] = useState<"approve" | "reject" | null>(
    null
  );

  // Fetch pending users
  const {
    data: pendingData,
    isLoading,
    error,
    refetch,
  } = useQuery<PendingUsersResponse>({
    queryKey: ["pending-users"],
    queryFn: async () => {
      const response = await apiClient.get<PendingUsersResponse>(API_BASE_URL + "/api/admin/users/pending");
      
      if (!response.ok) {
        throw new Error(response.error || "Failed to fetch pending users");
      }
      
      return response.data || { users: [], count: 0 };
    },
    refetchInterval: 10000, // Refetch every 10 seconds
  });

  // Approve user mutation
  const approveUserMutation = useMutation({
    mutationFn: async (userId: number) => {
      const response = await apiClient.post(`/api/admin/users/${userId}/approve`);
      
      if (!response.ok) {
        throw new Error(response.error || "Failed to approve user");
      }

      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["pending-users"] });
      toast({
        title: "User Approved",
        description: `${selectedUser?.display_name} has been approved for login.`,
      });
      setSelectedUser(null);
      setActionType(null);
    },
    onError: () => {
      toast({
        title: "Approval Failed",
        description: "Failed to approve user. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Reject user mutation
  const rejectUserMutation = useMutation({
    mutationFn: async (userId: number) => {
      const response = await apiClient.post(`/api/admin/users/${userId}/reject`);
      
      if (!response.ok) {
        throw new Error(response.error || "Failed to reject user");
      }

      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["pending-users"] });
      toast({
        title: "User Rejected",
        description: `${selectedUser?.display_name}'s registration has been rejected.`,
      });
      setSelectedUser(null);
      setActionType(null);
    },
    onError: () => {
      toast({
        title: "Rejection Failed",
        description: "Failed to reject user. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleApproveUser = (user: User) => {
    setSelectedUser(user);
    setActionType("approve");
  };

  const handleRejectUser = (user: User) => {
    setSelectedUser(user);
    setActionType("reject");
  };

  const confirmAction = () => {
    if (!selectedUser || !actionType) return;

    if (actionType === "approve") {
      approveUserMutation.mutate(selectedUser.id);
    } else {
      rejectUserMutation.mutate(selectedUser.id);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (error) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-8">
          <AlertTriangle className="w-12 h-12 text-destructive mb-4" />
          <h3 className="text-lg font-medium mb-2">
            Error Loading Pending Users
          </h3>
          <p className="text-sm text-muted-foreground text-center mb-4">
            {error instanceof Error ? error.message : "Unknown error occurred"}
          </p>
          <Button onClick={() => refetch()} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Pending User Approvals</h2>
          <p className="text-muted-foreground">
            Review and approve new user registrations
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline">{pendingData?.count || 0} pending</Badge>
          <Button onClick={() => refetch()} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {isLoading ? (
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <RefreshCw className="w-6 h-6 animate-spin mr-2" />
            Loading pending users...
          </CardContent>
        </Card>
      ) : !pendingData?.users?.length ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Users className="w-12 h-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No Pending Approvals</h3>
            <p className="text-sm text-muted-foreground text-center">
              All user registrations have been reviewed.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {pendingData.users.map((user) => (
            <Card key={user.id}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">
                      {user.display_name}
                    </CardTitle>
                    <CardDescription>{user.username}</CardDescription>
                  </div>
                  <Badge variant="secondary">{user.role}</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>Registered: {formatDate(user.created_at)}</span>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      onClick={() => handleApproveUser(user)}
                      disabled={
                        approveUserMutation.isPending ||
                        rejectUserMutation.isPending
                      }
                    >
                      <UserCheck className="w-4 h-4 mr-2" />
                      Approve
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRejectUser(user)}
                      disabled={
                        approveUserMutation.isPending ||
                        rejectUserMutation.isPending
                      }
                    >
                      <UserX className="w-4 h-4 mr-2" />
                      Reject
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Confirmation Dialog */}
      <Dialog
        open={!!selectedUser && !!actionType}
        onOpenChange={() => {
          setSelectedUser(null);
          setActionType(null);
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              {actionType === "approve" ? (
                <CheckCircle className="w-5 h-5 text-green-600" />
              ) : (
                <XCircle className="w-5 h-5 text-red-600" />
              )}
              <span>
                {actionType === "approve" ? "Approve User" : "Reject User"}
              </span>
            </DialogTitle>
            <DialogDescription>
              {actionType === "approve"
                ? `Are you sure you want to approve ${selectedUser?.display_name}? They will be able to log in immediately.`
                : `Are you sure you want to reject ${selectedUser?.display_name}'s registration? This action cannot be undone.`}
            </DialogDescription>
          </DialogHeader>

          {selectedUser && (
            <div className="space-y-2">
              <Separator />
              <div className="text-sm space-y-1">
                <div>
                  <strong>Display Name:</strong> {selectedUser.display_name}
                </div>
                <div>
                  <strong>Username:</strong> {selectedUser.username}
                </div>
                <div>
                  <strong>Registered:</strong>{" "}
                  {formatDate(selectedUser.created_at)}
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setSelectedUser(null);
                setActionType(null);
              }}
              disabled={
                approveUserMutation.isPending || rejectUserMutation.isPending
              }
            >
              Cancel
            </Button>
            <Button
              variant={actionType === "approve" ? "default" : "destructive"}
              onClick={confirmAction}
              disabled={
                approveUserMutation.isPending || rejectUserMutation.isPending
              }
            >
              {approveUserMutation.isPending || rejectUserMutation.isPending ? (
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              ) : actionType === "approve" ? (
                <UserCheck className="w-4 h-4 mr-2" />
              ) : (
                <UserX className="w-4 h-4 mr-2" />
              )}
              {actionType === "approve" ? "Approve" : "Reject"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
