import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ExternalLink } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { getTypeColor, formatTimeSpent, getActivityColor } from "@/lib/utils";

export type WorkLog = {
  name: string;
  type: string;
  issue_name: string;
  work_log_time_spent_human: string;
  work_log_comment: string;
  work_log_date: string;
  sp: number;
  link_ticket: string;
};

export const workLogColumns: ColumnDef<WorkLog>[] = [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ getValue }) => (
      <span className="font-medium">{getValue() as string}</span>
    ),
    enableSorting: true,
    // Only use filterFn for faceted filter, not for global search
    filterFn: (row, id, value) => {
      if (!Array.isArray(value)) return true;
      return value.includes(row.getValue(id));
    },
  },
  {
    accessorKey: "type",
    header: "Type",
    cell: ({ getValue }) => (
      <Badge className={getTypeColor(getValue() as string)} variant="secondary">
        {getValue() as string}
      </Badge>
    ),
    enableSorting: true,
    filterFn: (row, id, value) => {
      if (!Array.isArray(value)) return true;
      const type = row.getValue(id) as string;
      return value.includes(type);
    },
  },
  {
    accessorKey: "issue_name",
    header: "Ticket",
    cell: ({ getValue }) => (
      <div className="truncate max-w-xs" title={getValue() as string}>
        {getValue() as string}
      </div>
    ),
    enableSorting: true,
  },
  {
    accessorKey: "work_log_time_spent_human",
    header: "Time Spent",
    cell: ({ getValue }) => {
      return (
        <span className="text-sm">{formatTimeSpent(getValue() as string)}</span>
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: "work_log_comment",
    header: "Activity",
    enableSorting: true,
    filterFn: (row, id, value) => {
      if (!Array.isArray(value)) return true;
      const comment = row.getValue(id) as string;
      const match = comment.match(/\[([^\]]+)\]/);
      const label = match ? match[1] : comment.split("\n")[0];
      return value.includes(label);
    },
    cell: ({ getValue }) => {
      const comment = getValue() as string;
      const match = comment.match(/\[([^\]]+)\]/);
      const label = match ? match[1] : comment.split("\n")[0];
      return (
        <Popover>
          <PopoverTrigger asChild>
            <Badge
              className={getActivityColor(comment)}
              variant="secondary"
              style={{ cursor: "pointer" }}
            >
              {label}
            </Badge>
          </PopoverTrigger>
          <PopoverContent className="max-w-xs whitespace-pre-line text-sm">
            {comment}
          </PopoverContent>
        </Popover>
      );
    },
  },
  {
    accessorKey: "work_log_date",
    header: "Date",
    cell: ({ getValue }) => (
      <span className="text-sm ">
        {new Date(getValue() as string).toLocaleDateString()}
      </span>
    ),
    enableSorting: true,
    filterFn: (row, id, value) => {
      if (!Array.isArray(value)) return true;
      const date = new Date(row.getValue(id) as string);
      return value.includes(date.toISOString().split("T")[0]);
    },
  },
  {
    accessorKey: "sp",
    header: "SP",
    cell: ({ getValue }) => {
      const sp = getValue() as number;
      return sp > 0 ? <Badge variant="outline">{sp} SP</Badge> : null;
    },
    enableSorting: true,
  },
  {
    id: "actions",
    header: "",
    cell: ({ row }) => (
      <Button
        variant="ghost"
        size="sm"
        onClick={() => window.open(row.original.link_ticket, "_blank")}
      >
        <ExternalLink className="h-3 w-3" />
      </Button>
    ),
  },
];
