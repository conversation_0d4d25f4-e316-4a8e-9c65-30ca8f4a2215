"use client";

import { useSelector } from "react-redux";
import { useRouter, usePathname } from "next/navigation";
import { useEffect } from "react";
import { RootState } from "@/app/store";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { AlertTriangle } from "lucide-react";

interface AuthGuardProps {
  children: React.ReactNode;
  adminOnly?: boolean;
}

export function AuthGuard({ children, adminOnly = false }: AuthGuardProps) {
  const isAuthenticated = useSelector(
    (state: RootState) => state.auth.isAuthenticated
  );
  const user = useSelector((state: RootState) => state.auth.user);
  const showingPasskeySetup = useSelector(
    (state: RootState) => state.auth.showingPasskeySetup
  );
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (!isAuthenticated && pathname !== "/login" && pathname !== "/register") {
      router.push("/login");
    } else if (
      isAuthenticated &&
      (pathname === "/login" || pathname === "/register")
    ) {
      // Allow authenticated users to stay on login page if showing passkey setup
      if (pathname === "/login" && showingPasskeySetup) {
        return; // Don't redirect, let them complete passkey setup
      }
      router.push("/");
    }
  }, [isAuthenticated, pathname, router, showingPasskeySetup]);

  // Check authentication first
  if (!isAuthenticated && pathname !== "/login" && pathname !== "/register") {
    return null;
  }

  if (isAuthenticated && (pathname === "/login" || pathname === "/register")) {
    // Allow authenticated users to stay on login page if showing passkey setup
    if (pathname === "/login" && showingPasskeySetup) {
      return <>{children}</>;
    }
    return null;
  }

  // Check admin access if required
  if (adminOnly && isAuthenticated && user?.role !== "admin") {
    return (
      <div className="container mx-auto py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <AlertTriangle className="w-12 h-12 text-amber-500" />
            </div>
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You don't have permission to access this page.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center text-sm text-muted-foreground">
            This page is restricted to administrators only.
          </CardContent>
        </Card>
      </div>
    );
  }

  return <>{children}</>;
}
