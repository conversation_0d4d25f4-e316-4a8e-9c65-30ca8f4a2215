"use client";

import { useSelector } from "react-redux";
import { usePathname } from "next/navigation";
import { RootState } from "@/app/store";
import { DashboardHeader } from "@/components/dashboard-header";
import { AppSidebar } from "@/components/app-sidebar";
import { SidebarInset } from "@/components/ui/sidebar";
import { CommandMenu } from "@/components/command-menu";

interface AuthenticatedLayoutProps {
  children: React.ReactNode;
}

export function AuthenticatedLayout({ children }: AuthenticatedLayoutProps) {
  const isAuthenticated = useSelector(
    (state: RootState) => state.auth.isAuthenticated
  );
  const pathname = usePathname();

  if (!isAuthenticated || pathname === "/login") {
    return <>{children}</>;
  }

  return (
    <>
      <AppSidebar variant="sidebar" />
      <SidebarInset>
        <DashboardHeader />
        <main className="flex flex-1 flex-col">
          <div className="flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 p-4 md:gap-6 md:p-6 h-full">
              {children}
            </div>
          </div>
        </main>
      </SidebarInset>
      <CommandMenu />
    </>
  );
}
