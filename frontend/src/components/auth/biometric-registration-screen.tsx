"use client";

import { useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/app/store";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Fingerprint,
  Shield,
  CheckCircle,
  XCircle,
  ArrowLeft,
  Smartphone,
  Loader2,
} from "lucide-react";
import { useBiometricRegistration } from "@/hooks/use-biometric-registration";

interface BiometricRegistrationScreenProps {
  onComplete: () => void;
  onSkip: () => void;
}

export function BiometricRegistrationScreen({
  onComplete,
  onSkip,
}: BiometricRegistrationScreenProps) {
  const [deviceName, setDeviceName] = useState("");
  const [registrationSuccess, setRegistrationSuccess] = useState(false);
  const [dontShowAgain, setDontShowAgain] = useState(false);
  const { registerBiometric, isRegistering, registrationError, clearError } =
    useBiometricRegistration();

  // Get current user from Redux store
  const user = useSelector((state: RootState) => state.auth.user);

  const handleRegister = async () => {
    try {
      clearError();
      await registerBiometric({
        deviceName: deviceName.trim() || undefined,
      });
      setRegistrationSuccess(true);

      // Save preference to not show biometric setup again after successful registration (user-specific)
      if (typeof window !== "undefined" && user?.username) {
        localStorage.setItem(
          `dont-show-biometric-setup-${user.username}`,
          "true"
        );
      }

      // Redirect after showing success for 2 seconds
      setTimeout(() => {
        onComplete();
      }, 2000);
    } catch {
      // Error is handled by the hook
    }
  };

  const handleSkip = () => {
    // Save preference if user checked "don't show again" (user-specific)
    if (dontShowAgain && typeof window !== "undefined" && user?.username) {
      localStorage.setItem(
        `dont-show-biometric-setup-${user.username}`,
        "true"
      );
    }
    onSkip();
  };

  // Success screen
  if (registrationSuccess) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-background px-4">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader className="space-y-1 text-center">
            <CheckCircle className="w-16 h-16 mx-auto text-green-500 mb-4" />
            <CardTitle className="text-2xl font-bold text-green-700">
              Biometric Setup Complete!
            </CardTitle>
            <CardDescription>
              Your biometric authentication has been configured successfully.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <div className="space-y-4">
              <div className="flex justify-center">
                <Badge
                  variant="default"
                  className="text-green-700 bg-green-100"
                >
                  <Shield className="w-3 h-3 mr-1" />
                  Enhanced Security Enabled
                </Badge>
              </div>

              <p className="text-sm text-muted-foreground">
                You can now use biometric authentication to sign in quickly and
                securely.
              </p>

              <div className="pt-2">
                <Loader2 className="w-4 h-4 animate-spin mx-auto text-muted-foreground" />
                <p className="text-xs text-muted-foreground mt-1">
                  Taking you to the dashboard...
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-background px-4">
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="space-y-1">
          <div className="flex items-center justify-center mb-2">
            <Fingerprint className="w-8 h-8 text-primary" />
          </div>
          <CardTitle className="text-2xl font-bold text-center">
            Set Up Biometric Authentication
          </CardTitle>
          <CardDescription className="text-center">
            Add an extra layer of security with biometric authentication
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="space-y-4">
            <Alert>
              <Smartphone className="h-4 w-4" />
              <AlertDescription>
                This will use your device's built-in authentication like Touch
                ID, Face ID, or Windows Hello for quick and secure login.
              </AlertDescription>
            </Alert>

            <div className="space-y-2">
              <label htmlFor="device-name" className="text-sm font-medium">
                Device Name (Optional)
              </label>
              <Input
                id="device-name"
                type="text"
                placeholder="e.g., My iPhone, My MacBook"
                value={deviceName}
                onChange={(e) => setDeviceName(e.target.value)}
                disabled={isRegistering}
                maxLength={100}
              />
              <p className="text-xs text-muted-foreground">
                Leave empty to auto-generate a name for this device
              </p>
            </div>

            {registrationError && (
              <Alert variant="destructive">
                <XCircle className="h-4 w-4" />
                <AlertDescription>{registrationError}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-3 pt-2">
              <Button
                onClick={handleRegister}
                disabled={isRegistering}
                className="w-full"
                size="lg"
              >
                {isRegistering ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Setting Up...
                  </>
                ) : (
                  <>
                    <Fingerprint className="w-4 h-4 mr-2" />
                    Set Up Biometric Authentication
                  </>
                )}
              </Button>

              <Separator />

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="dont-show-again"
                    checked={dontShowAgain}
                    onCheckedChange={(checked) =>
                      setDontShowAgain(checked === true)
                    }
                    disabled={isRegistering}
                  />
                  <label
                    htmlFor="dont-show-again"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Don't show this again
                  </label>
                </div>

                <Button
                  variant="ghost"
                  onClick={handleSkip}
                  disabled={isRegistering}
                  className="w-full"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Skip for Now
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
