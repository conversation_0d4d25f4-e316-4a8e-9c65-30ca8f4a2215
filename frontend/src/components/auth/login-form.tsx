"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import Link from "next/link";
import {
  setBiometricSetup,
  loginStart,
  loginSuccess,
  loginFailure,
} from "@/app/authSlice";
import { RootState } from "@/app/store";
import {
  loginWithPassword as passwordLogin,
  getWebAuthnErrorMessage,
} from "@/lib/webauthn";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useWebAuthn } from "@/hooks/use-webauthn";
import { ApprovalStatus } from "./approval-status";
import { BiometricRegistrationScreen } from "./biometric-registration-screen";
import { Fingerprint, Shield, Smartphone, LogIn } from "lucide-react";

// Helper function to map backend user response to Redux user format
const mapUserResponse = (backendUser: any) => ({
  id: backendUser.id,
  username: backendUser.username,
  displayName: backendUser.display_name,
  role: backendUser.role,
});

const passwordLoginSchema = z.object({
  username: z.string().min(1, "Username is required"),
  password: z.string().min(1, "Password is required"),
});

type PasswordLoginForm = z.infer<typeof passwordLoginSchema>;

export function LoginForm() {
  const [pendingApprovalUsername, setPendingApprovalUsername] = useState<
    string | null
  >(null);
  const [passkeyUsername, setPasskeyUsername] = useState("");
  const [webauthnCapabilities, setWebauthnCapabilities] = useState({
    webauthnSupported: false,
    platformAuthenticatorAvailable: false,
    conditionalMediationSupported: false,
  });

  const router = useRouter();
  const dispatch = useDispatch();
  const showBiometricSetup = useSelector(
    (state: RootState) => state.auth.showingBiometricSetup
  );
  const {
    isAuthenticated,
    isLoading,
    error,
    login,
    loginDiscoverable,
    clearAuthError,
    checkCapabilities,
  } = useWebAuthn();

  const passwordForm = useForm<PasswordLoginForm>({
    resolver: zodResolver(passwordLoginSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });


  // Check WebAuthn capabilities on component mount
  useEffect(() => {
    checkCapabilities().then(setWebauthnCapabilities);
  }, [checkCapabilities]);

  // Only redirect on initial load if already authenticated (not after login)
  useEffect(() => {
    // Only redirect if authenticated on component mount, not after login
    if (isAuthenticated && !passwordForm.watch("username") && !passwordForm.watch("password")) {
      console.log("Already authenticated on mount, redirecting to home page");
      router.push("/");
    }
  }, [isAuthenticated, router, passwordForm]);

  // Clear errors when switching tabs or changing input
  useEffect(() => {
    if (error) {
      const timer = setTimeout(clearAuthError, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearAuthError]);


  const onPasswordSubmit = async (values: PasswordLoginForm) => {
    try {
      // Check capabilities BEFORE login to determine if we should show biometric setup
      const capabilities = await checkCapabilities();

      // Migrate global preference to user-specific preference (one-time migration)
      if (typeof window !== "undefined") {
        const globalPreference = localStorage.getItem(
          "dont-show-biometric-setup"
        );
        const userSpecificKey = `dont-show-biometric-setup-${values.username.trim()}`;

        if (
          globalPreference === "true" &&
          !localStorage.getItem(userSpecificKey)
        ) {
          // Migrate global preference to user-specific
          localStorage.setItem(userSpecificKey, "true");
          localStorage.removeItem("dont-show-biometric-setup");
        }
      }

      // Check if user has opted out of biometric setup (user-specific)
      const dontShowBiometric =
        typeof window !== "undefined"
          ? localStorage.getItem(
              `dont-show-biometric-setup-${values.username.trim()}`
            ) === "true"
          : false;

      // Start login process
      dispatch(loginStart());

      // Call the password login API directly
      const response = await passwordLogin({
        username: values.username.trim(),
        password: values.password.trim(),
      });

      // Dispatch login success with biometric setup flag
      // Only show biometric setup if available AND user hasn't opted out
      const shouldShowBiometric =
        capabilities.platformAuthenticatorAvailable && !dontShowBiometric;

      dispatch(
        loginSuccess({
          user: mapUserResponse(response.user),
          accessToken: response.accessToken,
          refreshToken: response.refreshToken,
          showBiometricSetup: shouldShowBiometric,
        })
      );

      // If no biometric available or user opted out, redirect to dashboard immediately
      if (!shouldShowBiometric) {
        router.push("/");
      }
      // If biometric is available and user hasn't opted out, the biometric setup screen will be shown
      // due to the showBiometricSetup flag being true
    } catch (err) {
      // Handle login failure
      // Check if the error is due to pending approval
      if (
        err instanceof Error &&
        err.message.includes("pending admin approval")
      ) {
        const errorMessage = getWebAuthnErrorMessage(err);
        dispatch(loginFailure(errorMessage));
        setPendingApprovalUsername(values.username.trim());
      } else {
        // Generic error message for all other passkey failures
        const errorMessage = "Sign in with passkey failed. Please try again.";
        dispatch(loginFailure(errorMessage));
      }
    }
  };


  const handleDiscoverableLogin = async () => {
    try {
      await loginDiscoverable();
    } catch {
      // Error is handled by the hook
    }
  };

  const handlePasskeyLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!passkeyUsername.trim()) return;

    try {
      await login({ username: passkeyUsername.trim() });
    } catch {
      // Error is handled by the hook
    }
  };

  const WebAuthnCapabilityBadge = () => (
    <div className="flex flex-wrap gap-2 mb-4 justify-center">
      <Badge
        variant={
          webauthnCapabilities.webauthnSupported ? "default" : "secondary"
        }
      >
        <Shield className="w-3 h-3 mr-1" />
        WebAuthn{" "}
        {webauthnCapabilities.webauthnSupported ? "Supported" : "Not Supported"}
      </Badge>
      {webauthnCapabilities.platformAuthenticatorAvailable && (
        <Badge variant="default">
          <Fingerprint className="w-3 h-3 mr-1" />
          Platform Authenticator
        </Badge>
      )}
      {webauthnCapabilities.conditionalMediationSupported && (
        <Badge variant="outline">
          <Smartphone className="w-3 h-3 mr-1" />
          Conditional UI
        </Badge>
      )}
    </div>
  );

  // Show approval status if user is pending approval
  if (pendingApprovalUsername) {
    return (
      <ApprovalStatus
        username={pendingApprovalUsername}
        onApproved={() => {
          setPendingApprovalUsername(null);
          // Try to login again or redirect to login
          window.location.reload();
        }}
      />
    );
  }

  // Show biometric setup if user just logged in and biometric is available
  if (showBiometricSetup) {
    return (
      <BiometricRegistrationScreen
        onComplete={() => {
          dispatch(setBiometricSetup(false));
          router.push("/");
        }}
        onSkip={() => {
          dispatch(setBiometricSetup(false));
          router.push("/");
        }}
      />
    );
  }

  if (!webauthnCapabilities.webauthnSupported) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-background px-4">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader className="space-y-1 text-center">
            <Shield className="w-12 h-12 mx-auto text-muted-foreground mb-2" />
            <CardTitle className="text-2xl font-bold">
              WebAuthn Not Supported
            </CardTitle>
            <CardDescription>
              Your browser or device doesn't support WebAuthn. Please use a
              modern browser with WebAuthn support.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center text-sm text-muted-foreground">
              <p>Supported browsers include:</p>
              <p>Chrome 67+, Firefox 60+, Safari 14+, Edge 18+</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-background px-4">
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="space-y-1">
          <div className="flex items-center justify-center mb-2">
            <Shield className="w-8 h-8 text-primary" />
          </div>
          <CardTitle className="text-2xl font-bold text-center">
            Login
          </CardTitle>
          <WebAuthnCapabilityBadge />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Form {...passwordForm}>
              <form onSubmit={passwordForm.handleSubmit(onPasswordSubmit)} className="space-y-4">
                <FormField
                  control={passwordForm.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Username</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter your username"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={passwordForm.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="Enter your password"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isLoading}
                >
                  <LogIn className="w-4 h-4 mr-2" />
                  {isLoading ? "Signing in..." : "Sign in"}
                </Button>
              </form>
            </Form>

            {webauthnCapabilities.webauthnSupported && (
              <>
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <Separator />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-background px-2 text-muted-foreground">
                      or
                    </span>
                  </div>
                </div>

                {webauthnCapabilities.platformAuthenticatorAvailable && (
                  <Button
                    onClick={handleDiscoverableLogin}
                    className="w-full"
                    disabled={isLoading}
                    variant="outline"
                  >
                    <Fingerprint className="w-4 h-4 mr-2" />
                    {isLoading ? "Authenticating..." : "Sign in with Passkey"}
                  </Button>
                )}

                {!webauthnCapabilities.platformAuthenticatorAvailable && (
                  <form onSubmit={handlePasskeyLogin} className="space-y-4">
                    <div className="space-y-2">
                      <label htmlFor="passkey-username" className="text-sm font-medium">
                        Username (for Passkey)
                      </label>
                      <Input
                        id="passkey-username"
                        placeholder="Enter your username"
                        value={passkeyUsername}
                        onChange={(e) => setPasskeyUsername(e.target.value)}
                        disabled={isLoading}
                        required
                      />
                    </div>
                    
                    <Button
                      type="submit"
                      className="w-full"
                      disabled={isLoading || !passkeyUsername.trim()}
                      variant="outline"
                    >
                      <Shield className="w-4 h-4 mr-2" />
                      {isLoading ? "Authenticating..." : "Sign in with Passkey"}
                    </Button>
                  </form>
                )}
              </>
            )}
          </div>

          {error && (
            <Alert className="mt-4" variant="destructive">
              <Shield className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="mt-4 text-center">
            <p className="text-sm text-muted-foreground">
              Don't have an account?{" "}
              <Link
                href="/register"
                className="font-medium text-primary hover:underline"
              >
                Sign up here
              </Link>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
