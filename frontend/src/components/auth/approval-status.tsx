"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { useUserApproval } from "@/hooks/use-user-approval";
import { loginStart, loginSuccess, setPasskeySetup } from "@/app/authSlice";
import { RootState } from "@/app/store";
import { getDeviceCapabilities, loginWithPassword } from "@/lib/webauthn";
import { PasskeyRegistrationScreen } from "./passkey-registration-screen";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  ClockIcon,
  XCircle,
  RefreshCw,
  ArrowLeft,
  Shield,
  CheckCircle,
  Loader2,
} from "lucide-react";

// Helper function to map backend user response to Redux user format
const mapUserResponse = (backendUser: any) => ({
  id: backendUser.id,
  username: backendUser.username,
  displayName: backendUser.display_name,
  role: backendUser.role,
});

interface ApprovalStatusProps {
  username: string;
  password?: string;
  onApproved?: () => void;
}

export function ApprovalStatus({
  username,
  password,
  onApproved,
}: ApprovalStatusProps) {
  const router = useRouter();
  const dispatch = useDispatch();
  const showPasskeySetup = useSelector(
    (state: RootState) => state.auth.showingPasskeySetup
  );
  const [isManualChecking, setIsManualChecking] = useState(false);
  const [isAutoLoggingIn, setIsAutoLoggingIn] = useState(false);

  // Query to check approval status
  const {
    data: approvalData,
    isLoading,
    error,
    refetch,
  } = useUserApproval({ username });

  const handleAutoLogin = useCallback(async () => {
    if (!password) return;

    setIsAutoLoggingIn(true);

    try {
      // Check capabilities for biometric setup
      const capabilities = await getDeviceCapabilities();

      // Migrate global preference to user-specific preference (one-time migration)
      if (typeof window !== "undefined") {
        const globalPreference = localStorage.getItem(
          "dont-show-passkey-setup"
        );
        const userSpecificKey = `dont-show-passkey-setup-${username.trim()}`;

        if (
          globalPreference === "true" &&
          !localStorage.getItem(userSpecificKey)
        ) {
          // Migrate global preference to user-specific
          localStorage.setItem(userSpecificKey, "true");
          localStorage.removeItem("dont-show-passkey-setup");
        }
      }

      // Check if user has opted out of biometric setup (user-specific)
      const dontShowPasskey =
        typeof window !== "undefined"
          ? localStorage.getItem(
              `dont-show-passkey-setup-${username.trim()}`
            ) === "true"
          : false;

      // Start login process
      dispatch(loginStart());

      // Call the password login function from webauthn library
      const authResponse = await loginWithPassword({
        username: username.trim(),
        password: password.trim(),
      });

      // Dispatch login success with passkey setup flag
      const shouldShowBiometric =
        capabilities.platformAuthenticatorAvailable && !dontShowPasskey;

      dispatch(
        loginSuccess({
          user: mapUserResponse(authResponse.user),
          accessToken: authResponse.accessToken,
          refreshToken: authResponse.refreshToken,
          showPasskeySetup: shouldShowBiometric,
        })
      );

      // If no biometric available or user opted out, redirect to dashboard immediately
      if (!shouldShowBiometric) {
        router.push("/");
      }
    } catch (error) {
      console.error("Auto-login failed:", error);
      // Fall back to the original callback
      onApproved?.();
    } finally {
      setIsAutoLoggingIn(false);
    }
  }, [password, username, dispatch, router, onApproved]);

  // Handle approval success with auto-login
  useEffect(() => {
    if (approvalData?.is_approved && password) {
      handleAutoLogin();
    } else if (approvalData?.is_approved) {
      // If no password provided, use the original callback
      onApproved?.();
    }
  }, [approvalData?.is_approved, password, onApproved, handleAutoLogin]);

  const handleManualCheck = async () => {
    setIsManualChecking(true);
    await refetch();
    setIsManualChecking(false);
  };

  const handleBackToLogin = () => {
    router.push("/login");
  };

  // Show auto-login loading state
  if (isAutoLoggingIn) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-background px-4">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader className="space-y-1 text-center">
            <CheckCircle className="w-12 h-12 mx-auto text-green-500 mb-2" />
            <CardTitle className="text-2xl font-bold text-green-700">
              Account Approved!
            </CardTitle>
            <CardDescription>Logging you in automatically...</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center space-y-2">
              <p className="text-sm text-muted-foreground">
                Username: <strong>{username}</strong>
              </p>
              <div className="flex items-center justify-center space-x-2">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span className="text-sm">Signing you in...</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show passkey setup if user just logged in and passkey is available
  if (showPasskeySetup) {
    return (
      <PasskeyRegistrationScreen
        onComplete={() => {
          dispatch(setPasskeySetup(false));
          router.push("/");
        }}
        onSkip={() => {
          dispatch(setPasskeySetup(false));
          router.push("/");
        }}
      />
    );
  }

  if (error) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-background px-4">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader className="space-y-1 text-center">
            <XCircle className="w-12 h-12 mx-auto text-destructive mb-2" />
            <CardTitle className="text-2xl font-bold">Error</CardTitle>
            <CardDescription>Failed to check approval status</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert variant="destructive">
              <AlertDescription>
                {error instanceof Error
                  ? error.message
                  : "Unknown error occurred"}
              </AlertDescription>
            </Alert>

            <div className="flex space-x-2">
              <Button
                onClick={handleManualCheck}
                variant="outline"
                className="flex-1"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Retry
              </Button>
              <Button onClick={handleBackToLogin} className="flex-1">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Login
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show rejection status if user is rejected
  if (approvalData?.is_rejected) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-background px-4">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader className="space-y-1 text-center">
            <div className="flex items-center justify-center mb-2">
              <XCircle className="w-12 h-12 text-destructive" />
            </div>
            <CardTitle className="text-2xl font-bold text-destructive">
              Application Rejected
            </CardTitle>
            <CardDescription>
              Your account registration has been rejected by an administrator
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center space-y-2">
              <p className="text-sm text-muted-foreground">
                Username: <strong>{username}</strong>
              </p>
              <Badge variant="destructive">
                <XCircle className="w-3 h-3 mr-1" />
                Rejected
              </Badge>
            </div>

            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>
                Your account registration has been rejected. Please contact an
                administrator for more information or try registering with a
                different username.
              </AlertDescription>
            </Alert>

            <div className="flex space-x-2">
              <Button
                onClick={handleManualCheck}
                variant="outline"
                className="flex-1"
                disabled={isLoading || isManualChecking}
              >
                <RefreshCw
                  className={`w-4 h-4 mr-2 ${
                    isLoading || isManualChecking ? "animate-spin" : ""
                  }`}
                />
                Check Again
              </Button>
              <Button onClick={handleBackToLogin} className="flex-1">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Login
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-background px-4">
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="space-y-1 text-center">
          <div className="flex items-center justify-center mb-2">
            <ClockIcon className="w-12 h-12 text-amber-500" />
          </div>
          <CardTitle className="text-2xl font-bold">Pending Approval</CardTitle>
          <CardDescription>
            Your account is awaiting administrator approval
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center space-y-2">
            <p className="text-sm text-muted-foreground">
              Username: <strong>{username}</strong>
            </p>
            <Badge variant="secondary" className="text-amber-700 bg-amber-100">
              <ClockIcon className="w-3 h-3 mr-1" />
              Awaiting Approval
            </Badge>
          </div>

          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              Your account registration has been submitted successfully. An
              administrator will review and approve your account soon.
            </AlertDescription>
          </Alert>

          <div className="space-y-3">
            <div className="flex space-x-2">
              <Button
                onClick={handleManualCheck}
                variant="outline"
                className="flex-1"
                disabled={isLoading || isManualChecking}
              >
                <RefreshCw
                  className={`w-4 h-4 mr-2 ${
                    isLoading || isManualChecking ? "animate-spin" : ""
                  }`}
                />
                Check Now
              </Button>
              <Button onClick={handleBackToLogin} className="flex-1">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Login
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
