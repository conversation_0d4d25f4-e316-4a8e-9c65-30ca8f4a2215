import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Trash2, Plus } from "lucide-react";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { UseFormReturn, useFieldArray } from "react-hook-form";

export interface TechStep {
  step: string;
  fileReference?: string;
}

export interface StepGroup {
  title: string;
  steps: TechStep[];
}

export interface TechData {
  figma?: string;
  epicBranch?: string;
  repository: string;
  page: string;
  account: string;
  stepGroups: StepGroup[];
}

type TechnicalPhaseProps = {
  form: UseFormReturn<any>;
  error?: string;
};

function StepGroupFields({
  form,
  groupIdx,
}: {
  form: UseFormReturn<any>;
  groupIdx: number;
}) {
  const { control, register } = form;
  const {
    fields: steps,
    append: appendStep,
    remove: removeStep,
  } = useFieldArray({
    control,
    name: `tech.stepGroups.${groupIdx}.steps`,
  });

  return (
    <div className="relative border border-border rounded-xl p-4 bg-gradient-to-br from-background to-muted/30 shadow-sm hover:shadow-md transition-all duration-200">
      <FormLabel className="font-medium text-foreground mb-2 block">
        Technical Group Title
      </FormLabel>
      <Input
        {...register(`tech.stepGroups.${groupIdx}.title`)}
        placeholder={`Technical Group ${groupIdx + 1} Title`}
        className="mb-4 bg-background text-foreground"
      />
      <div className="space-y-3">
        {steps.map((step, stepIdx) => (
          <div key={step.id} className="flex items-start gap-3">
            <div className="flex-1 flex gap-3">
              <FormField
                name={`tech.stepGroups.${groupIdx}.steps.${stepIdx}.fileReference`}
                render={({ field }) => (
                  <FormItem className="w-48">
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="📁 File (optional)"
                        className="w-48 text-sm bg-background text-foreground"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name={`tech.stepGroups.${groupIdx}.steps.${stepIdx}.step`}
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder={`Technical step ${stepIdx + 1}...`}
                        className="flex-1 bg-background text-foreground min-h-[100px]"
                        onKeyDown={(e) => {
                          if (
                            e.key === "Backspace" &&
                            field.value === "" &&
                            steps.length > 1
                          ) {
                            e.preventDefault();
                            removeStep(stepIdx);
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            {steps.length > 1 && (
              <Button
                variant="outline"
                size="sm"
                type="button"
                onClick={() => removeStep(stepIdx)}
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
          </div>
        ))}
      </div>
      <Button
        variant="outline"
        size="sm"
        type="button"
        className="mt-3"
        onClick={() => appendStep({ step: "", fileReference: "" })}
      >
        <Plus className="w-4 h-4 mr-2" />
        Add Technical Step
      </Button>
    </div>
  );
}

export default function TechnicalPhase({ form }: TechnicalPhaseProps) {
  const { control } = form;
  const { fields: stepGroups } = useFieldArray({
    control,
    name: "tech.stepGroups",
  });

  return (
    <div className="space-y-6">
      {/* Technical Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          name="tech.figma"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="font-medium">
                Figma Link (Optional)
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="https://www.figma.com/file/..."
                  className="bg-background text-foreground"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="tech.epicBranch"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="font-medium">
                Epic Branch (Optional)
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Leave empty - to be filled by Lead"
                  className="bg-background text-foreground"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="tech.repository"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="font-medium">
                Repository (Optional)
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="https://github.com/..."
                  className="bg-background text-foreground"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="tech.page"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="font-medium">Page URL (Optional)</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="https://app.example.com/..."
                  className="bg-background text-foreground"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="tech.account"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="font-medium">Account (Optional)</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="<EMAIL>"
                  className="bg-background text-foreground"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Divider */}
      <div className="border-t border-border my-6" />

      {/* Technical Steps */}
      <FormLabel className="font-medium">Technical Steps (Optional)</FormLabel>
      <div className="space-y-6">
        {stepGroups.map((group, groupIdx) => (
          <StepGroupFields key={group.id} form={form} groupIdx={groupIdx} />
        ))}
        <Button
          variant="outline"
          size="sm"
          type="button"
          className="mt-2"
          onClick={() =>
            form.setValue("tech.stepGroups", [
              ...form.getValues("tech.stepGroups"),
              { title: "", steps: [{ step: "", fileReference: "" }] },
            ])
          }
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Technical Group
        </Button>
        {stepGroups.length > 1 && (
          <Button
            variant="destructive"
            size="sm"
            type="button"
            className="mt-2 ml-2"
            onClick={() => {
              const arr = [...form.getValues("tech.stepGroups")];
              arr.pop();
              form.setValue("tech.stepGroups", arr);
            }}
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Remove Last
          </Button>
        )}
      </div>
    </div>
  );
}
