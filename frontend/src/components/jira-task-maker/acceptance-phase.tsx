import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Trash2, Plus } from "lucide-react";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { UseFormReturn, useFieldArray } from "react-hook-form";

type ValueItem = { value: string };

export interface AcceptanceCriteria {
  title: string;
  given: ValueItem[];
  when: ValueItem[];
  then: ValueItem[];
}

type AcceptancePhaseProps = {
  form: UseFormReturn<any>;
  error?: string;
};

function AcceptanceCriteriaFields({
  form,
  idx,
}: {
  form: UseFormReturn<any>;
  idx: number;
}) {
  const { control, register } = form;
  const givenArray = useFieldArray({
    control,
    name: `acceptance.${idx}.given`,
  });
  const whenArray = useFieldArray({
    control,
    name: `acceptance.${idx}.when`,
  });
  const thenArray = useFieldArray({
    control,
    name: `acceptance.${idx}.then`,
  });

  return (
    <div className="relative border border-border rounded-xl p-4 bg-gradient-to-br from-background to-muted/30 shadow-sm hover:shadow-md transition-all duration-200">
      <FormLabel className="font-medium text-foreground mb-2 block">
        Acceptance Criteria Title
      </FormLabel>
      <Input
        {...register(`acceptance.${idx}.title`)}
        placeholder={`Acceptance Criteria ${idx + 1} Title`}
        className="mb-4 bg-background text-foreground"
      />

      {/* Given */}
      <div className="space-y-2 mb-4">
        <FormLabel className="font-medium text-foreground">
          Given (Context) (Optional):
        </FormLabel>
        <div className="space-y-2">
          {givenArray.fields.map((field, givenIdx) => (
            <FormField
              key={field.id}
              name={`acceptance.${idx}.given.${givenIdx}.value`}
              render={({ field }) => (
                <FormItem className="flex items-center gap-2">
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Given condition..."
                      className="flex-1 bg-background text-foreground"
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          givenArray.append({ value: "" });
                        } else if (
                          e.key === "Backspace" &&
                          field.value === "" &&
                          givenArray.fields.length > 1
                        ) {
                          e.preventDefault();
                          givenArray.remove(givenIdx);
                        }
                      }}
                    />
                  </FormControl>
                  {givenArray.fields.length > 1 && (
                    <Button
                      variant="outline"
                      size="sm"
                      type="button"
                      onClick={() => givenArray.remove(givenIdx)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />
          ))}
          <Button
            variant="outline"
            size="sm"
            type="button"
            className="text-muted-foreground"
            onClick={() => givenArray.append({ value: "" })}
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Given
          </Button>
        </div>
      </div>

      {/* When */}
      <div className="space-y-3 mb-4">
        <FormLabel className="font-medium text-foreground">
          When (Action) (Optional):
        </FormLabel>
        <div className="space-y-2">
          {whenArray.fields.map((field, whenIdx) => (
            <FormField
              key={field.id}
              name={`acceptance.${idx}.when.${whenIdx}.value`}
              render={({ field }) => (
                <FormItem className="flex items-center gap-2">
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="When action..."
                      className="flex-1 bg-background text-foreground"
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          whenArray.append({ value: "" });
                        } else if (
                          e.key === "Backspace" &&
                          field.value === "" &&
                          whenArray.fields.length > 1
                        ) {
                          e.preventDefault();
                          whenArray.remove(whenIdx);
                        }
                      }}
                    />
                  </FormControl>
                  {whenArray.fields.length > 1 && (
                    <Button
                      variant="outline"
                      size="sm"
                      type="button"
                      onClick={() => whenArray.remove(whenIdx)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />
          ))}
          <Button
            variant="outline"
            size="sm"
            type="button"
            className="text-muted-foreground"
            onClick={() => whenArray.append({ value: "" })}
          >
            <Plus className="w-4 h-4 mr-2" />
            Add When
          </Button>
        </div>
      </div>

      {/* Then */}
      <div className="space-y-3">
        <FormLabel className="font-medium text-foreground">
          Then (Expected Result) (Optional):
        </FormLabel>
        <div className="space-y-2">
          {thenArray.fields.map((field, thenIdx) => (
            <FormField
              key={field.id}
              name={`acceptance.${idx}.then.${thenIdx}.value`}
              render={({ field }) => (
                <FormItem className="flex items-center gap-2">
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Then result..."
                      className="flex-1 bg-background text-foreground"
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          thenArray.append({ value: "" });
                        } else if (
                          e.key === "Backspace" &&
                          field.value === "" &&
                          thenArray.fields.length > 1
                        ) {
                          e.preventDefault();
                          thenArray.remove(thenIdx);
                        }
                      }}
                    />
                  </FormControl>
                  {thenArray.fields.length > 1 && (
                    <Button
                      variant="outline"
                      size="sm"
                      type="button"
                      onClick={() => thenArray.remove(thenIdx)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />
          ))}
          <Button
            variant="outline"
            size="sm"
            type="button"
            className="text-muted-foreground"
            onClick={() => thenArray.append({ value: "" })}
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Then
          </Button>
        </div>
      </div>
    </div>
  );
}

export default function AcceptancePhase({ form, error }: AcceptancePhaseProps) {
  const { control } = form;
  const { fields: acceptanceFields } = useFieldArray({
    control,
    name: "acceptance",
  });

  return (
    <div className="space-y-6">
      {error && <div className="text-red-500 text-xs mb-2">{error}</div>}
      <div className="space-y-6">
        {acceptanceFields.map((criteria, idx) => (
          <AcceptanceCriteriaFields key={criteria.id} form={form} idx={idx} />
        ))}
        <Button
          variant="outline"
          size="sm"
          type="button"
          className="mt-2"
          onClick={() =>
            form.setValue("acceptance", [
              ...form.getValues("acceptance"),
              {
                title: "",
                given: [{ value: "" }],
                when: [{ value: "" }],
                then: [{ value: "" }],
              },
            ])
          }
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Acceptance Criteria
        </Button>
        {acceptanceFields.length > 1 && (
          <Button
            variant="destructive"
            size="sm"
            type="button"
            className="mt-2 ml-2"
            onClick={() => {
              const arr = [...form.getValues("acceptance")];
              arr.pop();
              form.setValue("acceptance", arr);
            }}
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Remove Last
          </Button>
        )}
      </div>
    </div>
  );
}
