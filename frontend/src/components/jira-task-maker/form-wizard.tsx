import React, { useCallback } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormField,
  FormItem,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import ObjectivePhase from "./objective-phase";
import AcceptancePhase from "./acceptance-phase";
import TechnicalPhase, { TechData } from "./technical-phase";
import ApiPhase, { ApiSpec } from "./api-phase";
import UiPhase, { UiSpec } from "./ui-phase";
import { Textarea } from "../ui/textarea";

export type JiraTaskMakerMode = "frontend" | "backend";

interface FormWizardProps {
  mode: JiraTaskMakerMode;
}

type ObjectiveItem = { value: string };
type ValueItem = { value: string };

type AcceptanceCriteria = {
  title: string;
  given: ValueItem[];
  when: ValueItem[];
  then: ValueItem[];
};

type FormData = {
  objective: ObjectiveItem[];
  acceptance: AcceptanceCriteria[];
  tech: TechData;
  api: ApiSpec[];
  ui: UiSpec[];
  notes: string;
};

const getDefaultValues = (): FormData => ({
  objective: [{ value: "" }],
  acceptance: [
    {
      title: "",
      given: [{ value: "" }],
      when: [{ value: "" }],
      then: [{ value: "" }],
    },
  ],
  tech: {
    figma: "",
    epicBranch: "",
    repository: "",
    page: "",
    account: "",
    stepGroups: [{ title: "", steps: [{ step: "", fileReference: "" }] }],
  },
  api: [
    {
      name: "",
      endpointUrl: "",
      contract: { method: "", requestPayload: "", responsePayload: "" },
    },
  ],
  ui: [{ name: "", design: undefined, note: "" }],
  notes: "",
});

const NotesSection = () => (
  <FormField
    name="notes"
    render={({ field }) => (
      <FormItem>
        <FormControl>
          <Textarea
            {...field}
            placeholder="Additional notes, considerations, or special requirements..."
          />
        </FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
);

export default function FormWizard({ mode }: FormWizardProps) {
  const [isGenerating, setIsGenerating] = React.useState(false);
  const [showModal, setShowModal] = React.useState(false);
  const [generatedMarkdown, setGeneratedMarkdown] = React.useState("");

  const defaultValues = getDefaultValues();
  const form = useForm<FormData>({
    defaultValues,
    mode: "onChange",
  });

  // Reset form when mode changes to ensure default values are applied
  React.useEffect(() => {
    form.reset(getDefaultValues());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mode]);


  // Generate Jira-compatible content with tab-separated tables
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function generateJiraMarkdown(form: FormData) {
    let md = "";
    if (form.objective.length)
      md += `## Objective\n${form.objective
        .map((o) => o.value)
        .filter(Boolean)
        .map((v) => `- ${v}`)
        .join("\n")}\n\n`;
    if (form.acceptance.length)
      md += `## Acceptance Criteria\n${form.acceptance
        .map(
          (c, i) =>
            `### ${c.title || `Acceptance Criteria ${i + 1}`}\n` +
            c.given
              .map((g) => g.value)
              .filter(Boolean)
              .map((g) => `- **Given:** ${g}`)
              .join("\n") +
            "\n" +
            c.when
              .map((w) => w.value)
              .filter(Boolean)
              .map((w) => `- **When:** ${w}`)
              .join("\n") +
            "\n" +
            c.then
              .map((t) => t.value)
              .filter(Boolean)
              .map((t) => `- **Then:** ${t}`)
              .join("\n")
        )
        .join("\n\n")}\n`;
    if (form.tech)
      md +=
        `## Technical Specifications\n` +
        (form.tech.figma ? `- **Figma:** ${form.tech.figma}\n` : "") +
        (form.tech.epicBranch
          ? `- **Epic Branch:** ${form.tech.epicBranch}\n`
          : "") +
        (form.tech.repository
          ? `- **Repository:** ${form.tech.repository}\n`
          : "") +
        (form.tech.page ? `- **Page:** ${form.tech.page}\n` : "") +
        (form.tech.account ? `- **Account:** ${form.tech.account}\n` : "") +
        (form.tech.stepGroups && form.tech.stepGroups.length
          ? form.tech.stepGroups
              .map(
                (g, i) =>
                  `### ${g.title || `Technical Group ${i + 1}`}\n` +
                  g.steps
                    .filter((s) => s.step)
                    .map((s) => {
                      if (s.fileReference) {
                        const stepLines = s.step
                          .split("\n")
                          .filter((line) => line.trim())
                          .map((line) => {
                            // Preserve original indentation for multi-level bullets, add tab prefix
                            const leadingSpaces = line.match(/^(\s*)/)?.[1] || '';
                            const trimmed = line.trim();
                            
                            if (trimmed.startsWith('- ')) {
                              // Already has bullet, add tab + preserve original indentation
                              return `\t${line}`;
                            } else {
                              // Add bullet + tab, preserve indentation
                              return `\t${leadingSpaces}- ${trimmed}`;
                            }
                          })
                          .join("\n");
                        return `- \`${s.fileReference}\`:\n${stepLines}`;
                      } else {
                        const stepLines = s.step
                          .split("\n")
                          .filter((line) => line.trim())
                          .map((line) => {
                            // Preserve original indentation for multi-level bullets
                            const leadingSpaces = line.match(/^(\s*)/)?.[1] || '';
                            const trimmed = line.trim();
                            
                            if (trimmed.startsWith('- ')) {
                              // Already has bullet, preserve original indentation
                              return line;
                            } else {
                              // Add bullet, preserve indentation
                              return `${leadingSpaces}- ${trimmed}`;
                            }
                          })
                          .join("\n");
                        return stepLines;
                      }
                    })
                    .join("\n")
              )
              .join("\n\n")
          : "") +
        "\n";
    if (mode === "backend" && form.api && form.api.length)
      md += `## API\n${form.api
        .map(
          (a, i) =>
            `### ${a.name || `API ${i + 1}`}\n- **Endpoint:** ${
              a.endpointUrl
            }\n- **Method:** ${
              a.contract.method
            }\n- **Request:**\n\`\`\`json\n${
              a.contract.requestPayload
            }\n\`\`\`\n- **Response:**\n\`\`\`json\n${
              a.contract.responsePayload
            }\n\`\`\``
        )
        .join("\n\n")}\n`;
    if (mode === "frontend" && form.ui && form.ui.length) {
      const validUiSpecs = form.ui.filter((u) => u.name || u.note || u.design);
      if (validUiSpecs.length > 0) {
        md += `## UI Specifications\n\n`;

        // Create tab-separated table (compatible with Excel/Numbers copy-paste to Jira)
        md += `Component\tDesign\tRequirements & Notes\n`;

        validUiSpecs.forEach((u, i) => {
          const componentName = u.name || `UI Component ${i + 1}`;
          const designInfo = u.design
            ? typeof u.design === "string"
              ? `Design Link: ${u.design}`
              : `Design image attached (${u.design.name || "image"})`
            : "No design attached";
          const notes = u.note || "No additional requirements specified";

          md += `${componentName}\t${designInfo}\t${notes}\n`;
        });

        md += `\n`;
      }
    }
    if (form.notes) md += `## Notes\n${form.notes}\n`;
    return md;
  }

  // Simple markdown generator for preview
  const generateMarkdown = useCallback(
    (form: FormData) => {
      let md = "";
      if (form.objective.length)
        md += `## Objective\n${form.objective
          .map((o) => o.value)
          .filter(Boolean)
          .map((v) => `- ${v}`)
          .join("\n")}\n\n`;
      if (form.acceptance.length)
        md += `## Acceptance Criteria\n${form.acceptance
          .map(
            (c, i) =>
              `### ${c.title || `Acceptance Criteria ${i + 1}`}\n` +
              c.given
                .map((g) => g.value)
                .filter(Boolean)
                .map((g) => `- **Given:** ${g}`)
                .join("\n") +
              "\n" +
              c.when
                .map((w) => w.value)
                .filter(Boolean)
                .map((w) => `- **When:** ${w}`)
                .join("\n") +
              "\n" +
              c.then
                .map((t) => t.value)
                .filter(Boolean)
                .map((t) => `- **Then:** ${t}`)
                .join("\n")
          )
          .join("\n\n")}\n`;
      if (form.tech)
        md +=
          `## Technical Specifications\n` +
          (form.tech.figma ? `- **Figma:** ${form.tech.figma}\n` : "") +
          (form.tech.epicBranch
            ? `- **Epic Branch:** ${form.tech.epicBranch}\n`
            : "") +
          (form.tech.repository
            ? `- **Repository:** ${form.tech.repository}\n`
            : "") +
          (form.tech.page ? `- **Page:** ${form.tech.page}\n` : "") +
          (form.tech.account ? `- **Account:** ${form.tech.account}\n` : "") +
          (form.tech.stepGroups && form.tech.stepGroups.length
            ? form.tech.stepGroups
                .map(
                  (g, i) =>
                    `### ${g.title || `Technical Group ${i + 1}`}\n` +
                    g.steps
                      .filter((s) => s.step)
                      .map((s) => {
                        if (s.fileReference) {
                          const stepLines = s.step
                            .split("\n")
                            .filter((line) => line.trim());
                          const formattedLines = stepLines
                            .map((line) => {
                              // Preserve original indentation for multi-level bullets, add tab prefix
                              const leadingSpaces = line.match(/^(\s*)/)?.[1] || '';
                              const trimmed = line.trim();
                              
                              if (trimmed.startsWith('- ')) {
                                // Already has bullet, add tab + preserve original indentation
                                return `\t${line}`;
                              } else {
                                // Add bullet + tab, preserve indentation
                                return `\t${leadingSpaces}- ${trimmed}`;
                              }
                            })
                            .join("\n");
                          return `- \`${s.fileReference}\`:\n${formattedLines}`;
                        } else {
                          // Handle steps that may already contain bullets
                          const stepLines = s.step
                            .split("\n")
                            .filter((line) => line.trim())
                            .map((line) => {
                              // Preserve original indentation for multi-level bullets
                              const leadingSpaces = line.match(/^(\s*)/)?.[1] || '';
                              const trimmed = line.trim();
                              
                              if (trimmed.startsWith('- ')) {
                                // Already has bullet, preserve original indentation
                                return line;
                              } else {
                                // Add bullet, preserve indentation
                                return `${leadingSpaces}- ${trimmed}`;
                              }
                            })
                            .join("\n");
                          return stepLines;
                        }
                      })
                      .join("\n")
                )
                .join("\n\n")
            : "") +
          "\n";
      if (mode === "backend" && form.api && form.api.length)
        md += `## API Specification\n${form.api
          .map(
            (a, i) =>
              `### ${a.name || `API ${i + 1}`}\n- **Endpoint:** ${
                a.endpointUrl
              }\n- **Method:** ${
                a.contract.method
              }\n- **Request:**\n\`\`\`json\n${
                a.contract.requestPayload
              }\n\`\`\`\n- **Response:**\n\`\`\`json\n${
                a.contract.responsePayload
              }\n\`\`\``
          )
          .join("\n\n")}\n`;
      if (mode === "frontend" && form.ui && form.ui.length) {
        const validUiSpecs = form.ui.filter(
          (u) => u.name || u.note || u.design
        );
        if (validUiSpecs.length > 0) {
          md += `## UI Specification\n\n`;

          // Create proper markdown table for preview (ReactMarkdown rendering)
          md += `| Component | Design | Requirements & Notes |\n`;
          md += `|-----------|--------|---------------------|\n`;

          validUiSpecs.forEach((u, i) => {
            const componentName = u.name || `UI Component ${i + 1}`;
            const designInfo = u.design
              ? typeof u.design === "string"
                ? `Design Link: ${u.design}`
                : `Design image attached (${u.design.name || "image"})`
              : "No design attached";
            const notes = u.note || "No additional requirements specified";

            // Escape pipe characters for markdown table
            const escapedComponent = componentName.replace(/\|/g, "\\|");
            const escapedDesign = designInfo.replace(/\|/g, "\\|");
            const escapedNotes = notes.replace(/\|/g, "\\|");

            md += `| ${escapedComponent} | ${escapedDesign} | ${escapedNotes} |\n`;
          });

          md += `\n`;
        }
      }
      if (form.notes) md += `## Notes\n${form.notes}\n`;
      return md;
    },
    [mode]
  );

  const handleSubmit = useCallback(() => {
    setIsGenerating(true);
    const md = generateMarkdown(form.getValues());
    setGeneratedMarkdown(md);
    setTimeout(() => {
      setIsGenerating(false);
      setShowModal(true);
    }, 500);
  }, [form, generateMarkdown]);

  return (
    <div className="form-wizard w-full">
      <Dialog open={showModal} onOpenChange={setShowModal}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader className="space-y-3">
            <DialogTitle className="text-xl font-bold">
              Generated Jira Task
            </DialogTitle>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Ready to copy and paste into Jira</span>
            </div>
          </DialogHeader>
          <Tabs defaultValue="raw" className="space-y-4">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="raw">Raw Markdown</TabsTrigger>
              <TabsTrigger value="preview">Preview</TabsTrigger>
            </TabsList>
            <TabsContent value="raw" className="space-y-4">
              <div className="border border-border rounded-lg overflow-hidden">
                <div className="bg-muted/30 px-4 py-2 border-b border-border">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-muted-foreground">
                      Jira Markdown Content
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {generatedMarkdown.length} characters
                    </span>
                  </div>
                </div>
                <textarea
                  className="w-full h-80 font-mono text-sm p-4 bg-background text-foreground resize-none focus:outline-none"
                  value={generatedMarkdown}
                  readOnly
                  placeholder="Generated content will appear here..."
                />
              </div>
            </TabsContent>

            <TabsContent value="preview" className="space-y-4">
              <div className="border border-border rounded-lg overflow-hidden">
                <div className="bg-muted/30 px-4 py-2 border-b border-border">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-muted-foreground">
                      Markdown Preview
                    </span>
                    <span className="text-xs text-muted-foreground">
                      Rendered output
                    </span>
                  </div>
                </div>
                <div className="h-80 p-4 bg-background text-foreground overflow-y-auto prose prose-sm dark:prose-invert max-w-none">
                  {generatedMarkdown ? (
                    <ReactMarkdown remarkPlugins={[remarkGfm]}>{generatedMarkdown}</ReactMarkdown>
                  ) : (
                    <div className="text-muted-foreground italic">
                      Generated content will appear here...
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                Content formatted for Jira compatibility
              </div>
              <Button
                variant="default"
                onClick={async () => {
                  try {
                    // Use the same content as shown in raw markdown tab
                    const markdownContent = generateMarkdown(form.getValues());

                    // Always use plain text markdown - it works better with Jira
                    await navigator.clipboard.writeText(markdownContent);
                  } catch {
                    // Fallback for older browsers
                    try {
                      const markdownContent = generateMarkdown(
                        form.getValues()
                      );
                      await navigator.clipboard.writeText(markdownContent);
                    } catch (fallbackErr) {
                      console.error(
                        "Failed to copy to clipboard:",
                        fallbackErr
                      );
                      // Could show a toast notification here
                    }
                  }
                }}
                className="px-6"
              >
                Copy to Clipboard
              </Button>
            </div>
          </Tabs>
        </DialogContent>
      </Dialog>
      <div className="max-w-5xl mx-auto">
        <Card className="w-full bg-background text-foreground shadow-lg border-0 ring-1 ring-border/50 pt-0">
          <CardContent className="p-0">
            {/* Header */}
            <div className="border-b border-border/50 bg-gradient-to-r from-background to-muted/30 p-8 rounded-t-xl rounded-b-none">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <div className="flex items-center gap-3">
                    <h1 className="text-2xl font-bold text-foreground tracking-tight">
                      Jira Task Maker
                    </h1>
                    <div className="px-3 py-1 bg-primary/20 text-primary dark:bg-primary/80 dark:text-primary-foreground rounded-full text-sm font-medium border border-primary/30">
                      {mode === "frontend" ? "Frontend" : "Backend"}
                    </div>
                  </div>
                  <p className="text-muted-foreground">
                    Create well-structured Jira tasks with ease
                  </p>
                </div>
              </div>
            </div>

            {/* Single Form Layout */}
            <div className="p-8">
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(handleSubmit)}
                  className="space-y-8"
                >
                  {/* Objectives Section */}
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <h2 className="text-xl font-bold text-foreground flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-500/10 text-blue-600 dark:bg-blue-500/20 dark:text-blue-400 rounded-lg flex items-center justify-center text-sm font-semibold">
                          1
                        </div>
                        Objectives
                      </h2>
                      <p className="text-muted-foreground text-sm ml-11">
                        Describe what needs to be accomplished (optional)
                      </p>
                    </div>
                    <div className="ml-11">
                      <ObjectivePhase form={form} error={undefined} />
                    </div>
                  </div>

                  {/* Acceptance Criteria Section */}
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <h2 className="text-xl font-bold text-foreground flex items-center gap-3">
                        <div className="w-8 h-8 bg-green-500/10 text-green-600 dark:bg-green-500/20 dark:text-green-400 rounded-lg flex items-center justify-center text-sm font-semibold">
                          2
                        </div>
                        Acceptance Criteria
                      </h2>
                      <p className="text-muted-foreground text-sm ml-11">
                        Define success criteria using Given-When-Then format
                        (optional)
                      </p>
                    </div>
                    <div className="ml-11">
                      <AcceptancePhase form={form} error={undefined} />
                    </div>
                  </div>

                  {/* Technical Specification Section */}
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <h2 className="text-xl font-bold text-foreground flex items-center gap-3">
                        <div className="w-8 h-8 bg-purple-500/10 text-purple-600 dark:bg-purple-500/20 dark:text-purple-400 rounded-lg flex items-center justify-center text-sm font-semibold">
                          3
                        </div>
                        Technical Specification
                      </h2>
                      <p className="text-muted-foreground text-sm ml-11">
                        Provide implementation details (optional)
                      </p>
                    </div>
                    <div className="ml-11">
                      <TechnicalPhase form={form} error={undefined} />
                    </div>
                  </div>

                  {/* UI/API Specification Section */}
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <h2 className="text-xl font-bold text-foreground flex items-center gap-3">
                        <div className="w-8 h-8 bg-orange-500/10 text-orange-600 dark:bg-orange-500/20 dark:text-orange-400 rounded-lg flex items-center justify-center text-sm font-semibold">
                          4
                        </div>
                        {mode === "frontend"
                          ? "UI Specification"
                          : "API Specification"}
                      </h2>
                      <p className="text-muted-foreground text-sm ml-11">
                        {mode === "frontend"
                          ? "Describe UI/UX requirements (optional)"
                          : "Define endpoints and data formats (optional)"}
                      </p>
                    </div>
                    <div className="ml-11">
                      {mode === "frontend" ? (
                        <UiPhase form={form} />
                      ) : (
                        <ApiPhase form={form} />
                      )}
                    </div>
                  </div>

                  {/* Notes Section */}
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <h2 className="text-xl font-bold text-foreground flex items-center gap-3">
                        <div className="w-8 h-8 bg-gray-500/10 text-gray-600 dark:bg-gray-500/20 dark:text-gray-400 rounded-lg flex items-center justify-center text-sm font-semibold">
                          5
                        </div>
                        Additional Notes
                      </h2>
                      <p className="text-muted-foreground text-sm ml-11">
                        Additional notes, considerations, or special
                        requirements (optional)
                      </p>
                    </div>
                    <div className="ml-11">
                      <NotesSection />
                    </div>
                  </div>

                  {/* Generate Button */}
                  <div className="flex justify-center pt-8 border-t border-border/50">
                    <Button
                      variant="default"
                      type="submit"
                      disabled={isGenerating}
                      className="px-6 py-2 font-medium bg-primary hover:bg-primary/90 transition-colors"
                    >
                      {isGenerating ? (
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-4 border-2 border-primary-foreground/30 border-t-primary-foreground rounded-full animate-spin" />
                          Generating...
                        </div>
                      ) : (
                        "Generate Jira Task"
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
