import React, { useRef } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Trash2, Plus, Image as ImageIcon } from "lucide-react";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { UseFormReturn, useFieldArray } from "react-hook-form";

export interface UiSpec {
  name: string;
  design?: File | string;
  note?: string;
}

type UiPhaseProps = {
  form: UseFormReturn<any>;
};

function getImagePreviewUrl(design: File | string | undefined) {
  if (!design) return "";
  if (typeof design === "string") return design;
  return URL.createObjectURL(design);
}

export default function UiPhase({ form }: UiPhaseProps) {
  const { control, setValue, watch } = form;
  const { fields, append, remove } = useFieldArray({
    control,
    name: "ui",
  });
  const fileInputs = useRef<Array<HTMLInputElement | null>>([]);

  const handleFileChange = (idx: number, file: File | null) => {
    if (!file) return;
    setValue(`ui.${idx}.design`, file);
  };

  const clearImage = (idx: number) => {
    setValue(`ui.${idx}.design`, undefined);
  };

  const triggerImageUpload = (idx: number) => {
    fileInputs.current[idx]?.click();
  };

  return (
    <div className="space-y-8">
      {fields.length > 0 && (
        <div className="space-y-8">
          {fields.map((field, idx) => {
            const design = watch(`ui.${idx}.design`);
            return (
              <div
                key={field.id}
                className="relative border border-border rounded-xl p-4 bg-gradient-to-br from-background to-muted/30 shadow-sm hover:shadow-md transition-all duration-200"
              >
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-foreground rounded-lg flex items-center justify-center text-sm font-semibold">
                      {idx + 1}
                    </div>
                    <h4 className="text-lg font-semibold text-foreground">
                      UI Specification {idx + 1}
                    </h4>
                  </div>
                  {fields.length > 1 && (
                    <Button
                      variant="outline"
                      size="sm"
                      type="button"
                      onClick={() => remove(idx)}
                      className="hover:bg-destructive/10 hover:text-destructive"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <div className="space-y-6">
                  <FormField
                    name={`ui.${idx}.name`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>UI Component Name</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="e.g., Login Form, Dashboard Header, User Profile Card"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="space-y-4">
                    <FormLabel className="font-medium">
                      Design Mock-up
                    </FormLabel>
                    <div className="space-y-3">
                      <div className="border border-border rounded-lg bg-background overflow-hidden">
                        <div className="p-6">
                          {design ? (
                            <div className="space-y-4">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                  <span className="text-sm font-medium text-muted-foreground">
                                    Design Image Attached
                                  </span>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  type="button"
                                  onClick={() => clearImage(idx)}
                                  className="hover:bg-destructive/10 hover:text-destructive text-muted-foreground"
                                >
                                  <Trash2 className="w-4 h-4 mr-1" />
                                  Remove
                                </Button>
                              </div>
                              <div className="relative group">
                                {/* eslint-disable-next-line @next/next/no-img-element */}
                                <img
                                  src={getImagePreviewUrl(design)}
                                  alt="UI Design Mockup"
                                  className="w-full h-auto max-h-64 object-contain rounded-lg border border-border shadow-sm group-hover:shadow-md transition-shadow"
                                />
                                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/5 rounded-lg transition-colors" />
                              </div>
                            </div>
                          ) : (
                            <div
                              className="flex flex-col items-center justify-center h-32 border-2 border-dashed border-border/60 rounded-lg bg-muted/30 cursor-pointer hover:border-primary/50 hover:bg-muted/50 transition-all group"
                              onClick={() => triggerImageUpload(idx)}
                            >
                              <ImageIcon className="w-10 h-10 text-muted-foreground/60 group-hover:text-primary/70 transition-colors mb-3" />
                              <p className="text-sm font-medium text-muted-foreground group-hover:text-foreground transition-colors">
                                Click to attach design image
                              </p>
                              <p className="text-xs text-muted-foreground/80 mt-1">
                                PNG, JPG, or paste image directly
                              </p>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center gap-3">
                        <Button
                          variant="outline"
                          size="sm"
                          type="button"
                          onClick={() => triggerImageUpload(idx)}
                          className="bg-background hover:bg-muted"
                        >
                          <ImageIcon className="w-4 h-4 mr-2" />
                          {design ? "Replace Image" : "Attach Image"}
                        </Button>
                        <input
                          ref={(el) => {
                            fileInputs.current[idx] = el;
                          }}
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={(e) =>
                            handleFileChange(
                              idx,
                              e.target.files ? e.target.files[0] : null
                            )
                          }
                        />
                        <span className="text-sm text-muted-foreground">
                          or drag and drop image here
                        </span>
                      </div>
                    </div>
                  </div>

                  <FormField
                    name={`ui.${idx}.note`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-medium">
                          Requirements & Notes
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            rows={3}
                            placeholder="Describe functionality, interactions, responsive behavior, accessibility requirements, or any special considerations..."
                            className="bg-background border-border hover:border-primary/50 focus:border-primary transition-colors resize-none"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            );
          })}
        </div>
      )}

      <div className="flex pt-4">
        <Button
          variant="outline"
          size="default"
          type="button"
          onClick={() => append({ name: "", design: undefined, note: "" })}
          className="bg-background hover:bg-primary/5 hover:border-primary/50 transition-all"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add UI Specification
        </Button>
      </div>
    </div>
  );
}
