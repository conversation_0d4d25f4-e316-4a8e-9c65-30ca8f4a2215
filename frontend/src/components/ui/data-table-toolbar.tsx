import * as React from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { Table } from "@tanstack/react-table";
import { DataTableFacetedFilter } from "@/components/ui/data-table-faceted-filter";
import { DataTableViewOptions } from "@/components/ui/data-table-view-options";
interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  filterColumns?: Array<{
    id: string;
    placeholder?: string;
    options?: Array<{ label: string; value: string }>;
  }>;
  disableSearch?: boolean;
  seachPlaceHolder?: string;
  filterData?: Array<{
    value: string;
    label: string;
  }>;
  selectedFilter?: string;
  onFilterChange?: (value: string) => void;
}

export function DataTableToolbar<TData>({
  table,
  filterColumns,
  disableSearch,
  seachPlaceHolder,
  filterData,
  selectedFilter,
  onFilterChange,
}: DataTableToolbarProps<TData>) {
  const searchableColumnLists = [
    "issue_name",
      "task_name",
    "bug_name",
    "name",
    "key",
    "summary",
  ];
  return (
    <div className="flex flex-wrap items-center gap-2 mb-4 px-1 sm:px-0">
      {/* Global search */}
      <Input
        hidden={disableSearch}
        className="w-full sm:max-w-xs md:w-64"
        placeholder={seachPlaceHolder ? seachPlaceHolder : "Search tickets..."}
        value={(() => {
          const columns = table.getAllColumns();
          const searchableColumns = searchableColumnLists;

          for (const columnId of searchableColumns) {
            const column = columns.find((col) => col.id === columnId);
            if (column && column.getCanFilter()) {
              const filterValue = column.getFilterValue() as string;
              if (filterValue) return filterValue;
            }
          }
          return "";
        })()}
        onChange={(e) => {
          const columns = table.getAllColumns();
          const searchableColumns = searchableColumnLists;

          searchableColumns.forEach((columnId) => {
            const column = columns.find((col) => col.id === columnId);
            if (column && column.getCanFilter()) {
              column.setFilterValue("");
            }
          });

          // Set filter on the first available searchable column
          if (e.target.value) {
            for (const columnId of searchableColumns) {
              const column = columns.find((col) => col.id === columnId);
              if (column && column.getCanFilter()) {
                column.setFilterValue(e.target.value);
                break;
              }
            }
          }
        }}
      />
      {filterColumns?.map((col) => {
        const column = table.getColumn(col.id);
        if (!column) return null;
        if (col.options) {
          return (
            <DataTableFacetedFilter
              key={col.id}
              column={column}
              title={col.placeholder || col.id}
              options={col.options}
            />
          );
        }
        return (
          <Input
            key={col.id}
            placeholder={col.placeholder || col.id}
            value={(column.getFilterValue() as string) || ""}
            onChange={(e) => column.setFilterValue(e.target.value)}
            className="w-full max-w-[120px] sm:max-w-xs md:w-40"
          />
        );
      })}
      {/* Clear filters button */}
      {table.getState().columnFilters?.length ? (
        <Button
          variant="ghost"
          onClick={() => {
            table.resetColumnFilters();
          }}
          className="h-8 px-2 lg:px-3"
        >
          Reset
          <X />
        </Button>
      ) : null}
      {/* Filter Data Option */}
      {filterData && (
        <div className="ml_auto">
          <select
            className="border border-input bg-background rounded-md text-sm px-3 h-8"
            value={selectedFilter ?? ""}
            onChange={(e) => {
              const selected = e.target.value;
              onFilterChange?.(selected); // 🔥 this triggers Epic update
            }}
          >
            {filterData.map((item) => (
              <option key={item.value} value={item.value}>
                {item.label}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Column visibility toggle */}
      <DataTableViewOptions table={table} />
    </div>
  );
}
