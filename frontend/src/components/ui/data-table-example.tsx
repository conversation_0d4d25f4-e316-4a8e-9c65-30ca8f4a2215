// Example usage of column visibility feature in your data table columns

import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "@/components/ui/data-table-column-header";

// Example Payment type
type Payment = {
    id: string;
    amount: number;
    status: "pending" | "processing" | "success" | "failed";
    email: string;
};

// Example columns with column visibility features
export const exampleColumns: ColumnDef<Payment>[] = [
    {
        accessorKey: "status",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Status" />
        ),
    },
    {
        accessorKey: "email",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Email" />
        ),
    },
    {
        accessorKey: "amount",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Amount" />
        ),
        cell: ({ row }) => {
            const amount = parseFloat(row.getValue("amount"));
            const formatted = new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: "USD",
            }).format(amount);

            return <div className="text-right font-medium">{formatted}</div>;
        },
    },
    {
        id: "actions",
        enableHiding: false, // This column cannot be hidden
        cell: ({ row }) => {
            const payment = row.original;

            return (
                <div className="text-right">
                    <button onClick={() => console.log(payment)}>
                        View Details
                    </button>
                </div>
            );
        },
    },
];

// Usage example in your component:
/*
import { DataTable } from "@/components/ui/data-table";

<DataTable
  columns={exampleColumns}
  data={paymentsData}
  filterColumns={[
    {
      id: "status",
      placeholder: "Filter status...",
      options: [
        { label: "Pending", value: "pending" },
        { label: "Processing", value: "processing" },
        { label: "Success", value: "success" },
        { label: "Failed", value: "failed" },
      ],
    },
  ]}
/>
*/
