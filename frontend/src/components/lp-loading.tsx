"use client";

import Image from "next/image";

export function LPLoading() {
  return (
    <div className="flex flex-col items-center justify-center h-full animate-fade-in bg-gradient-to-br ">
      <span className="relative flex h-16 w-16 mb-6">
        <Image
          src="/icon.webp"
          alt="Loading icon"
          width={50}
          height={50}
          className="w-full h-full"
        />
        <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-gradient-to-tr from-red-400 via-pink-500 to-yellow-400 opacity-75"></span>
        <span className="relative inline-flex rounded-full h-10 w-10 bg-gradient-to-tr from-red-400 via-pink-500 to-yellow-400 opacity-90"></span>
      </span>
    </div>
  );
}
