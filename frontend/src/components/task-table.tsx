"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import { taskColumns, Task } from "@/components/task-columns";
import React from "react";
import { IssueWorklogs } from "./performance-analysis/issue-work-logs";

interface TaskTableProps {
    data: Task[];
}

export function TaskTable({ data }: TaskTableProps) {
    const initialSort = [
        {
            id: "updated",
            desc: true,
        },
    ];

    const assigneeOptions = React.useMemo(() => {
        const assignees = Array.from(
            new Set(
                (Array.isArray(data) ? data : []).map((task: Task) => task?.assignee ?? "")
            )
        );
        return assignees
            .filter(Boolean)
            .sort((a, b) => a.localeCompare(b, undefined, { sensitivity: "base" }))
            .map((assignee) => ({ label: assignee, value: assignee }));
    }, [data]);

    const statusOptions = React.useMemo(() => {
        const statuses = Array.from(
            new Set(
                (Array.isArray(data) ? data : []).map((task: Task) => task?.status ?? "")
            )
        );
        return statuses
            .filter(Boolean)
            .sort((a, b) => a.localeCompare(b))
            .map((status) => ({ label: status, value: status }));
    }, [data]);

    const priorityOptions = React.useMemo(() => {
        const priorities = Array.from(
            new Set(
                (Array.isArray(data) ? data : []).map((task: Task) => task?.priority ?? "")
            )
        );
        return priorities
            .filter(Boolean)
            .sort((a, b) => a.localeCompare(b))
            .map((priority) => ({ label: priority, value: priority }));
    }, [data]);

    const typeOptions = React.useMemo(() => {
        const types = Array.from(
            new Set(
                (Array.isArray(data) ? data : []).map((task: Task) => task?.type ?? "")
            )
        );
        return types
            .filter(Boolean)
            .sort((a, b) => a.localeCompare(b))
            .map((type) => ({ label: type, value: type }));
    }, [data]);

    const parentNameOptions = React.useMemo(() => {
        const parentNames = Array.from(
            new Set(
                (Array.isArray(data) ? data : []).map((task: Task) => task?.parent_name ?? "")
            )
        );
        return parentNames
            .filter(Boolean)
            .sort((a, b) => a.localeCompare(b, undefined, { sensitivity: "base" }))
            .map((parentName) => ({ label: parentName, value: parentName }));
    }, [data]);

    const categoryOptions = React.useMemo(() => {
        const categories = Array.from(
            new Set(
                (Array.isArray(data) ? data : []).map((task: Task) => task?.category ?? "")
            )
        );
        return categories
            .filter(Boolean)
            .sort((a, b) => a.localeCompare(b, undefined, { sensitivity: "base" }))
            .map((category) => ({ label: category, value: category }));
    }, [data]);

    const filterColumns = [
        {
            id: "assignee",
            placeholder: "Assignee",
            options: assigneeOptions,
        },
        {
            id: "status",
            placeholder: "Status",
            options: statusOptions,
        },
        {
            id: "priority",
            placeholder: "Priority",
            options: priorityOptions,
        },
        {
            id: "type",
            placeholder: "Type",
            options: typeOptions,
        },
        {
            id: "category",
            placeholder: "Category",
            options: categoryOptions,
        },
        {
            id: "parent_name",
            placeholder: "Parent Name",
            options: parentNameOptions,
        },
    ];

    return (
        <Card>
            <CardHeader>
                <CardTitle>Task Details</CardTitle>
            </CardHeader>
            <CardContent>
                <DataTable
                    columns={taskColumns}
                    data={data || []}
                    filterColumns={filterColumns}
                    initialSort={initialSort}
                    renderExpandedRow={row => {
                        return <IssueWorklogs worklogs={row.original.worklogs} />;
                    }}
                />
            </CardContent>
        </Card>
    );
}
