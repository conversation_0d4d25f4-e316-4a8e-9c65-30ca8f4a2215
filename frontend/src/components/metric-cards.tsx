"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { LucideIcon } from "lucide-react";
import {Badge} from "@/components/ui/badge";

interface MetricCardProps {
  title: string;
  value: string | number | React.ReactNode;
  description?: string;
  icon: LucideIcon;
  iconColor?: string;
  isWarning?: boolean;
}

interface MetricCardsProps {
  metrics: MetricCardProps[];
  columns?: 1 | 2 | 3 | 4 | 5;
  section: string;
}

export function MetricCards({
  metrics,
  columns = 5,
  section,
}: MetricCardsProps) {
  const getGridColumns = () => {
    switch (columns) {
      case 2:
        return "grid-cols-2";
      case 3:
        return "grid-cols-2 sm:grid-cols-3";
      case 4:
        return "grid-cols-2 sm:grid-cols-4 md:grid-cols-4";
      case 5:
        return "grid-cols-2 sm:grid-cols-3 md:grid-cols-5";
      default:
        return "grid-cols-2 sm:grid-cols-3 md:grid-cols-5";
    }
  };

  return (
    <div className="min-w-0">
      <div className="font-bold mb-2">{section}</div>
      <div
        className={`grid ${getGridColumns()} gap-2 sm:gap-1 md:gap-3 px-1 sm:px-0 min-w-0`}
      >
        {metrics.map((metric, index) => {
          const Icon = metric.icon;
          return (
            <Card key={index} className="min-w-0">
              <CardHeader className="">
                  <div className="flex flex-row items-center justify-between space-y-0">
                      <CardTitle className="text-sm font-medium">
                          {metric.title}
                      </CardTitle>
                      <Icon
                          strokeWidth={3}
                          className={`h-4 w-4 ${metric.iconColor || "text-muted-foreground"
                          }`}
                      />
                  </div>
                  {metric.isWarning && (
                      <Badge
                          className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200"
                          variant="secondary"
                      >
                          WARNING
                      </Badge>
                  )}
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metric.value}</div>
                {metric.description && (
                  <p className="text-xs text-muted-foreground truncate">
                    {metric.description}
                  </p>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
