"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import { workLogColumns, WorkLog } from "@/components/work-log-columns";
import React from "react";

interface WorkLogTableProps {
  data: WorkLog[];
}

export function WorkLogTable({ data }: WorkLogTableProps) {
  const initialSort = [
    {
      id: "work_log_date",
      desc: true,
    },
  ];

  const engineerOptions = React.useMemo(() => {
    const names = Array.from(
      new Set(
        (Array.isArray(data) ? data : []).map((row: any) => row?.name ?? "")
      )
    );
    return names
      .filter(Boolean)
      .sort((a, b) => a.localeCompare(b, undefined, { sensitivity: "base" }))
      .map((name) => ({ label: name, value: name }));
  }, [data]);

  const typeOptions = React.useMemo(() => {
    const types = Array.from(
      new Set(
        (Array.isArray(data) ? data : []).map((row: any) => row?.type ?? "")
      )
    );
    return types
      .filter(Boolean)
      .sort((a, b) => a.localeCompare(b))
      .map((type) => ({ label: type, value: type }));
  }, [data]);

  const activityOptions = React.useMemo(() => {
    const activities = Array.from(
      new Set(
        (Array.isArray(data) ? data : []).map((row: any) => {
          const match = row?.work_log_comment?.match(/\[([^\]]+)\]/);
          const activity = match
            ? match[1]
            : row?.work_log_comment?.split("\n")[0];
          return activity?.trim();
        })
      )
    );
    return activities
      .filter(Boolean)
      .filter((activity) => activity && activity.length > 0) // Extra filtering
      .sort((a, b) => a.localeCompare(b, undefined, { sensitivity: "base" })) // Case-insensitive sort
      .map((activity) => ({ label: activity, value: activity }));
  }, [data]);

  const dateOptions = React.useMemo(() => {
    const dateSet = new Set<string>();
    (Array.isArray(data) ? data : []).forEach((row: any) => {
      if (row?.work_log_date) {
        const value = new Date(row.work_log_date).toISOString().split("T")[0];
        dateSet.add(value);
      }
    });
    return Array.from(dateSet)
      .sort((a, b) => b.localeCompare(a))
      .map((value) => {
        const dateObj = new Date(value);
        return {
          label: dateObj.toLocaleDateString(),
          value,
        };
      });
  }, [data]);

  const filterColumns = [
    {
      id: "name",
      placeholder: "Name",
      options: engineerOptions,
    },
    {
      id: "type",
      placeholder: "Type",
      options: typeOptions,
    },
    {
      id: "work_log_comment",
      placeholder: "Activity",
      options: activityOptions,
    },
    {
      id: "work_log_date",
      placeholder: "Date",
      options: dateOptions,
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Work Log Details</CardTitle>
      </CardHeader>
      <CardContent>
        <DataTable
          columns={workLogColumns}
          data={data}
          initialSort={initialSort}
          filterColumns={filterColumns}
        />
      </CardContent>
    </Card>
  );
}
