import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ExternalLink } from "lucide-react";
import { getTypeColor, formatTimeSpent } from "@/lib/utils";
import * as React from "react";
import { EpicUserIssue } from "@/types/epicuser";


export const engineerIssueColumns: ColumnDef<EpicUserIssue>[] = [
  {
    accessorKey: "type",
    header: "Type",
    cell: ({ row }) => (
      <Badge
        className={getTypeColor(row.original.type) + " text-xs px-2 py-1"}
        variant="secondary"
      >
        {row.original.type}
      </Badge>
    ),
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "issue_name",
    header: "Issue Name",
    cell: ({ row }) => (
      <div
        className="truncate max-w-[120px] sm:max-w-md"
        title={row.original.issue_name}
      >
        {row.original.issue_name}
      </div>
    ),
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.original.status;
      let color = "bg-gray-100 text-gray-800";
      switch (status) {
        case "Done":
          color =
            "bg-emerald-100 dark:bg-emerald-800 text-emerald-800 dark:text-emerald-100";
          break;
        case "In Progress":
          color =
            "bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100";
          break;
        case "Backlog":
          color =
            "bg-slate-100 dark:bg-slate-800 text-slate-800 dark:text-slate-100";
          break;
        case "To Do":
          color = "bg-sky-100 dark:bg-sky-800 text-sky-800 dark:text-sky-100";
          break;
      }
      return (
        <Badge className={color + " text-xs px-2 py-1"} variant="secondary">
          {status.toUpperCase()}
        </Badge>
      );
    },
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "ticket_status",
    header: "Ticket status",
    cell: ({ row }) => {
      const status = row.original.ticket_status;
      let color = "bg-gray-100 text-gray-800";
      switch (status) {
        case "Done":
          color =
            "bg-emerald-100 dark:bg-emerald-800 text-emerald-800 dark:text-emerald-100";
          break;
        case "In Progress":
          color =
            "bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100";
          break;
        case "Backlog":
          color =
            "bg-slate-100 dark:bg-slate-800 text-slate-800 dark:text-slate-100";
          break;
        case "To Do":
          color = "bg-sky-100 dark:bg-sky-800 text-sky-800 dark:text-sky-100";
          break;
      }
      return (
        <Badge className={color + " text-xs px-2 py-1"} variant="secondary">
          {status ? String(status).toUpperCase() : "-"}
        </Badge>
      );
    },
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "time_spent_human",
    header: "Total Time Spent",
    cell: ({ row }) => formatTimeSpent(row.original.time_spent_human),
    enableSorting: true,
  },
  {
    accessorKey: "time_spent_detailing_human",
    header: "Detailing Time Spent",
    cell: ({ row }) => formatTimeSpent(row.original.time_spent_detailing_human),
    enableSorting: true,
  },
  {
    accessorKey: "time_spent_operation_human",
    header: "Operation Time Spent",
    cell: ({ row }) => formatTimeSpent(row.original.time_spent_operation_human),
    enableSorting: true,
  },
  {
    accessorKey: "last_activity_at",
    header: "Date",
    cell: ({ row }) => (
      <div className="text-xs sm:text-sm">
        {row.original.worklogs !== null && row.original.worklogs[row.original.worklogs?.length - 1]?.created_at
          ? new Intl.DateTimeFormat('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric'
          }).format(new Date(row.original.worklogs[row.original.worklogs?.length - 1].created_at))
          : "-"}
      </div>
    ),
    enableSorting: true,
  },
  {
    accessorKey: "expected_start_date",
    header: "Expected Start Date",
    cell: ({ row }) => (
      <div className="text-xs sm:text-sm">
        {row.original.expected_start_date
          ? new Intl.DateTimeFormat('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric'
          }).format(new Date(row.original.expected_start_date))
          : "-"}
      </div>
    ),
    enableSorting: true,
  },
  {
    accessorKey: "expected_end_date",
    header: "Expected End Date",
    cell: ({ row }) => (
      <div className="text-xs sm:text-sm">
        {row.original.expected_end_date
          ? new Intl.DateTimeFormat('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric'
          }).format(new Date(row.original.expected_end_date))
          : "-"}
      </div>
    ),
    enableSorting: true,
  },
  {
    accessorKey: "sp",
    header: "SP",
    cell: ({ row }) => <Badge variant="outline">{row.original.sp} SP</Badge>,
    enableSorting: true,
  },
  {
    id: "actions",
    header: "",
    cell: ({ row }) => (
      <Button
        variant="ghost"
        size="icon"
        className="h-7 w-7"
        onClick={() => window.open(row.original.link_ticket, "_blank")}
      >
        <ExternalLink className="h-3 w-3" />
      </Button>
    ),
    enableSorting: false,
    enableColumnFilter: false,
  },
];

export const engineerIssueColumnsWithName: ColumnDef<EpicUserIssue>[] = [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => (
      <div
        className="truncate max-w-[120px] sm:max-w-md"
        title={row.original.name}
      >
        {row.original.name}
      </div>
    ),
    enableSorting: true,
    enableColumnFilter: true,
  },
  ...engineerIssueColumns,
];

export const engineerIssueColumnsTaskRecommendation: ColumnDef<EpicUserIssue>[] = [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => (
      <div
        className="truncate max-w-[120px] sm:max-w-md"
        title={row.original.name}
      >
        {row.original.name}
      </div>
    ),
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "type",
    header: "Type",
    cell: ({ row }) => (
      <Badge
        className={getTypeColor(row.original.type) + " text-xs px-2 py-1"}
        variant="secondary"
      >
        {row.original.type}
      </Badge>
    ),
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "issue_name",
    header: "Issue Name",
    cell: ({ row }) => (
      <div
        className="truncate max-w-[120px] sm:max-w-md"
        title={row.original.issue_name}
      >
        {row.original.issue_name}
      </div>
    ),
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.original.status;
      let color = "bg-gray-100 text-gray-800";
      switch (status) {
        case "Done":
          color =
            "bg-emerald-100 dark:bg-emerald-800 text-emerald-800 dark:text-emerald-100";
          break;
        case "In Progress":
          color =
            "bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100";
          break;
        case "Backlog":
          color =
            "bg-slate-100 dark:bg-slate-800 text-slate-800 dark:text-slate-100";
          break;
        case "To Do":
          color = "bg-sky-100 dark:bg-sky-800 text-sky-800 dark:text-sky-100";
          break;
      }
      return (
        <Badge className={color + " text-xs px-2 py-1"} variant="secondary">
          {status.toUpperCase()}
        </Badge>
      );
    },
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "sp",
    header: "SP",
    cell: ({ row }) => <Badge variant="outline">{row.original.sp} SP</Badge>,
    enableSorting: true,
  },
  {
    id: "actions",
    header: "",
    cell: ({ row }) => (
      <Button
        variant="ghost"
        size="icon"
        className="h-7 w-7"
        onClick={() => window.open(row.original.link_ticket, "_blank")}
      >
        <ExternalLink className="h-3 w-3" />
      </Button>
    ),
    enableSorting: false,
    enableColumnFilter: false,
  },
];
export const engineerIssueColumnsHandoverRecommendation: ColumnDef<EpicUserIssue>[] = [
  {
    accessorKey: "handoverTo",
    header: "Handover to",
    cell: ({ row }) => (
      <div
        className="truncate max-w-[120px] sm:max-w-md"
        title={row.original.handoverTo}
      >
        {row.original.handoverTo}
      </div>
    ),
    enableSorting: true,
    enableColumnFilter: true,
  },
  ...engineerIssueColumnsTaskRecommendation
];

export const sdetIssueColumns: ColumnDef<any>[] = [
  {
    accessorKey: "type_task",
    header: "Type Task",
    cell: ({ row }) => (
      <Badge
        className={getTypeColor(row.original.type_task) + " text-xs px-2 py-1"}
        variant="secondary"
      >
        {row.original.type_task}
      </Badge>
    ),
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "task_id",
    header: "Task ID",
    cell: ({ row }) => (
      <a
        className="truncate max-w-[120px] sm:max-w-md underline"
        title={row.original.task_link}
        href={row.original.task_link}
        target="_blank"
      >
        {row.original.task_link.replace("https://lionparcel.atlassian.net/browse/", "")}
      </a>
    ),
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "task_name",
    header: "Issue Name",
    cell: ({ row }) => (
      <div
        className="truncate max-w-[120px] sm:max-w-md"
        title={row.original.task_name}
      >
        {row.original.task_name}
      </div>
    ),
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.original.status;
      let color = "bg-gray-100 text-gray-800";
      switch (status) {
        case "Done":
          color =
            "bg-emerald-100 dark:bg-emerald-800 text-emerald-800 dark:text-emerald-100";
          break;
        case "In Progress":
          color =
            "bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100";
          break;
        case "Backlog":
          color =
            "bg-slate-100 dark:bg-slate-800 text-slate-800 dark:text-slate-100";
          break;
        case "To Do":
          color = "bg-sky-100 dark:bg-sky-800 text-sky-800 dark:text-sky-100";
          break;
      }
      return (
        <Badge className={color + " text-xs px-2 py-1"} variant="secondary">
          {status.toUpperCase()}
        </Badge>
      );
    },
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "time_spent_human",
    header: "Total Time Spent",
    cell: ({ row }) => (
      <div
        className="truncate max-w-[120px] sm:max-w-md"
        title={row.original.time_spent_hours}
      >
        {row.original.time_spent_hours.toFixed(2)} Hour
      </div>
    ),
    enableSorting: true,
  },
  {
    accessorKey: "sp",
    header: "SP",
    cell: ({ row }) => <Badge variant="outline">{row.original.sp} SP</Badge>,
    enableSorting: true,
  }
];

export const paretoEpicColumns: ColumnDef<any>[] = [
  {
    accessorKey: "task_id",
    header: "Task ID",
    cell: ({ row }) => (
      <a
        className="truncate max-w-[120px] sm:max-w-md underline"
        title={row.original.task_link}
        href={row.original.task_link}
        target="_blank"
      >
        {row.original.task_link.replace("https://lionparcel.atlassian.net/browse/", "")}
      </a>
    ),
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "task_name",
    header: "Issue Name",
    cell: ({ row }) => (
      <div
        className="truncate max-w-[120px] sm:max-w-md"
        title={row.original.task_name}
      >
        {row.original.task_name}
      </div>
    ),
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.original.status;
      let color = "bg-gray-100 text-gray-800";
      switch (status) {
        case "Done":
          color =
            "bg-emerald-100 dark:bg-emerald-800 text-emerald-800 dark:text-emerald-100";
          break;
        case "In Progress":
          color =
            "bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100";
          break;
        case "Backlog":
          color =
            "bg-slate-100 dark:bg-slate-800 text-slate-800 dark:text-slate-100";
          break;
        case "To Do":
          color = "bg-sky-100 dark:bg-sky-800 text-sky-800 dark:text-sky-100";
          break;
      }
      return (
        <Badge className={color + " text-xs px-2 py-1"} variant="secondary">
          {status.toUpperCase()}
        </Badge>
      );
    },
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "time_spent_human",
    header: "Total Time Spent",
    cell: ({ row }) => (
      <div
        className="truncate max-w-[120px] sm:max-w-md"
        title={row.original.time_spent_hours}
      >
        {row.original.time_spent_hours.toFixed(2)} Hour
      </div>
    ),
    enableSorting: true,
  },
  {
    accessorKey: "sp",
    header: "SP",
    cell: ({ row }) => <Badge variant="outline">{row.original.sp} SP</Badge>,
    enableSorting: true,
  },
  {
    accessorKey: "assignee",
    header: "Assignee",
    cell: ({ row }) => (
      <div
        className="truncate max-w-[120px] sm:max-w-md"
        title={row.original.assignee}
      >
        {row.original.assignee}
      </div>
    ),
    enableSorting: true,
    enableColumnFilter: true,
  }
];
export const paretoTaskColumns: ColumnDef<any>[] = [
  {
    accessorKey: "assignee",
    header: "Assignee",
    cell: ({ row }) => (
      <div
        className="truncate max-w-[120px] sm:max-w-md"
        title={row.original.assignee}
      >
        {row.original.assignee}
      </div>
    ),
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "task_id",
    header: "Task ID",
    cell: ({ row }) => (
      <a
        className="truncate max-w-[120px] sm:max-w-md underline"
        title={row.original.task_link}
        href={row.original.task_link}
        target="_blank"
      >
        {row.original.task_link.replace("https://lionparcel.atlassian.net/browse/", "")}
      </a>
    ),
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "task_name",
    header: "Issue Name",
    cell: ({ row }) => (
      <div
        className="truncate max-w-[120px] sm:max-w-md"
        title={row.original.task_name}
      >
        {row.original.task_name}
      </div>
    ),
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.original.status;
      let color = "bg-gray-100 text-gray-800";
      switch (status) {
        case "Done":
          color =
            "bg-emerald-100 dark:bg-emerald-800 text-emerald-800 dark:text-emerald-100";
          break;
        case "In Progress":
          color =
            "bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100";
          break;
        case "Backlog":
          color =
            "bg-slate-100 dark:bg-slate-800 text-slate-800 dark:text-slate-100";
          break;
        case "To Do":
          color = "bg-sky-100 dark:bg-sky-800 text-sky-800 dark:text-sky-100";
          break;
      }
      return (
        <Badge className={color + " text-xs px-2 py-1"} variant="secondary">
          {status.toUpperCase()}
        </Badge>
      );
    },
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "time_spent_human",
    header: "Total Time Spent",
    cell: ({ row }) => (
      <div
        className="truncate max-w-[120px] sm:max-w-md"
        title={row.original.time_spent_hours}
      >
        {row.original.time_spent_hours.toFixed(2)} Hour
      </div>
    ),
    enableSorting: true,
  },
  {
    accessorKey: "sp",
    header: "SP",
    cell: ({ row }) => <Badge variant="outline">{row.original.sp} SP</Badge>,
    enableSorting: true,
  }
];

