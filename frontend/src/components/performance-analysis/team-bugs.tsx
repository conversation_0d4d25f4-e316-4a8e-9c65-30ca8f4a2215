"use client";

import { useSuspenseQuery } from "@tanstack/react-query";
import { useSelector } from "react-redux";
import { format } from "date-fns";
import axios from "axios";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Bug, ExternalLink } from "lucide-react";
import type { BugResponse } from "@/types/bug";
import type { RootState } from "@/app/store";
import { DataTable } from "../ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { API_BASE_URL } from "@/constants";

// Bug table columns
const bugColumns: ColumnDef<any>[] = [
  {
    accessorKey: "bug_name",
    header: "Bug Name",
    cell: ({ row }) => (
      <div
        className="truncate max-w-[200px] sm:max-w-md"
        title={row.original.bug_name}
      >
        {row.original.bug_name}
      </div>
    ),
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "severity_bug",
    header: "Severity",
    cell: ({ row }) => {
      const severity = row.original.severity_bug;
      let color = "bg-gray-100 text-gray-800";
      switch (severity) {
        case "High":
          color = "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
          break;
        case "Medium":
          color =
            "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
          break;
        case "Low":
          color =
            "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
          break;
      }
      return (
        <Badge className={color} variant="secondary">
          {severity}
        </Badge>
      );
    },
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "accident_bug",
    header: "Accident Bug",
    cell: ({ row }) => {
      const accidentBug = row.original.accident_bug;
      let color = "bg-gray-100 text-gray-800";
      switch (accidentBug) {
        case "Bug Case Not Covered":
          color = "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
          break;
        case "Bug in Test Case":
          color =
            "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
          break;
        case "Out Of Requirement":
          color =
            "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
          break;
        case "Adjustment Test Case":
          color =
            "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
          break;
        case "Code Collison":
          color =
            "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200";
          break;
        case "Changes Design":
          color =
            "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200";
          break;
        case "Bug Existing Feature":
          color =
            "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200";
          break;
      }
      return (
        <Badge className={color} variant="secondary">
          {accidentBug || "N/A"}
        </Badge>
      );
    },
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "pe_name",
    header: "Platform Engineer",
    cell: ({ row }) => (
      <div className="truncate max-w-[120px]" title={row.original.pe_name}>
        {row.original.pe_name || "N/A"}
      </div>
    ),
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "squad",
    header: "Squad",
    cell: ({ row }) => (
      <Badge variant="outline" className="text-xs">
        {row.original.squad}
      </Badge>
    ),
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "assignee",
    header: "Assignee",
    cell: ({ row }) => (
      <div className="truncate max-w-[120px]" title={row.original.assignee}>
        {row.original.assignee}
      </div>
    ),
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "reporter",
    header: "Reporter",
    cell: ({ row }) => (
      <div className="truncate max-w-[120px]" title={row.original.reporter}>
        {row.original.reporter}
      </div>
    ),
    enableSorting: true,
    enableColumnFilter: true,
  },
  {
    accessorKey: "ticket_created_at",
    header: "Created",
    cell: ({ row }) => (
      <span className="text-xs">
        {new Date(row.original.ticket_created_at).toLocaleDateString()}
      </span>
    ),
    enableSorting: true,
  },
  {
    id: "actions",
    header: "",
    cell: ({ row }) => (
      <Button
        variant="ghost"
        size="icon"
        className="h-7 w-7"
        onClick={() => window.open(row.original.bug_link, "_blank")}
      >
        <ExternalLink className="h-3 w-3" />
      </Button>
    ),
    enableSorting: false,
    enableColumnFilter: false,
  },
];

export function TeamBugs() {
  const { startDate, endDate } = useSelector(
    (state: RootState) => state.dateFilter
  );

  const { data: bugData } = useSuspenseQuery<BugResponse>({
    queryKey: ["team-bugs", startDate, endDate],
    queryFn: async () => {
      const res = await axios.get(API_BASE_URL + `/api/jira/bug`, {
        headers: {
          Accept: "application/json",
        },
        params: {
          start_date: startDate
            ? format(new Date(startDate), "yyyy-MM-dd")
            : undefined,
          end_date: endDate
            ? format(new Date(endDate), "yyyy-MM-dd")
            : undefined,
        },
      });
      const emptyData: BugResponse = {
        bugs: [],
      };
      return res.data ?? emptyData;
    },
  });

  // Calculate bug statistics
  const bugStats = {
    total: bugData.bugs.length,
    high: bugData.bugs.filter((bug) => bug.severity_bug === "High").length,
    medium: bugData.bugs.filter((bug) => bug.severity_bug === "Medium").length,
    low: bugData.bugs.filter((bug) => bug.severity_bug === "Low").length,
    bySquad: bugData.bugs.reduce((acc, bug) => {
      acc[bug.squad] = (acc[bug.squad] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    byAccidentBug: bugData.bugs.reduce((acc, bug) => {
      if (bug.accident_bug) {
        acc[bug.accident_bug] = (acc[bug.accident_bug] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>),
    byPlatformEngineer: bugData.bugs.reduce((acc, bug) => {
      if (bug.pe_name) {
        acc[bug.pe_name] = (acc[bug.pe_name] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>),
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Bug className="h-5 w-5 mr-2" />
          Team Bugs
        </CardTitle>
      </CardHeader>
      <CardContent>
        {bugData.bugs.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-4 mb-4">
              <Bug className="h-8 w-8 text-gray-400 dark:text-gray-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              No Bugs Found
            </h3>
            <p className="text-gray-500 dark:text-gray-400 max-w-sm">
              No bug data is available for the selected date range. Try
              adjusting your date filter or check if bugs exist.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Bug Statistics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card className="p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">
                    {bugStats.total}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Total Bugs
                  </div>
                </div>
              </Card>
              <Card className="p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {bugStats.high}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    High Severity
                  </div>
                </div>
              </Card>
              <Card className="p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">
                    {bugStats.medium}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Medium Severity
                  </div>
                </div>
              </Card>
              <Card className="p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {bugStats.low}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Low Severity
                  </div>
                </div>
              </Card>
            </div>

            {/* Squad Breakdown */}
            {Object.keys(bugStats.bySquad).length > 0 && (
              <div>
                <h4 className="text-sm font-semibold mb-3">Bugs by Squad</h4>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(bugStats.bySquad).map(([squad, count]) => (
                    <Badge key={squad} variant="outline" className="text-xs">
                      {squad}: {count}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Accident Bug Breakdown */}
            {Object.keys(bugStats.byAccidentBug).length > 0 && (
              <div>
                <h4 className="text-sm font-semibold mb-3">
                  Bugs by Accident Type
                </h4>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(bugStats.byAccidentBug).map(
                    ([accidentType, count]) => (
                      <Badge
                        key={accidentType}
                        variant="outline"
                        className="text-xs"
                      >
                        {accidentType}: {count}
                      </Badge>
                    )
                  )}
                </div>
              </div>
            )}

            {/* Bug By Platform Engineer */}
            {Object.keys(bugStats.byPlatformEngineer).length > 0 && (
              <div>
                <h4 className="text-sm font-semibold mb-3">
                  Bugs by Platform Engineer
                </h4>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(bugStats.byPlatformEngineer).map(
                    ([platformEngineer, count]) => (
                      <Badge
                        key={platformEngineer}
                        variant="outline"
                        className="text-xs"
                      >
                        {platformEngineer}: {count}
                      </Badge>
                    )
                  )}
                </div>
              </div>
            )}

            {/* Bug Table */}
            <div>
              <h4 className="text-sm font-semibold mb-3">Bug Details</h4>
              <DataTable
                columns={bugColumns}
                data={bugData.bugs}
                initialSort={[{ id: "ticket_created_at", desc: true }]}
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
