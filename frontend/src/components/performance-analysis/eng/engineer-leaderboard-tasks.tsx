import * as React from "react";
import { FileText } from "lucide-react";
import { TaskLeaderBoard } from "@/types/eng/leaderboard";
import { DataTable } from "@/components/ui/data-table";
import { engineeringTaskLeaderBoard } from "./engineering-engineer-leaderboard";

export function EngineeringTaskLeaderboardTasks({
  tasks,
}: {
  tasks: TaskLeaderBoard[];
}) {
  if (!tasks || tasks.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-center">
        <div className="rounded-full bg-muted/50 p-3 mb-3">
          <FileText className="h-6 w-6 text-muted-foreground" />
        </div>
        <h3 className="text-base font-medium text-foreground mb-1">
          No Task Assigned
        </h3>
        <p className="text-xs text-muted-foreground">
          Task will appear here once they are assigned.
        </p>
      </div>
    );
  }
  return (
    <div className="space-y-0">
      <DataTable
        columns={engineeringTaskLeaderBoard}
        data={tasks}
        disableSearch={true}
        initialSort={[{ id: "expected_start_date", desc: false }]}
      />
    </div>
  );
}
