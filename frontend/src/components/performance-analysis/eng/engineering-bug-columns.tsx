import { DevBugDetail, ProdBugDetail } from "@/types/eng/bug";
import { ColumnDef } from "@tanstack/react-table";
import { ButtonURL } from "./button-url";

const prodBugcolumns: (keyof ProdBugDetail)[] = [
  "summary",
  "url",
  "created_date",
  "function",
  "fixing_time",
  "testing_time",
  "resolution_time",
  "severity",
  "accident_bug",
  "squad",
];

const devBugcolumns: (keyof DevBugDetail)[] = [
    "summary",
    "url",
    "created_date",
    "assignee",
    "resolution_time",
    "severity",
    "accident_bug",
    "squad",
  ];

// Helper to check if the field is a date
const dateFields = new Set<string>([
  "created_date",
]);

export const devBugList: ColumnDef<DevBugDetail>[] = devBugcolumns.map((key) => ({
    accessorKey: key,
    header: key.replace(/_/g, " ").replace(/\b\w/g, (c) => c.toUpperCase()),
    cell: ({ row }) => {
      const value = row.original[key];
      if (key === "url" && typeof value === "string") {
        return (
          <ButtonURL url={value}></ButtonURL>
        );
      }
      const isDate = dateFields.has(key);
      const formattedValue =
        isDate && value
          ? new Intl.DateTimeFormat("en-GB", {
              day: "2-digit",
              month: "short",
              year: "numeric",
            }).format(new Date(value))
          : String(value ?? "-");
  
      return (
        <div
          className="truncate max-w-[120px] sm:max-w-md"
          title={formattedValue}
        >
          {formattedValue}
        </div>
      );
    },
    enableSorting: true,
    enableColumnFilter: true,
  }));

export const prodBugList: ColumnDef<ProdBugDetail>[] = prodBugcolumns.map((key) => ({
  accessorKey: key,
  header: key.replace(/_/g, " ").replace(/\b\w/g, (c) => c.toUpperCase()),
  cell: ({ row }) => {
    const value = row.original[key];
    if (key === "url" && typeof value === "string") {
      return (
        <ButtonURL url={value}></ButtonURL>
      );
    }
    const isDate = dateFields.has(key);
    const formattedValue =
      isDate && value
        ? new Intl.DateTimeFormat("en-GB", {
            day: "2-digit",
            month: "short",
            year: "numeric",
          }).format(new Date(value))
        : String(value ?? "-");

    return (
      <div
        className="truncate max-w-[120px] sm:max-w-md"
        title={formattedValue}
      >
        {formattedValue}
      </div>
    );
  },
  enableSorting: true,
  enableColumnFilter: true,
}));
