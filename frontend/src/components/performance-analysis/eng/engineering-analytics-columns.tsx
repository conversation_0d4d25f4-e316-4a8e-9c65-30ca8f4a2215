import { EngAnalytics, EngAnalyticsPerMember, EngAnalyticsPerRole } from "@/types/eng/analytics";
import { ColumnDef } from "@tanstack/react-table";

const analyticColums: (keyof EngAnalytics)[] = [
  "release_date",
  "total_epic",
  "total_sp",
  "total_manpower",
  "sp_per_manpower",
  "total_hour",
  "hour_per_sp",
];

const analyticPerRolesColums: (keyof EngAnalyticsPerRole)[] = [
  "role_name",
  "total_sp",
  "total_manpower",
  "sp_per_manpower",
  "total_hour",
  "hour_per_sp",
];

const analyticPerMemberColums: (keyof EngAnalyticsPerMember)[] = [
  "member_name",
  "total_sp",
  "total_hour",
  "hour_per_sp",
];

export const analyticPerMemberLists: ColumnDef<EngAnalyticsPerMember>[] = analyticPerMemberColums.map(
  (key) => ({
    accessorKey: key,
    header: key.replace(/_/g, " ").replace(/\b\w/g, (c) => c.toUpperCase()),
    cell: ({ row }) => {
      const value = row.original[key];
      const formattedValue = String(value ?? "-");

      return (
        <div className="truncate max-w-[120px] sm:max-w-md" title={formattedValue}>
          {formattedValue}
        </div>
      );
    },
    enableSorting: true,
    enableColumnFilter: false,
  })
);

export const analyticPerRolesLists: ColumnDef<EngAnalyticsPerRole>[] = analyticPerRolesColums.map(
  (key) => ({
    accessorKey: key,
    header: key.replace(/_/g, " ").replace(/\b\w/g, (c) => c.toUpperCase()),
    cell: ({ row }) => {
      const value = row.original[key];
      const formattedValue = String(value ?? "-");

      return (
        <div className="truncate max-w-[120px] sm:max-w-md" title={formattedValue}>
          {formattedValue}
        </div>
      );
    },
    enableSorting: true,
    enableColumnFilter: false,
  })
);

export const analyticLists: ColumnDef<EngAnalytics>[] = analyticColums.map(
  (key) => ({
    accessorKey: key,
    header: key.replace(/_/g, " ").replace(/\b\w/g, (c) => c.toUpperCase()),
    cell: ({ row }) => {
      const value = row.original[key];
      const formattedValue = String(value ?? "-");

      return (
        <div className="truncate max-w-[120px] sm:max-w-md" title={formattedValue}>
          {formattedValue}
        </div>
      );
    },
    enableSorting: true,
    enableColumnFilter: true,
  })
);
