import { Button } from "@/components/ui/button";
import { ExternalLink } from "lucide-react";

export function ButtonURL({url}: {url: string}){
    return (
        <Button
          variant="ghost"
          size="sm"
          className="w-fit"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            window.open(url, "_blank", "noopener,noreferrer");
          }}
        >
          <ExternalLink className="h-4 w-4" />
        </Button>
      );
}