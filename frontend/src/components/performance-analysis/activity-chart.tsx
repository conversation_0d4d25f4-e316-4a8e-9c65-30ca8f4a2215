"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { getActivityColor, getGroupedActivity, getGroupedActivityForSdet } from "@/lib/utils";
import { BarChart3, Info } from "lucide-react";


interface ActivityChartProps {
  data: any[];
  role: string;
}

// Categorized activity descriptions
const activityCategories = {
  epic: {
    title: "Work Log Epic",
    activities: [
      { code: "GROOMING-n", description: "Grooming session (n-th iteration)" },
      { code: "READ PRD", description: "Reading Product Requirement Document" },
      {
        code: "CHECK EXISTING FEATURE",
        description: "Reviewing existing features",
      },
      {
        code: "CHECK EXISTING CODE",
        description: "Reviewing existing codebase",
      },
      { code: "ANALYSIS DB", description: "Database analysis" },
      { code: "ANALYSIS API", description: "API analysis" },
      { code: "ANALYSIS UI & UX", description: "UI/UX analysis" },
      {
        code: "ANALYSIS TECHNICAL",
        description: "Technical analysis and investigation",
      },
      {
        code: "DETAILING",
        description: "Detailing requirements or tasks (khusus FE)",
      },
      { code: "DONE", description: "Task completed" },
      { code: "MATRIX", description: "Create Matrix for epic" },
    ],
  },
  ticket: {
    title: "Work Log Ticket",
    activities: [
      {
        code: "DETAILING",
        description: "Detailing requirements or tasks (BE dan FE)",
      },
      { code: "DISCUSSION", description: "Team discussions" },
      { code: "BLOCKED", description: "Blocked by an issue" },
      { code: "PAIRING", description: "Collaborative programming sessions" },
      { code: "CODE REVIEW", description: "Reviewing code changes" },
      { code: "DONE", description: "Task completed" },
    ],
  },
  additional: {
    title: "Additional Task (PTD)",
    activities: [
      { code: "DISCUSSION", description: "Team discussions" },
      {
        code: "PROJECT SA",
        description: "SA Project",
      },
      { code: "REVISIT", description: "Revisit Ticket" },
      { code: "ISSUE", description: "Resolve Issue" },
      { code: "MEETING", description: "Meeting" },
      {
        code: "ON LEAVE",
        description: "On Leave / Sick Leave / Holiday Nationals",
      },
      {
        code: "OTHERS",
        description:
          "Interview, workshop, LP ultah, farewell, etc. (non-productive)",
      },
    ],
  },
};

export function ActivityChart({ data, role }: ActivityChartProps) {
  // Extract activity types from comments
  const activityCounts = data.reduce((acc, item) => {
    const match = item.work_log_comment.match(/\[([^\]]+)\]/);
    const activity = (match ? match[1] : item.work_log_comment.split("\n")[0]) as string;
    let groupedActivity = "";
    if (role === "sdet") {
      groupedActivity = getGroupedActivityForSdet(item);
    } else if (role === "pe") {
      groupedActivity = getGroupedActivity(item, activity);
    } else {
      groupedActivity = activity.toUpperCase();
    }
    acc[groupedActivity] = (acc[groupedActivity] || 0) + item.work_log_time_spent_hour;
    return acc;
  }, {} as Record<string, number>);
  const sortedActivities = Object.entries(activityCounts)
    .sort(([, a], [, b]) => (b as number) - (a as number))
    .slice(0, 10);

  const totalHours = data.reduce((total, item) => {
    return total + item.work_log_time_spent_hour;
  }, 0);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Activity Breakdown</CardTitle>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="icon">
                <Info className="h-4 w-4" />
                <span className="sr-only">Activity descriptions</span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-96 max-h-96 overflow-y-auto">
              <div className="space-y-4">
                {Object.entries(activityCategories).map(([key, category]) => (
                  <div key={key} className="space-y-2">
                    <h4 className="font-semibold text-sm">{category.title}</h4>
                    <div className="space-y-1">
                      {category.activities.map((activity) => (
                        <p
                          key={activity.code}
                          className="text-xs text-muted-foreground"
                        >
                          • <strong>{activity.code}:</strong>{" "}
                          {activity.description}
                        </p>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </CardHeader>
      <CardContent>
        {sortedActivities.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-4 mb-4">
              <BarChart3 className="h-8 w-8 text-gray-400 dark:text-gray-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              No Activities Found
            </h3>
            <p className="text-gray-500 dark:text-gray-400 max-w-sm">
              No activity data is available for the selected period. Activities
              are extracted from work log comments.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-4">
            {sortedActivities.map(([activity, count]) => {
              const percentage = ((count as number) / totalHours * 100).toFixed(2);
              return (
                <div
                  key={activity}
                  className="flex items-center justify-between p-3 rounded-lg border"
                >
                  <div className="flex items-center space-x-2">
                    <Badge
                      className={getActivityColor(activity as string)}
                      variant="secondary"
                    >
                      {activity}
                    </Badge>
                  </div>
                  <span className="font-semibold text-lg">{(count as number).toFixed(2)} Hours ({percentage} %)</span>
                </div>
              )
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
