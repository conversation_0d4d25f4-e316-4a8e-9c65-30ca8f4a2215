"use client";

import axios from "axios";
import { useQuery, useQueries } from "@tanstack/react-query";
import { useSelector } from "react-redux";
import { RootState } from "@/app/store";
import { <PERSON>, CardH<PERSON>er, CardTitle, CardContent } from "../ui/card";
import { Skeleton } from "../ui/skeleton";
import { MetricCards } from "@/components/metric-cards";
import { DataTable } from "@/components/ui/data-table";
import { Badge } from "@/components/ui/badge";
import { ColumnDef } from "@tanstack/react-table";
import { Clock, TrendingUp, CheckCircle, ExternalLink } from "lucide-react";
import { API_BASE_URL } from "@/constants";
import { format } from "date-fns";
import { useMemo } from "react";
import { getStatusColor } from "@/components/task-columns";

interface Task {
    task_link: string;
    task_name: string;
    status: string;
    category: string;
    due_date?: string;
    start_date?: string;
}

interface TaskResponse {
    tasks: Task[];
}

interface CountResponse {
    count: number;
}

const OP_CATEGORIES = [
    { code: "CATEGORY-PROJECT-SA", title: "Project SA" },
    { code: "CATEGORY-FIXER", title: "Fixer" },
    { code: "CATEGORY-ISSUE", title: "Issue" },
    { code: "CATEGORY-TECHDEBT", title: "Tech Debt" },
] as const;

const formatJiraDate = (d?: string | null) => {
    try {
        if (!d) return format(new Date(), "yyyy-MM-dd");
        return format(new Date(d), "yyyy-MM-dd");
    } catch {
        return format(new Date(), "yyyy-MM-dd");
    }
};

export function Operation() {
    const { startDate, endDate } = useSelector((s: RootState) => s.dateFilter);
    const opStart = formatJiraDate(startDate ?? endDate ?? null);
    const opEnd = formatJiraDate(endDate ?? startDate ?? null);

    const operationJql = `project = "PRIN" AND "Category[Dropdown]" IN (CATEGORY-PROJECT-SA,CATEGORY-FIXER,CATEGORY-ISSUE,CATEGORY-TECHDEBT) AND type = Epic AND "Start date[Date]" >= "${opStart}" AND "Start date[Date]" <= "${opEnd}"`;

    const { data: operationData, isError: operationIsError } = useQuery<TaskResponse>({
        queryKey: ["operation-tasks", opStart, opEnd],
        queryFn: async () => {
            const res = await axios.get(API_BASE_URL + `/api/jira/task`, {
                headers: { Accept: "application/json" },
                params: { jql: operationJql },
            });
            return res.data as TaskResponse;
        },
        staleTime: 60 * 1000,
    });

    // Helper to extract Jira issue key from a task link like https://.../browse/KEY-123
    const extractIssueKey = (link?: string): string | null => {
        if (!link) return null;
        try {
            const url = new URL(link);
            const parts = url.pathname.split("/");
            const last = parts[parts.length - 1];
            return last?.split("?")[0] || null;
        } catch {
            // Fallback: simple split if URL constructor fails
            const parts = link.split("/");
            return parts[parts.length - 1]?.split("?")[0] || null;
        }
    };

    // Build queries to fetch child issues for each epic to aggregate SPs
    const epicKeys = useMemo(
        () => Array.from(new Set((operationData?.tasks ?? []).map(t => extractIssueKey(t.task_link)).filter(Boolean) as string[])),
        [operationData]
    );

    const buildChildIssuesQuery = (epicKey: string) => async () => {
        const res = await axios.get(API_BASE_URL + `/api/jira/task`, {
            headers: { Accept: "application/json" },
            params: {
                // As requested: fetch children by parent key derived from the epic link
                jql: `parent = "${epicKey}"`,
            },
        });
        return res.data as TaskResponse;
    };

    const childIssuesQueries = useQueries({
        queries: epicKeys.map((key) => ({
            queryKey: ["op-children", key],
            queryFn: buildChildIssuesQuery(key),
            staleTime: 60 * 1000,
        })),
    }) as Array<{
        data?: TaskResponse;
        isLoading: boolean;
    }>;

    // Aggregate SP Engineer (sp_dev) and SP QA (qa_point) per epic key
    const spByEpic = useMemo(() => {
        const m = new Map<string, { spDev: number; spQA: number }>();
        childIssuesQueries.forEach((q, idx) => {
            const key = epicKeys[idx];
            const tasks = q.data?.tasks ?? [];
            let spDev = 0;
            let spQA = 0;
            for (const ct of tasks as Array<any>) {
                spDev += Number(ct?.sp_dev ?? 0);
                spQA += Number(ct?.qa_point ?? 0);
            }
            m.set(key, { spDev, spQA });
        });
        return m;
    }, [childIssuesQueries, epicKeys]);

    const spLoadingByEpic = useMemo(() => {
        const m = new Map<string, boolean>();
        childIssuesQueries.forEach((q, idx) => {
            m.set(epicKeys[idx], !!q.isLoading);
        });
        return m;
    }, [childIssuesQueries, epicKeys]);

    const buildCountQuery = (categoryCode: string, statusJql: string) => async () => {
        const res = await axios.get(API_BASE_URL + `/api/jira/count`, {
            headers: { Accept: "application/json" },
            params: {
                jql: `project = "PRIN" AND "Category[Dropdown]" = ${categoryCode} AND type = Epic AND ${statusJql}`,
            },
        });
        return res.data as CountResponse;
    };

    const statusDefs = [
        { key: "todo" as const, title: "To Do", jql: `status = \"To Do\"`, icon: Clock, color: "text-gray-600", desc: "epics not started" },
        { key: "inprogress" as const, title: "In Progress", jql: `status IN (\"In Progress\", \"In Testing Dev (Optional)\", \"In testing stg (OPTIONAL)\", \"Ready To Test Dev (Optional)\", \"Ready To Test Stg (Optional)\", \"READY TO PR STG\")`, icon: TrendingUp, color: "text-blue-600", desc: "epics ongoing" },
        { key: "done" as const, title: "Done", jql: `status = \"Done\"`, icon: CheckCircle, color: "text-green-600", desc: "epics completed" },
    ];

    const opCountQueries = useQueries({
        queries: OP_CATEGORIES.flatMap((c) =>
            statusDefs.map((sd) => ({ queryKey: ["op-count", c.code, sd.key], queryFn: buildCountQuery(c.code, sd.jql), staleTime: 60 * 1000 }))
        ),
    }) as Array<{
        data?: CountResponse;
        isLoading: boolean;
    }>;

    const opAnyMetricLoading = opCountQueries.some((q) => q?.isLoading);

    const opMetricsByCategory = OP_CATEGORIES.map((c, catIdx) => {
        const metrics = statusDefs.map((sd, statusIdx) => {
            const q = opCountQueries[catIdx * statusDefs.length + statusIdx];
            return { title: sd.title, value: q?.data?.count ?? 0, icon: sd.icon, iconColor: sd.color, description: `${c.title} ${sd.desc}` };
        });
        const skeleton = statusDefs.map((sd) => ({ title: sd.title, value: <Skeleton className="h-8 w-16" />, icon: sd.icon, iconColor: sd.color, description: `${c.title} ${sd.desc}` }));
        return { category: c, metrics, skeleton };
    });

    const opTasksByCategory = useMemo(() => {
        const grouped: Record<string, Task[]> = {};
        const all = (operationData?.tasks ?? []) as Task[];
        for (const t of all) {
            const key = t.category || "UNKNOWN";
            if (!grouped[key]) grouped[key] = [];
            grouped[key].push(t);
        }
        return grouped;
    }, [operationData]);

    // Using DataTable's built-in filters; no local filter state needed

    return (
        <div className="space-y-6">
            {operationIsError && <div className="text-red-600 text-sm">Failed to load Operation data.</div>}

            {OP_CATEGORIES.map((cat, idx) => {
                const code = cat.code;
                const tasks = opTasksByCategory[code] ?? [];
                const statuses = Array.from(new Set(tasks.map((t) => t.status).filter(Boolean)));

                const metricPack = opMetricsByCategory[idx];

                return (
                    <Card key={code} className="border">
                        <CardHeader>
                            <CardTitle className="flex items-center justify-between">{cat.title}</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <MetricCards metrics={opAnyMetricLoading ? metricPack.skeleton : metricPack.metrics} columns={3} />

                            {/* Removed redundant manual filters; DataTable toolbar handles searching and filtering */}

                            <div>
                                {/* Use DataTable for consistent behavior */}
                                {/** Define columns for DataTable **/}
                                {
                                    (() => {
                                        type Row = Task;

                                        const columns: ColumnDef<Row, any>[] = [
                                            {
                                                accessorKey: "task_name",
                                                header: "Name",
                                                cell: info => {
                                                    const name = info.getValue() as string;
                                                    const link = info.row.original.task_link;
                                                    return (
                                                        <span className="inline-flex items-center gap-2">
                                                            {link ? (
                                                                <a
                                                                    href={link}
                                                                    target="_blank"
                                                                    rel="noopener noreferrer"
                                                                    aria-label="Open task in Jira"
                                                                    className="inline-flex"
                                                                >
                                                                    <ExternalLink className="h-4 w-4" />
                                                                </a>
                                                            ) : null}
                                                            <span>{name}</span>
                                                        </span>
                                                    );
                                                },
                                            },
                                            {
                                                accessorKey: "status",
                                                header: "Status",
                                                cell: (info) => {
                                                    const status = (info.getValue() as string) ?? "-";
                                                    return (
                                                        <Badge className={`${getStatusColor(status)} max-w-[10rem] truncate`} variant="secondary">
                                                            {status}
                                                        </Badge>
                                                    );
                                                },
                                            },
                                            {
                                                id: "sp_engineer",
                                                header: "SP Engineer",
                                                cell: (info) => {
                                                    const link = info.row.original.task_link;
                                                    const key = extractIssueKey(link);
                                                    const isLoading = key ? spLoadingByEpic.get(key) : false;
                                                    const totals = key ? spByEpic.get(key) : undefined;
                                                    return isLoading ? (
                                                        <Skeleton className="h-4 w-12" />
                                                    ) : (
                                                        <span>{totals?.spDev ?? 0}</span>
                                                    );
                                                },
                                            },
                                            {
                                                id: "sp_qa",
                                                header: "SP QA",
                                                cell: (info) => {
                                                    const link = info.row.original.task_link;
                                                    const key = extractIssueKey(link);
                                                    const isLoading = key ? spLoadingByEpic.get(key) : false;
                                                    const totals = key ? spByEpic.get(key) : undefined;
                                                    return isLoading ? (
                                                        <Skeleton className="h-4 w-12" />
                                                    ) : (
                                                        <span>{totals?.spQA ?? 0}</span>
                                                    );
                                                },
                                            },
                                            {
                                                accessorKey: "start_date",
                                                header: "Start Date",
                                                cell: info => info.getValue() ? format(new Date(info.getValue() as string), 'yyyy-MM-dd') : '-',
                                            },
                                            {
                                                accessorKey: "due_date",
                                                header: "End Date",
                                                cell: info => info.getValue() ? format(new Date(info.getValue() as string), 'yyyy-MM-dd') : '-',
                                            },
                                        ];

                                        const filterColumns = [
                                            { id: "status", placeholder: "Status", options: statuses.map(s => ({ label: s, value: s })) },
                                        ];

                                        return (
                                            <DataTable columns={columns} data={operationData ? tasks : []} filterColumns={filterColumns} />
                                        );
                                    })()
                                }
                            </div>
                        </CardContent>
                    </Card>
                );
            })}
        </div>
    );
}

export default Operation;
