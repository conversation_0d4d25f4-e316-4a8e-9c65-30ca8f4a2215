"use client";

import axios from "axios";
import { RootState } from "@/app/store";
import { useSuspenseQuery } from "@tanstack/react-query";
import {differenceInCalendarDays, format} from "date-fns";
import { usePathname } from "next/navigation";
import { useSelector } from "react-redux";
import { User, ArrowRight, Clock, CheckCircle, TrendingUp } from "lucide-react";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardFooter,
} from "../ui/card";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";
import { WorkLogTable } from "../work-log-table";
import { ActivityChart } from "./activity-chart";
import { PerformanceMetricsCards } from "./performance-metrics-cards";
import { TimeDistributionChart } from "./time-distribution-chart";
import Link from "next/link";
import { Avatar, AvatarFallback } from "../ui/avatar";
import { API_BASE_URL } from "@/constants";

interface TeamOverviewProps {
  jql?: string;
}

export function TeamOverview({ jql }: TeamOverviewProps) {
  const pathname = usePathname();

  const role = pathname.split("/")[1] || "pe";

  const { startDate, endDate } = useSelector(
    (state: RootState) => state.dateFilter
  );
  const endDateTimeDistribution = endDate ? endDate : startDate
  const totalDaysFilter = differenceInCalendarDays(new Date(endDateTimeDistribution), new Date(startDate)) + 1;
  const totalHoursFilter = totalDaysFilter * 8;

  const { data, isError } = useSuspenseQuery({
    queryKey: ["worklog", startDate, endDate, role],
    queryFn: async () => {
      const res = await axios.get(API_BASE_URL + `/api/jira/report`, {
        headers: {
          Accept: "application/json",
        },
        params: {
          start_date: startDate
            ? format(new Date(startDate), "yyyy-MM-dd")
            : undefined,
          end_date: endDate
            ? format(new Date(endDate), "yyyy-MM-dd")
            : undefined,
          jql,
        },
      });
      return res.data.issues;
    },
  });

  if (isError)
    return (
      <div className="flex flex-col items-center justify-center h-full animate-fade-in bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="flex items-center bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded mb-4 shadow-md animate-fade-in">
          <svg
            className="w-6 h-6 mr-3 text-red-500"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M18.364 5.636l-12.728 12.728M5.636 5.636l12.728 12.728"
            />
          </svg>
          <div>
            <span className="font-bold">Oops! Something went wrong.</span>
            <div className="text-sm mt-1">
              We couldn't load the data. Please check your connection or try
              again.
            </div>
          </div>
        </div>
        <button
          className="bg-red-500 to-yellow-400 hover:bg-red-600 text-white font-bold py-2 px-6 rounded shadow-lg focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-50 transition"
          onClick={() => window.location.reload()}
          aria-label="Retry loading data"
        >
          Retry
        </button>
      </div>
    );

  // Calculate individual engineer metrics
  const engineerMetrics = data.reduce((acc, item) => {
    if (!acc[item.name]) {
      acc[item.name] = {
        totalHours: 0,
        tickets: new Set(),
        storyPoints: 0,
        activities: new Set(),
        lastActivity: null,
      };
    }

    acc[item.name].totalHours += item.work_log_time_spent_hour;
    acc[item.name].tickets.add(item.link_ticket);
    acc[item.name].storyPoints += item.sp;
    acc[item.name].userID = item.user_id;
    acc[item.name].activities.add(
      (() => {
        const match = item.work_log_comment.match(/\[([^\]]+)\]/);
        return match ? match[1] : item.work_log_comment.split("\n")[0];
      })()
    );

    const activityDate = new Date(item.work_log_date);
    if (
      !acc[item.name].lastActivity ||
      activityDate > acc[item.name].lastActivity
    ) {
      acc[item.name].lastActivity = activityDate;
    }

    return acc;
  }, {} as Record<string, any>);

  const sortedEngineers = Object.entries(engineerMetrics)
    .map(([name, metrics]) => {
      const m = metrics as {
        totalHours: number;
        tickets: Set<any>;
        storyPoints: number;
        activities: Set<any>;
        lastActivity: Date | null;
        userID?: string;
      };
      return {
        name,
        ...m,
        ticketCount: m.tickets.size,
        efficiency: m.tickets.size > 0 ? m.totalHours / m.tickets.size : 0,
      };
    })
    .sort((a, b) => b.totalHours - a.totalHours);

  const getPerformanceLevel = (hours: number) => {
    const percentage = (hours / totalHoursFilter) * 100;
    if (percentage >= 80)
      return {
        level: "High",
        color:
          "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      };
    if (percentage >= 60)
      return {
        level: "Medium",
        color:
          "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      };
    return {
      level: "Low",
      color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    };
  };

  return (
    <div className="space-y-6">
      {/* Team Metrics */}
      <PerformanceMetricsCards data={data} />
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="h-5 w-5 mr-2" />
            Team Members
          </CardTitle>
        </CardHeader>
        <CardContent>
          {sortedEngineers.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-4 mb-4">
                <User className="h-8 w-8 text-gray-400 dark:text-gray-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                No Team Members Found
              </h3>
              <p className="text-gray-500 dark:text-gray-400 max-w-sm">
                No team member data is available for the selected date range.
                Try adjusting your date filter or check if work logs exist.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {sortedEngineers.map((engineer) => {
                const performance = getPerformanceLevel(
                  engineer.totalHours
                );

                return (
                  <Card key={engineer.name}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <Avatar>
                            <AvatarFallback className="bg-primary text-primary-foreground tracking-wide flex items-center justify-center">
                              {(() => {
                                const initials = engineer.name
                                  .split(" ")
                                  .map((n) => n[0])
                                  .join("")
                                  .toUpperCase();
                                // If initials are 3+ letters, only show first 2
                                return initials.length > 2
                                  ? initials.slice(0, 2)
                                  : initials;
                              })()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <h3 className="font-semibold text-sm">
                              {engineer.name}
                            </h3>
                            <Badge
                              className={performance.color}
                              variant="secondary"
                            >
                              {performance.level} Activity
                            </Badge>
                          </div>
                        </div>
                        <ArrowRight className="h-4 w-4 text-muted-foreground" />
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="flex items-center ">
                            <Clock className="h-3 w-3 mr-1" />
                            Hours
                          </span>
                          <span className="font-medium">
                            {engineer.totalHours.toFixed(1)}h
                          </span>
                        </div>

                        <div className="flex items-center justify-between text-sm">
                          <span className="flex items-center ">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Tickets
                          </span>
                          <span className="font-medium">
                            {engineer.ticketCount}
                          </span>
                        </div>

                        <div className="flex items-center justify-between text-sm">
                          <span className="flex items-center ">
                            <TrendingUp className="h-3 w-3 mr-1" />
                            Efficiency
                          </span>
                          <span className="font-medium">
                            {engineer.efficiency.toFixed(1)}h/ticket
                          </span>
                        </div>

                        {engineer.lastActivity && (
                          <div className="text-xs  mt-2">
                            Last activity:{" "}
                            {engineer.lastActivity.toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button className="w-full" variant="outline" asChild>
                        <Link
                          href={{
                            pathname: `/${role}/performance-analysis/detail`,
                            query: { user_id: engineer.userID },
                          }}
                        >
                          View Details
                        </Link>
                      </Button>
                    </CardFooter>
                  </Card>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Team Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <TimeDistributionChart data={data} />
        <ActivityChart data={data} />
      </div>

      {/* Work Log Table */}
      <WorkLogTable key="1" data={data} />
    </div>
  );
}
