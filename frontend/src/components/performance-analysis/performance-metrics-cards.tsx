"use client";

import { Clock, Users, TrendingUp } from "lucide-react";
import { MetricCards } from "@/components/metric-cards";

interface PerformanceMetricsCardsProps {
  data: any[];
}

export function PerformanceMetricsCards({
  data,
}: PerformanceMetricsCardsProps) {
  const totalHours = data.reduce(
    (sum, item) => sum + item.work_log_time_spent_hour,
    0
  );
  const totalTickets = new Set(data.map((item) => item.link_ticket)).size;
  const totalEngineers = new Set(data.filter((item) => !item.name.includes("Bian")).map((item) => item.name)).size;
  const avgHoursPerTicket =
    totalTickets > 0 ? (totalHours / totalTickets).toFixed(1) : "0";

  const metrics = [
    {
      title: "Total Hours Logged",
      value: `${totalHours.toFixed(1)}h`,
      description: "Logged time",
      icon: Clock,
      iconColor: "text-blue-600 dark:text-blue-400",
    },
    {
      title: "Team Members",
      value: totalEngineers.toString(),
      description: "Engineers",
      icon: Users,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
    {
      title: "Avg Hours/Ticket",
      value: `${avgHoursPerTicket}h`,
      description: "Efficiency metric",
      icon: TrendingUp,
      iconColor: "text-emerald-600 dark:text-emerald-400",
    },
  ];

  return <MetricCards metrics={metrics} columns={4} />;
}
