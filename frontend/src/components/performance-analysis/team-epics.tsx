"use client";
import { useSuspenseQuery } from "@tanstack/react-query";
import { useSelector } from "react-redux";
import { format } from "date-fns";
import axios from "axios";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {User, ExternalLink, Target, Clock} from "lucide-react";
import type { EpicUser } from "@/types/epicuser";
import type { RootState } from "@/app/store";
import { DataTable } from "../ui/data-table";
import { engineerIssueColumnsWithName } from "./engineer-issue-columns";
import {
  getBacklogEpics,
  getDoneBacklogEpics,
  getMondayWednesdayEpics,
  getThursdayFridayEpics,
  getThursdayFridayStoryPoints,
  getMondayWednesdayStoryPoints,
  getDoneWeeklyEpics,
  getTotalStoryPointsFromBacklog,
  getCalculateDoneStoryPoints,
  getTotalBreakdownHours,
  getEpicStatusColor,
} from "@/lib/utils";
import { API_BASE_URL } from "@/constants";
import { IssueWorklogs } from "./issue-work-logs";
import { MetricCards } from "@/components/metric-cards";
import {TeamMembersEpic} from "@/app/pe/performance-analysis/components/team-members-epic";

interface TeamEpicsProps {
  uniqueUserIds: string[];
}

export function TeamEpics({ uniqueUserIds }: TeamEpicsProps) {
  const { startDate, endDate } = useSelector(
    (state: RootState) => state.dateFilter
  );
  const filterDate = {
    start_date: format(new Date(startDate ?? "").setHours(0, 0, 0 ,0), "yyyy-MM-dd"),
    end_date: format(new Date(endDate ?? "").setHours(0, 0, 0 ,0), "yyyy-MM-dd"),
  }

  const { data: epicData } = useSuspenseQuery<EpicUser>({
    queryKey: ["team-epics", startDate, endDate, uniqueUserIds],
    queryFn: async () => {
      if (uniqueUserIds.length === 0) {
        return { epics: [] };
      }
      const res = await axios.get(API_BASE_URL + `/api/jira/epic`, {
        headers: {
          Accept: "application/json",
        },
        params: {
          start_date: startDate
            ? format(new Date(startDate), "yyyy-MM-dd")
            : undefined,
          end_date: endDate
            ? format(new Date(endDate), "yyyy-MM-dd")
            : undefined,
          user_ids: uniqueUserIds,
        },
      });
      return {
        epics: res.data.epics,
      }
    },
  });
  const mappingBacklogEpicData = epicData.epics.map((epic) => {
    return {
      ...epic,
      backlog_sp: epic.issues.filter((issue) => issue.status !== "Done")
          .reduce((sp, issue) => {
            return (sp + issue.sp);
          }, 0)
    }
  })
  const sortedEpics = mappingBacklogEpicData.sort((a, b) => {
    return b.backlog_sp - a.backlog_sp;
  });

  const formatTimeSpent = (timeString: string) => {
    return timeString || "0h";
  };

  const metricsSummary = [
    {
      title: "Story Points",
      value: getCalculateDoneStoryPoints(getThursdayFridayEpics(epicData.epics, filterDate), filterDate) + getCalculateDoneStoryPoints(getMondayWednesdayEpics(epicData.epics, filterDate), filterDate) + getCalculateDoneStoryPoints(getBacklogEpics(epicData.epics, filterDate), filterDate),
      description: "Total SP",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
    {
      title: "Total Hours in Epics",
      value: `${getTotalBreakdownHours(epicData.epics, filterDate).toFixed(2)}h`,
      description: "Logged time",
      icon: Clock,
      iconColor: "text-blue-600 dark:text-blue-400",
    },
    {
      title: "SP / Hour",
      value: (getTotalBreakdownHours(epicData.epics, filterDate) > 0 ? (getCalculateDoneStoryPoints(epicData.epics, filterDate) / getTotalBreakdownHours(epicData.epics, filterDate)) : 0).toFixed(2),
      description: "Average of SP Produced per Hour",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    }
  ];
  const metricsBacklog = [
    {
      title: "Backlog Epics",
      value: getBacklogEpics(epicData.epics, filterDate).length,
      description: "Total Backlog Epics from Last Week",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
    {
      title: "SP from Backlog Epics",
      value: `${getTotalStoryPointsFromBacklog(epicData.epics, filterDate)} SP`,
      description: "Total SP from Backlog Epics from Last Week",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
  ];
  const metricsDoneFromBacklog = [
    {
      title: "Done Backlog Epics",
      value: getDoneBacklogEpics(epicData.epics, filterDate).length,
      description: "Total Backlog Epics Done from Last Week",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
    {
      title: "Done SP from Backlog Epics",
      value: `${getCalculateDoneStoryPoints(getBacklogEpics(epicData.epics, filterDate), filterDate)} SP`,
      description: "Total SP Done from Backlog Epics from Last Week",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
  ];
  const metricsAdding = [
    {
      title: "Epics Added at Monday-Wednesday",
      value: getMondayWednesdayEpics(epicData.epics, filterDate).length,
      description: "Total Epics Added at Monday-Wednesday",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
    {
      title: "SP from Epics Added at Monday-Wednesday",
      value: `${getMondayWednesdayStoryPoints(epicData.epics, filterDate)} SP`,
      description: "Total SP from Epics Added at Monday-Wednesday",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
    {
      title: "Epics Added at Thursday-Friday",
      value: getThursdayFridayEpics(epicData.epics, filterDate).length,
      description: "Total Epics Added at Thursday-Friday",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
    {
      title: "SP from Epics Added at Thursday-Friday",
      value: `${getThursdayFridayStoryPoints(epicData.epics, filterDate)} SP`,
      description: "Total SP from Epics Added at Thursday-Friday",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
  ];
  const metricsDoneFromAdding = [
    {
      title: "Done Epics Added at Monday-Wednesday",
      value: getDoneWeeklyEpics(getMondayWednesdayEpics(epicData.epics, filterDate)).length,
      description: "Total Epics Added at Monday-Wednesday",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
    {
      title: "Done SP from Epics Added at Monday-Wednesday",
      value: `${getCalculateDoneStoryPoints(getMondayWednesdayEpics(epicData.epics, filterDate), filterDate)} SP`,
      description: "Total SP from Epics Added at Monday-Wednesday",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
    {
      title: "Done Epics Added at Thursday-Friday",
      value:getDoneWeeklyEpics(getThursdayFridayEpics(epicData.epics, filterDate)).length,
      description: "Total Epics Added at Thursday-Friday",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
    {
      title: "Done SP from Epics Added at Thursday-Friday",
      value: `${getCalculateDoneStoryPoints(getThursdayFridayEpics(epicData.epics, filterDate), filterDate)} SP`,
      description: "Total SP from Epics Added at Thursday-Friday",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
  ];

  return (
    <div className="flex flex-col gap-3">
      {/* Key Metrics */}
      <MetricCards metrics={metricsSummary} columns={3} section={"Summary"} />
      <MetricCards metrics={metricsBacklog} columns={2} section={"Backlog"} />
      <MetricCards metrics={metricsDoneFromBacklog} columns={2} section={"Done Backlog"} />
      <MetricCards metrics={metricsAdding} columns={4} section={"Epics Added"} />
      <MetricCards metrics={metricsDoneFromAdding} columns={4} section={"Done Epics Added"} />
      <TeamMembersEpic dataEpics={epicData.epics} />
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="h-5 w-5 mr-2" />
            Team Epics
          </CardTitle>
        </CardHeader>
        <CardContent>
          {epicData.epics.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-4 mb-4">
                  <User className="h-8 w-8 text-gray-400 dark:text-gray-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  No Epics Found
                </h3>
                <p className="text-gray-500 dark:text-gray-400 max-w-sm">
                  No epic data is available for the selected date range. Try
                  adjusting your date filter or check if epics exist.
                </p>
              </div>
          ) : (
              <div className="space-y-4 sm:space-y-6">
                {sortedEpics.map((epic) => (
                    <Card key={epic.epic_link} className="border-2">
                      <CardHeader>
                        <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="flex flex-wrap items-center gap-2 mb-2 relative">
                              <Badge
                                  className={
                                      getEpicStatusColor(epic.issues.every((issue) => issue.status === "Done") ? "Done" : "In Progress") +
                                      " text-xs px-2 py-1"
                                  }
                                  variant="secondary"
                              >
                                {getBacklogEpics(epicData.epics, filterDate).findIndex((backlogEpic) => backlogEpic.epic_link === epic.epic_link) > -1 ? "BACKLOG" : ""}
                              </Badge>
                              <CardTitle
                                  className={`text-base sm:text-lg px-2 py-1 w-fit border-l-6 font-semibold ${epic.epic_name.toLowerCase().startsWith("worklog")
                                      ? "border-blue-600 dark:border-blue-400"
                                      : "border-violet-600 dark:border-violet-400"
                                  }`}
                              >
                                {epic.epic_name}
                              </CardTitle>
                              <Badge
                                  className={
                                      getEpicStatusColor(epic.issues.every((issue) => issue.status === "Done") ? "Done" : "In Progress") +
                                      " text-xs px-2 py-1"
                                  }
                                  variant="secondary"
                              >
                                {epic.issues.every((issue) => issue.status === "Done") ? "DONE" : "IN PROGRESS"}
                              </Badge>
                            </div>
                            <div className="flex flex-col gap-2 text-xs sm:text-sm text-muted-foreground">
                              {/* First row: Reporter */}
                              <div className="flex items-center gap-2 w-full">
                                <div className="flex items-center gap-1 min-w-0 flex-1">
                            <span className="font-semibold text-foreground text-xs whitespace-nowrap">
                              Reporter:
                            </span>
                                  <Badge
                                      variant="outline"
                                      className="px-2 py-0.5 text-xs truncate"
                                  >
                                    {epic.reporter}
                                  </Badge>
                                </div>
                              </div>
                              {/* Second row: Grooming | Done Breakdown */}
                              <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4 sm:flex-wrap">
                                {epic.grooming_date && (
                                    <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                Grooming:
                              </span>
                                      <Badge
                                          variant="outline"
                                          className="px-2 py-0.5 text-xs"
                                      >
                                        {new Intl.DateTimeFormat('en-GB', {
                                          day: '2-digit',
                                          month: 'short',
                                          year: 'numeric'
                                        }).format(new Date(
                                            epic.grooming_date
                                        ))}
                                      </Badge>
                                    </div>
                                )}
                                {epic.done_break_down_date && (
                                    <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                Done Breakdown:
                              </span>
                                      <Badge
                                          variant="outline"
                                          className="px-2 py-0.5 text-xs"
                                      >
                                        {new Intl.DateTimeFormat('en-GB', {
                                          day: '2-digit',
                                          month: 'short',
                                          year: 'numeric'
                                        }).format(new Date(
                                            epic.done_break_down_date
                                        ))}
                                      </Badge>
                                    </div>
                                )}
                              </div>
                              {/* Third row: Breakdown Duration */}
                              <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between w-full">
                                <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4 flex-1 min-w-0">
                                  <div className="flex items-center gap-1">
                                <span className="font-semibold text-foreground text-xs">
                                  Breakdown:
                                </span>
                                    <Badge
                                        variant="outline"
                                        className="px-2 py-0.5 text-xs"
                                    >
                                      {formatTimeSpent(
                                          epic.break_down_duration_hour.toFixed(2)
                                      )} hour
                                    </Badge>
                                  </div>
                                </div>
                              </div>
                              {/* Fourth row: Detailing Hour & Operation Hour */}
                              <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between w-full">
                                <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4 flex-1 min-w-0">
                                  <div className="flex items-center gap-1">
                                <span className="font-semibold text-foreground text-xs">
                                  Detailing:
                                </span>
                                    <Badge
                                        variant="outline"
                                        className="px-2 py-0.5 text-xs"
                                    >
                                      {formatTimeSpent(
                                          epic.detailing_duration_hour.toFixed(2)
                                      )} hour
                                    </Badge>
                                  </div>
                                  <div className="flex items-center gap-1">
                                <span className="font-semibold text-foreground text-xs">
                                  Operation:
                                </span>
                                    <Badge
                                        variant="outline"
                                        className="px-2 py-0.5 text-xs"
                                    >
                                      {formatTimeSpent(epic.operation_hour.toFixed(2))} hour
                                    </Badge>
                                  </div>
                                </div>
                              </div>
                              {/* Fifth row: Total SP | Issues | Done SP | Backlog SP */}
                              <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between w-full">
                                <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4 flex-1 min-w-0">
                                  <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                Total SP:
                              </span>
                                    <Badge
                                        variant="outline"
                                        className="px-2 py-0.5 text-xs"
                                    >
                                      {epic.sp}
                                    </Badge>
                                  </div>
                                  <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                Issues:
                              </span>
                                    <Badge
                                        variant="outline"
                                        className="px-2 py-0.5 text-xs"
                                    >
                                      {epic.issues.length}
                                    </Badge>
                                  </div>
                                  <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                Done SP:
                              </span>
                                    <Badge
                                        variant="outline"
                                        className="px-2 py-0.5 text-xs"
                                    >
                                      {epic.issues
                                          .filter((issue) => issue.status === "Done")
                                          .reduce((sp, issue) => {
                                            return (sp += issue.sp);
                                          }, 0)}

                                    </Badge>
                                  </div>
                                  <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                Backlog SP:
                              </span>
                                    <Badge
                                        variant="outline"
                                        className="px-2 py-0.5 text-xs"
                                    >
                                      {epic.backlog_sp}
                                    </Badge>
                                  </div>
                                  <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                  SP/Hour:
                              </span>
                                    <Badge
                                        variant="outline"
                                        className="px-2 py-0.5 text-xs"
                                    >
                                      {epic.detailing_duration_hour + epic.break_down_duration_hour === 0 ? 0 : (epic.issues
                                          .filter((issue) => issue.status === "Done")
                                          .reduce((sp, issue) => {
                                            return sp += issue.sp
                                          }, 0) / (epic.detailing_duration_hour + epic.break_down_duration_hour)).toFixed(2)
                                      }
                                    </Badge>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <Button
                              variant="ghost"
                              size="sm"
                              className="w-fit"
                              onClick={() => window.open(epic.epic_link, "_blank")}
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <DataTable
                            columns={engineerIssueColumnsWithName}
                            data={epic.issues}
                            initialSort={[{ id: "status", desc: false }]}
                            renderExpandedRow={(row) => {
                              return <IssueWorklogs worklogs={row.original.worklogs} />;
                            }}
                        />
                      </CardContent>
                    </Card>
                ))}
              </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
