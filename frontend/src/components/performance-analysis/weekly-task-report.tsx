"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { useSelector } from "react-redux";
import { RootState } from "@/app/store";
import { useQuery } from "@tanstack/react-query";
import { API_BASE_URL } from "@/constants";
import axios from "axios";
import { Skeleton } from "@/components/ui/skeleton";
import ReactMarkdown from "react-markdown";
import { Badge } from "@/components/ui/badge";
import { getStatusColor } from "@/app/sdet/performance-analysis/components/task-columns";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";

interface Task {
    assignee: string;
    assignee_id: string;
    created: string;
    labels: string[];
    parent_link: string;
    parent_name: string;
    priority: string;
    reporter: string;
    sp: number;
    status: string;
    release_at: string;
    release_name: string;
    task_link: string;
    task_name: string;
    time_spent_hours: number;
    time_spent_human: string;
    time_spent_second: number;
    type: string;
    category: string;
    updated: string;
    // Issue description fields (present when with_issue_description=true)
    issue_root_cause?: string;
    issue_business_impact?: string;
    issue_solution_short_term?: string;
    issue_solution_long_term?: string;
    worklogs: Array<{
        created_at: string;
        description: string;
        name: string;
        time_spent_hour: number;
        time_spent_human: string;
        time_spent_second: number;
        type: string;
        user_id: string;
    }>;
}

interface TaskResponse {
    tasks: Task[];
}

interface WeekRange {
    start_date: string;
    end_date: string;
    startISO: string;
    endISO: string;
}

const CATEGORIES = [
    "CATEGORY-ISSUE",
    "CATEGORY-PROJECT-SA",
    "CATEGORY-FIXER",
    "CATEGORY-TECHDEBT",
    "CATEGORY-ADHOC"
];

function getLast4WeeksRange(startDate: string, endDate: string): WeekRange[] {
    const result: WeekRange[] = [];
    endDate = endDate.split('T')[0];
    // Parse endDate to avoid timezone issues
    const [endYear, endMonth, endDay] = endDate.split('-').map(Number);
    const referenceDate = new Date(endYear, endMonth - 1, endDay);

    // Helper function to get Monday of a given week
    function getMondayOfWeek(date: Date): Date {
        const d = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        const day = d.getDay();
        const diff = d.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
        d.setDate(diff);
        return d;
    }

    // Helper function to get Sunday of a given week
    function getSundayOfWeek(date: Date): Date {
        const monday = getMondayOfWeek(date);
        const sunday = new Date(monday.getFullYear(), monday.getMonth(), monday.getDate() + 6);
        return sunday;
    }

    // Calculate 4 weeks (Monday to Sunday) based on the end date
    // Always use static Monday-to-Sunday weeks
    for (let i = 3; i >= 0; i--) {
        // Calculate the reference date for each week going back
        const weekReferenceDate = new Date(referenceDate.getFullYear(), referenceDate.getMonth(), referenceDate.getDate() - i * 7);
        // Always get Monday-Sunday for each week
        const weekStart = getMondayOfWeek(weekReferenceDate);
        const weekEnd = getSundayOfWeek(weekReferenceDate);

        result.push({
            start_date: weekStart.toLocaleDateString('en-GB', {
                day: '2-digit',
                month: 'short',
                year: 'numeric'
            }),
            end_date: weekEnd.toLocaleDateString('en-GB', {
                day: '2-digit',
                month: 'short',
                year: 'numeric'
            }),
            startISO: weekStart.toISOString().split('T')[0],
            endISO: weekEnd.toISOString().split('T')[0]
        });
    }

    return result;
}

interface CategoryMetrics {
    taskCount: number;
    totalHours: number;
    totalSP: number;
}

interface WeeklyData {
    [category: string]: CategoryMetrics;
    total: CategoryMetrics;
}

export function WeeklyTaskReport() {
    const { startDate, endDate } = useSelector(
        (state: RootState) => state.dateFilter
    );

    // Generate date ranges for current week + 3 previous weeks
    const weekRanges = getLast4WeeksRange(startDate || new Date().toISOString(), endDate || new Date().toISOString());

    // Create queries for each week using individual useQuery calls
    const week0Query = useQuery({
        queryKey: ["weekly-tasks", weekRanges[0]?.startISO, weekRanges[0]?.endISO],
        queryFn: async () => {
            const range = weekRanges[0];
            const res = await axios.get(API_BASE_URL + `/api/jira/task`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: `project = Principal and status WAS IN ("In Progress", "In Testing Dev (Optional)", "In testing stg (OPTIONAL)") DURING ("${range.startISO}", "${range.endISO}") AND type != Epic ORDER BY assignee`,
                },
            });
            return res.data as TaskResponse;
        },
        enabled: !!startDate && !!endDate && weekRanges.length > 0,
    });

    const week1Query = useQuery({
        queryKey: ["weekly-tasks", weekRanges[1]?.startISO, weekRanges[1]?.endISO],
        queryFn: async () => {
            const range = weekRanges[1];
            const res = await axios.get(API_BASE_URL + `/api/jira/task`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: `project = Principal and status WAS IN ("In Progress", "In Testing Dev (Optional)", "In testing stg (OPTIONAL)") DURING ("${range.startISO}", "${range.endISO}") AND type != Epic ORDER BY assignee`,
                },
            });
            return res.data as TaskResponse;
        },
        enabled: !!startDate && !!endDate && weekRanges.length > 1,
    });

    const week2Query = useQuery({
        queryKey: ["weekly-tasks", weekRanges[2]?.startISO, weekRanges[2]?.endISO],
        queryFn: async () => {
            const range = weekRanges[2];
            const res = await axios.get(API_BASE_URL + `/api/jira/task`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: `project = Principal and status WAS IN ("In Progress", "In Testing Dev (Optional)", "In testing stg (OPTIONAL)") DURING ("${range.startISO}", "${range.endISO}") AND type != Epic ORDER BY assignee`,
                },
            });
            return res.data as TaskResponse;
        },
        enabled: !!startDate && !!endDate && weekRanges.length > 2,
    });

    const week3Query = useQuery({
        queryKey: ["weekly-tasks", weekRanges[3]?.startISO, weekRanges[3]?.endISO],
        queryFn: async () => {
            const range = weekRanges[3];
            const res = await axios.get(API_BASE_URL + `/api/jira/task`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: `project = Principal and status WAS IN ("In Progress", "In Testing Dev (Optional)", "In testing stg (OPTIONAL)") DURING ("${range.startISO}", "${range.endISO}") AND type != Epic ORDER BY assignee`,
                },
            });
            return res.data as TaskResponse;
        },
        enabled: !!startDate && !!endDate && weekRanges.length > 3,
    });

    const weekQueries = [week0Query, week1Query, week2Query, week3Query];
    const isLoading = weekQueries.some(query => query.isLoading);
    const hasError = weekQueries.some(query => query.isError);

    // Query for Production Issues within the selected date range
    const issuesQuery = useQuery({
        queryKey: [
            "weekly-issues",
            startDate ? startDate.split("T")[0] : undefined,
            endDate ? endDate.split("T")[0] : undefined,
        ],
        queryFn: async () => {
            const start = (startDate || new Date().toISOString()).split("T")[0];
            const end = (endDate || new Date().toISOString()).split("T")[0];
            const res = await axios.get(API_BASE_URL + `/api/jira/task`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    // Use exact JQL as requested, including time window
                    jql: `"Category[Dropdown]" = "CATEGORY-ISSUE" AND type = Epic AND status WAS IN ("In Progress", "In Testing Dev (Optional)", "In testing stg (OPTIONAL)") DURING ("${start} 00:00", "${end} 23:59") ORDER BY assignee`,
                    with_issue_description: true,
                },
            });
            return res.data as TaskResponse;
        },
        enabled: !!startDate && !!endDate,
    });

    // Process data for each week
    const weeklyData: WeeklyData[] = weekQueries.map(query => {
        if (!query.data?.tasks) {
            return CATEGORIES.reduce((acc, category) => {
                acc[category] = { taskCount: 0, totalHours: 0, totalSP: 0 };
                return acc;
            }, { total: { taskCount: 0, totalHours: 0, totalSP: 0 } } as WeeklyData);
        }

        const tasks = query.data.tasks;
        const categoryData: WeeklyData = CATEGORIES.reduce((acc, category) => {
            acc[category] = { taskCount: 0, totalHours: 0, totalSP: 0 };
            return acc;
        }, {} as WeeklyData);

        // Initialize total
        categoryData.total = { taskCount: 0, totalHours: 0, totalSP: 0 };

        // Group tasks by category
        tasks.forEach(task => {
            const category = task.category;

            if (CATEGORIES.includes(category)) {
                categoryData[category].taskCount += 1;
                categoryData[category].totalHours += task.time_spent_hours || 0;
                categoryData[category].totalSP += task.sp || 0;

                // Add to total (only categorized tasks)
                categoryData.total.taskCount += 1;
                categoryData.total.totalHours += task.time_spent_hours || 0;
                categoryData.total.totalSP += task.sp || 0;
            }
        });

        return categoryData;
    });

    return (
        <>
            <Card>
                <CardHeader>
                    <CardTitle>Weekly Task Report</CardTitle>
                </CardHeader>
                <CardContent>
                    {hasError ? (
                        <div className="text-center py-8 text-red-600">
                            Error loading weekly task data. Please try again.
                        </div>
                    ) : (
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-muted/40">
                                        <TableHead className="font-semibold bg-muted/40">Operations</TableHead>
                                        {weekRanges.map((range, index) => {
                                            const startDate = new Date(range.start_date);
                                            const endDate = new Date(range.end_date);

                                            const startDayName = startDate.toLocaleDateString('en-GB', { weekday: 'long' });
                                            const endDayName = endDate.toLocaleDateString('en-GB', { weekday: 'long' });

                                            return (
                                                <TableHead key={index} className="text-center min-w-[120px] bg-muted/40">
                                                    <div className="space-y-1">
                                                        <div className="font-medium">{range.start_date} ({startDayName})</div>
                                                        <div className="text-xs text-muted-foreground">to</div>
                                                        <div className="font-medium">{range.end_date} ({endDayName})</div>
                                                    </div>
                                                </TableHead>
                                            );
                                        })}
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {CATEGORIES.map((category) => (
                                        <TableRow key={category}>
                                            <TableCell className="font-medium">
                                                {category}
                                            </TableCell>
                                            {weeklyData.map((weekData, weekIndex) => (
                                                <TableCell key={weekIndex} className="text-center">
                                                    {isLoading ? (
                                                        <Skeleton className="h-4 w-20 mx-auto" />
                                                    ) : (
                                                        <div className="space-y-1">
                                                            <div className="font-medium">
                                                                {weekData[category].taskCount} Task{weekData[category].taskCount !== 1 ? 's' : ''} ({weekData[category].totalSP} SP)
                                                            </div>
                                                            <div className="text-sm text-muted-foreground">
                                                                ({weekData[category].totalHours.toFixed(1)} Hours)
                                                            </div>
                                                        </div>
                                                    )}
                                                </TableCell>
                                            ))}
                                        </TableRow>
                                    ))}
                                    {/* Total Row */}
                                    <TableRow className="border-t-2 bg-muted/40 shadow-sm">
                                        <TableCell className="font-bold">Total</TableCell>
                                        {weeklyData.map((weekData, weekIndex) => (
                                            <TableCell key={weekIndex} className="text-center">
                                                {isLoading ? (
                                                    <Skeleton className="h-4 w-20 mx-auto" />
                                                ) : (
                                                    <div className="space-y-1">
                                                        <div className="font-bold">
                                                            {weekData.total.taskCount} Task{weekData.total.taskCount !== 1 ? 's' : ''} ({weekData.total.totalSP} SP)
                                                        </div>
                                                        <div className="text-sm font-medium">
                                                            ({weekData.total.totalHours.toFixed(1)} Hours)
                                                        </div>
                                                    </div>
                                                )}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                </TableBody>
                            </Table>
                        </div>
                    )}
                </CardContent>
            </Card>

            <Card className="mt-6">
                <CardHeader>
                    <CardTitle>Issue Production</CardTitle>
                </CardHeader>
                <CardContent>
                    {issuesQuery.isLoading ? (
                        <div className="space-y-2">
                            <Skeleton className="h-5 w-40" />
                            <Skeleton className="h-16 w-full" />
                            <Skeleton className="h-16 w-full" />
                        </div>
                    ) : issuesQuery.isError ? (
                        <div className="text-red-600 text-sm">Failed to load production issues.</div>
                    ) : (
                        <div className="space-y-4">
                            {(issuesQuery.data?.tasks || [])
                                .filter((t) => t.category === "CATEGORY-ISSUE")
                                .map((t, idx) => (
                                    <div
                                        key={`${t.task_link}-${idx}`}
                                        className="rounded-lg border bg-card text-card-foreground shadow-sm"
                                    >
                                        <div className="p-4 sm:p-5">
                                            <div className="flex items-start justify-between gap-3">
                                                <div className="text-base font-semibold leading-6 break-words">
                                                    {t.task_name}
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    {t.status && (
                                                        <Badge
                                                            variant="secondary"
                                                            className={`${getStatusColor(t.status)}`}
                                                        >
                                                            {t.status}
                                                        </Badge>
                                                    )}
                                                    <a
                                                        href={t.task_link}
                                                        target="_blank"
                                                        rel="noreferrer"
                                                        className="text-xs font-medium text-destructive hover:underline"
                                                    >
                                                        Open in Jira
                                                    </a>
                                                </div>
                                            </div>

                                            <div className="mt-4 grid gap-6 md:grid-cols-2">
                                                <section>
                                                    <div className="text-sm font-semibold mb-1">Root Cause</div>
                                                    <div className="prose prose-sm dark:prose-invert max-w-none break-words whitespace-pre-wrap prose-p:my-1 prose-ul:my-1 prose-ol:my-1">
                                                        <ReactMarkdown>
                                                            {t.issue_root_cause || "-"}
                                                        </ReactMarkdown>
                                                    </div>
                                                </section>
                                                <section>
                                                    <div className="text-sm font-semibold mb-1">Business Impact</div>
                                                    <div className="prose prose-sm dark:prose-invert max-w-none break-words whitespace-pre-wrap prose-p:my-1 prose-ul:my-1 prose-ol:my-1">
                                                        <ReactMarkdown>
                                                            {t.issue_business_impact || "-"}
                                                        </ReactMarkdown>
                                                    </div>
                                                </section>
                                            </div>

                                            <div className="my-4 h-px bg-border" />

                                            <div className="grid gap-6 md:grid-cols-2">
                                                <section>
                                                    <div className="text-sm font-semibold mb-1">Solution (Short Term)</div>
                                                    <div className="prose prose-sm dark:prose-invert max-w-none break-words whitespace-pre-wrap prose-p:my-1 prose-ul:my-1 prose-ol:my-1">
                                                        <ReactMarkdown>
                                                            {t.issue_solution_short_term || "-"}
                                                        </ReactMarkdown>
                                                    </div>
                                                </section>
                                                <section>
                                                    <div className="text-sm font-semibold mb-1">Solution (Long Term)</div>
                                                    <div className="prose prose-sm dark:prose-invert max-w-none break-words whitespace-pre-wrap prose-p:my-1 prose-ul:my-1 prose-ol:my-1">
                                                        <ReactMarkdown>
                                                            {t.issue_solution_long_term || "-"}
                                                        </ReactMarkdown>
                                                    </div>
                                                </section>
                                            </div>

                                            <div className="my-4 h-px bg-border" />

                                            <section>
                                                <div className="text-sm font-semibold mb-1">Release At</div>
                                                <p className="text-sm text-muted-foreground">
                                                    {t.release_name ? (
                                                        <>
                                                            {t.release_name}
                                                            <Badge
                                                                variant="secondary"
                                                                className="ml-1 bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                                                            >
                                                                {t.release_at}
                                                            </Badge>
                                                        </>
                                                    ) : (
                                                        '-'
                                                    )}
                                                </p>
                                            </section>
                                        </div>
                                    </div>
                                ))}

                            {issuesQuery.data?.tasks?.filter((t) => t.category === "CATEGORY-ISSUE").length === 0 && (
                                <div className="text-sm text-muted-foreground">No issues found for the selected period.</div>
                            )}
                        </div>
                    )}
                </CardContent>
            </Card>
        </>
    );
}
