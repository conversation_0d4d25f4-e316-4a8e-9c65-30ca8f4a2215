"use client";

import axios from "axios";
import { RootState } from "@/app/store";
import { useQuery, useQueries } from "@tanstack/react-query";
import { usePathname } from "next/navigation";
import { useSelector } from "react-redux";
import { <PERSON><PERSON>, <PERSON>, CheckCircle, TrendingUp, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ert<PERSON>gle, FolderOpen } from "lucide-react";
import {
    Card,
    CardHeader,
    CardTitle,
    CardContent,
} from "../ui/card";
import { Badge } from "../ui/badge";
import { Avatar, AvatarFallback } from "../ui/avatar";
import { Skeleton } from "../ui/skeleton";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "../ui/tabs";
import { API_BASE_URL } from "@/constants";
import { TaskTable } from "@/components/task-table";
import { MetricCards } from "@/components/metric-cards";
import { WeeklyTaskReport } from "./weekly-task-report";
import { differenceInCalendarDays, addDays, parseISO, format } from "date-fns";
import { Suspense, lazy } from "react";
const Operation = lazy(() => import("./operation"));
import { LPLoading } from "@/components/lp-loading";
import { TimeDistributionChart } from "@/components/performance-analysis/time-distribution-chart";
import { ActivityChart } from "@/components/performance-analysis/activity-chart";

interface TaskOverviewProps {
    jql?: string;
}

interface Task {
    assignee: string;
    assignee_id: string;
    created: string;
    start_date?: string; // optional for epic operation view
    due_date?: string;
    labels: string[];
    parent_link: string;
    parent_name: string;
    priority: string;
    reporter: string;
    sp: number;
    status: string;
    task_link: string;
    task_name: string;
    time_spent_hours: number;
    time_spent_human: string;
    time_spent_second: number;
    type: string;
    category: string;
    updated: string;
    worklogs: Array<{
        created_at: string;
        description: string;
        name: string;
        time_spent_hour: number;
        time_spent_human: string;
        time_spent_second: number;
        type: string;
        user_id: string;
    }>;
}

interface TaskResponse {
    tasks: Task[];
}

interface CountResponse {
    count: number;
}

const METRIC_CONFIGS = [
    {
        jql: `project = "PRIN" 
AND type = Task
AND "Category[Dropdown]" = CATEGORY-FIXER
AND status = "To Do"
AND "Team[Labels]" = TEAM-IT-ENG
`,
        name: "API Fixer",
        icon: Wrench,
        iconColor: "text-blue-600",
        description: "Engineer To Do Tasks"
    },
    {
        jql: `project = "PRIN" 
AND type = Task
AND "Category[Dropdown]" = CATEGORY-ADHOC
AND status = "To Do"
AND "Team[Labels]" = TEAM-IT-ENG
`,
        name: "Adhoc",
        icon: AlertTriangle,
        iconColor: "text-yellow-600",
        description: "Engineer To Do Tasks"
    },
    {
        jql: `project = "PRIN" 
AND type = Task
AND "Category[Dropdown]" = CATEGORY-TECHDEBT
AND status = "To Do"
AND "Team[Labels]" = TEAM-IT-ENG
`,
        name: "Tech Debt",
        icon: AlertTriangle,
        iconColor: "text-purple-600",
        description: "Engineer To Do Tasks"
    },
    {
        jql: `project = "PRIN" 
AND type = Task
AND "Category[Dropdown]" = CATEGORY-ISSUE
AND status = "To Do"
AND "Team[Labels]" = TEAM-IT-ENG
`,
        name: "Issue",
        icon: Bug,
        iconColor: "text-red-600",
        description: "Engineer To Do Tasks"
    },
    {
        jql: `project = "PRIN" 
AND type = Task
AND "Category[Dropdown]" = CATEGORY-PROJECT-SA
AND status = "To Do"
AND "Team[Labels]" = TEAM-IT-ENG
`,
        name: "Project",
        icon: FolderOpen,
        iconColor: "text-green-600",
        description: "Engineer To Do Tasks"
    },
    {
        jql: `project = "PRIN" 
AND type = Task
AND "Category[Dropdown]" = CATEGORY-FIXER
AND status = "To Do"
AND "Team[Labels]" = TEAM-IT-QA
`,
        name: "API Fixer Implementation",
        icon: Wrench,
        iconColor: "text-blue-600",
        description: "QA To Do Tasks"
    },
    {
        jql: `project = "PRIN" 
AND type = Task
AND "Category[Dropdown]" = CATEGORY-ADHOC
AND status = "To Do"
AND "Team[Labels]" = TEAM-IT-QA
`,
        name: "Adhoc",
        icon: AlertTriangle,
        iconColor: "text-yellow-600",
        description: "QA To Do Tasks"
    },
    {
        jql: `project = "PRIN" 
AND type = Task
AND "Category[Dropdown]" = CATEGORY-TECHDEBT
AND status = "To Do"
AND "Team[Labels]" = TEAM-IT-QA
`,
        name: "Tech Debt",
        icon: AlertTriangle,
        iconColor: "text-purple-600",
        description: "QA To Do Tasks"
    },
    {
        jql: `project = "PRIN" 
AND type = Task
AND "Category[Dropdown]" = CATEGORY-ISSUE
AND status = "To Do"
AND "Team[Labels]" = TEAM-IT-QA
`,
        name: "Issue",
        icon: Bug,
        iconColor: "text-red-600",
        description: "QA To Do Tasks"
    },
    {
        jql: `project = "PRIN" 
AND type = Task
AND "Category[Dropdown]" = CATEGORY-PROJECT-SA
AND status = "To Do"
AND "Team[Labels]" = TEAM-IT-QA
`,
        name: "Project",
        icon: FolderOpen,
        iconColor: "text-green-600",
        description: "QA To Do Tasks"
    }
];

export function TaskOverview({ jql }: TaskOverviewProps) {
    const pathname = usePathname();
    const role = pathname.split("/")[1] || "pe";

    const { startDate, endDate } = useSelector(
        (state: RootState) => state.dateFilter
    );

    const { data, isError, isLoading } = useQuery({
        queryKey: ["tasks", startDate, endDate, role],
        queryFn: async () => {
            const res = await axios.get(API_BASE_URL + `/api/jira/task`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: jql || `project = Principal and status WAS IN ("In Progress", "In Testing Dev (Optional)", "In testing stg (OPTIONAL)") DURING (startOfWeek(), endOfWeek()) ORDER BY assignee`,
                },
            });
            return res.data as TaskResponse;
        },
        staleTime: 60 * 1000,
    });

    // Operation logic moved to separate `Operation` component (./operation)

    // Metric cards queries - called individually to follow React hooks rules
    const apiFixerQuery = useQuery({
        queryKey: ["count", METRIC_CONFIGS[0].jql],
        queryFn: async () => {
            const res = await axios.get(API_BASE_URL + `/api/jira/count`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: METRIC_CONFIGS[0].jql,
                },
            });
            return res.data as CountResponse;
        },
    });

    const adhocQuery = useQuery({
        queryKey: ["count", METRIC_CONFIGS[1].jql],
        queryFn: async () => {
            const res = await axios.get(API_BASE_URL + `/api/jira/count`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: METRIC_CONFIGS[1].jql,
                },
            });
            return res.data as CountResponse;
        },
    });

    const techDebtQuery = useQuery({
        queryKey: ["count", METRIC_CONFIGS[2].jql],
        queryFn: async () => {
            const res = await axios.get(API_BASE_URL + `/api/jira/count`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: METRIC_CONFIGS[2].jql,
                },
            });
            return res.data as CountResponse;
        },
    });

    const issueQuery = useQuery({
        queryKey: ["count", METRIC_CONFIGS[3].jql],
        queryFn: async () => {
            const res = await axios.get(API_BASE_URL + `/api/jira/count`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: METRIC_CONFIGS[3].jql,
                },
            });
            return res.data as CountResponse;
        },
    });

    const projectQuery = useQuery({
        queryKey: ["count", METRIC_CONFIGS[4].jql],
        queryFn: async () => {
            const res = await axios.get(API_BASE_URL + `/api/jira/count`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: METRIC_CONFIGS[4].jql,
                },
            });
            return res.data as CountResponse;
        },
    });

    const apiFixerQueryQA = useQuery({
        queryKey: ["count", METRIC_CONFIGS[5].jql],
        queryFn: async () => {
            const res = await axios.get(API_BASE_URL + `/api/jira/count`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: METRIC_CONFIGS[5].jql,
                },
            });
            return res.data as CountResponse;
        },
    });

    const adhocQueryQA = useQuery({
        queryKey: ["count", METRIC_CONFIGS[6].jql],
        queryFn: async () => {
            const res = await axios.get(API_BASE_URL + `/api/jira/count`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: METRIC_CONFIGS[6].jql,
                },
            });
            return res.data as CountResponse;
        },
    });

    const techDebtQueryQA = useQuery({
        queryKey: ["count", METRIC_CONFIGS[7].jql],
        queryFn: async () => {
            const res = await axios.get(API_BASE_URL + `/api/jira/count`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: METRIC_CONFIGS[7].jql,
                },
            });
            return res.data as CountResponse;
        },
    });

    const issueQueryQA = useQuery({
        queryKey: ["count", METRIC_CONFIGS[8].jql],
        queryFn: async () => {
            const res = await axios.get(API_BASE_URL + `/api/jira/count`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: METRIC_CONFIGS[8].jql,
                },
            });
            return res.data as CountResponse;
        },
    });

    const projectQueryQA = useQuery({
        queryKey: ["count", METRIC_CONFIGS[9].jql],
        queryFn: async () => {
            const res = await axios.get(API_BASE_URL + `/api/jira/count`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: METRIC_CONFIGS[9].jql,
                },
            });
            return res.data as CountResponse;
        },
    });

    const metricQueries = [
        apiFixerQuery,
        adhocQuery,
        techDebtQuery,
        issueQuery,
        projectQuery,
        apiFixerQueryQA,
        adhocQueryQA,
        techDebtQueryQA,
        issueQueryQA,
        projectQueryQA,
    ];
    const isAnyMetricLoading = metricQueries.some(query => query.isLoading);

    // Split metrics into Engineer (0-3) and QA (4-7) parts
    const engineerMetrics = METRIC_CONFIGS.slice(0, 5).map((config, index) => ({
        title: config.name,
        value: metricQueries[index].data?.count ?? 0,
        icon: config.icon,
        iconColor: config.iconColor,
        description: config.description,
    }));

    const qaMetrics = METRIC_CONFIGS.slice(5, 10).map((config, index) => ({
        title: config.name,
        value: metricQueries[index + 5].data?.count ?? 0,
        icon: config.icon,
        iconColor: config.iconColor,
        description: config.description,
    }));


    // Skeleton metrics for loading state
    const skeletonEngineerMetrics = METRIC_CONFIGS.slice(0, 5).map((config) => ({
        title: config.name,
        value: <Skeleton className="h-8 w-16" />,
        icon: config.icon,
        iconColor: config.iconColor,
        description: config.description,
    }));

    const skeletonQaMetrics = METRIC_CONFIGS.slice(5, 10).map((config) => ({
        title: config.name,
        value: <Skeleton className="h-8 w-16" />,
        icon: config.icon,
        iconColor: config.iconColor,
        description: config.description,
    }));

    // Precompute metrics and per-assignee availability BEFORE any early returns to satisfy hooks rules
    const tasks = data?.tasks ?? [];

    // Flatten worklogs for Time Distribution component with safe fallbacks
    const worklogEntries = tasks.flatMap((task) =>
        (task.worklogs || [])
            .filter((wl) => {
                const t = (wl?.type ?? "").toString().trim().toLowerCase();
                if (!t) return false;
                const skipTypes = new Set([
                    "to do",
                    "done",
                    "in progress",
                    "hold",
                    "in testing dev (optional)",
                    "ready to test dev (optional)",
                    "in testing stg (optional)",
                ]);
                return !skipTypes.has(t);
            })
            .map((wl) => ({
                // Prefer worklog author name; fallback to task assignee
                name: wl?.name || task.assignee || "Unknown",
                work_log_comment: (wl?.type ?? "") as string,
                // Prefer hour field; fallback convert seconds to hours
                work_log_time_spent_hour:
                    (typeof wl?.time_spent_hour === "number" && !Number.isNaN(wl.time_spent_hour))
                        ? wl.time_spent_hour
                        : ((typeof wl?.time_spent_second === "number" ? wl.time_spent_second : 0) / 3600),
                // For SDET grouping compatibility
                type: wl?.type ?? task.type ?? "",
                type_task: (wl as any)?.type_task ?? "general",
            }))
    );

    // Calculate individual engineer metrics from tasks
    const engineerMetricsRecords = tasks.reduce((acc, task) => {
        if (task.assignee === "") {
            return acc; // Skip tasks without an assignee
        }
        if (!acc[task.assignee]) {
            acc[task.assignee] = {
                totalHours: 0,
                tasks: new Set(),
                storyPoints: 0,
                taskCount: 0,
                // dayOff stores total hours from worklogs whose description contains the exact lowercase "day off"
                dayOff: 0,
                assigneeId: task.assignee_id || "",
            };
        }

        acc[task.assignee].totalHours += task.time_spent_hours;
        acc[task.assignee].tasks.add(task.task_link);
        acc[task.assignee].storyPoints += task.sp;
        acc[task.assignee].taskCount = acc[task.assignee].tasks.size;

        // Accumulate "day off" hours from worklogs (match exact lowercase substring 'day off')
        if (Array.isArray(task.worklogs)) {
            for (const wl of task.worklogs) {
                if (wl && typeof wl.description === 'string' && wl.description.toLowerCase().includes('on leave')) {
                    // worklog has time_spent_hour per the Task interface; guard fallback to 0
                    acc[task.assignee].dayOff += (wl.time_spent_hour ?? 0);
                }
            }
        }

        return acc;
    }, {} as Record<string, any>);

    // Build unique assignee ids to fetch their next availability (due date + 1 day)
    const uniqueAssigneeIds: string[] = Array.from(
        new Set(
            Object.values(engineerMetricsRecords)
                .map((v: any) => v.assigneeId as string)
                .filter((id): id is string => Boolean(id))
        )
    );

    // Fetch "Available At" per assignee using useQueries
    const availableAtQueries = useQueries({
        queries: uniqueAssigneeIds.map((assigneeId) => ({
            queryKey: ["availableAt", assigneeId],
            queryFn: async () => {
                const res = await axios.get(API_BASE_URL + `/api/jira/task`, {
                    headers: { Accept: "application/json" },
                    params: {
                        jql: `assignee = ${assigneeId} AND duedate IS NOT EMPTY ORDER BY duedate DESC`,
                        limit: 1,
                    },
                });
                const tasks = (res.data as TaskResponse).tasks;
                const rawDue = tasks?.[0]?.due_date ?? null;
                if (!rawDue) return null;
                // Add 1 day and format as yyyy-MM-dd
                try {
                    const d = addDays(parseISO(rawDue), 1);
                    return format(d, 'dd MMMM yyyy');
                } catch {
                    return null;
                }
            },
            enabled: uniqueAssigneeIds.length > 0,
            staleTime: 5 * 60 * 1000,
        })),
    });

    // Map of assigneeId -> { value, isLoading }
    const availableAtMap = uniqueAssigneeIds.reduce<Record<string, { value: string | null; isLoading: boolean }>>((acc, id, idx) => {
        const q = availableAtQueries[idx];
        acc[id] = { value: (q?.data as string | null) ?? null, isLoading: q?.isLoading ?? false };
        return acc;
    }, {});

    if (isError)
        return (
            <div className="flex flex-col items-center justify-center h-full animate-fade-in bg-gradient-to-br from-gray-50 to-gray-100">
                <div className="flex items-center bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded mb-4 shadow-md animate-fade-in">
                    <svg
                        className="w-6 h-6 mr-3 text-red-500"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        viewBox="0 0 24 24"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M18.364 5.636l-12.728 12.728M5.636 5.636l12.728 12.728"
                        />
                    </svg>
                    <div>
                        <span className="font-bold">Oops! Something went wrong.</span>
                        <div className="text-sm mt-1">
                            We couldn't load the data. Please check your connection or try
                            again.
                        </div>
                    </div>
                </div>
                <button
                    className="bg-red-500 to-yellow-400 hover:bg-red-600 text-white font-bold py-2 px-6 rounded shadow-lg focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-50 transition"
                    onClick={() => window.location.reload()}
                    aria-label="Retry loading data"
                >
                    Retry
                </button>
            </div>
        );



    const sortedEngineers = Object.entries(engineerMetricsRecords)
        .map(([name, metrics]) => {
            const m = metrics as {
                totalHours: number;
                tasks: Set<any>;
                storyPoints: number;
                taskCount: number;
                dayOff: number;
                assigneeId: string;
            };
            return {
                name,
                ...m,
                efficiency: m.taskCount > 0 ? m.totalHours / m.taskCount : 0,
            };
        })
        .sort((a, b) => b.totalHours - a.totalHours);

    const endDateTimeDistribution = endDate ?? startDate ?? new Date().toISOString();
    const startDateForCalc = startDate ?? endDate ?? new Date().toISOString();
    const totalDaysFilter = differenceInCalendarDays(new Date(endDateTimeDistribution), new Date(startDateForCalc)) + 1;
    const totalHoursFilter = totalDaysFilter * 8;
    const getPerformanceLevel = (hours: number) => {
        const percentage = (hours / totalHoursFilter) * 100;
        if (percentage >= 80)
            return {
                level: "High",
                color:
                    "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            };
        if (percentage >= 60)
            return {
                level: "Medium",
                color:
                    "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            };
        return {
            level: "Low",
            color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
        };
    };

    return (
        <div className="space-y-6 overflow-x-hidden">
            {/* Tabs for different views */}
            <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full md:w-2/3 grid-cols-3 mb-4">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="operation">Operation</TabsTrigger>
                    <TabsTrigger value="weekly_report">Weekly Report</TabsTrigger>
                </TabsList>

                {/* Overview Tab */}
                <TabsContent value="overview" className="space-y-6">{/* Task Table */}
                    {/* Engineer Metric Cards */}
                    <MetricCards
                        metrics={isAnyMetricLoading ? skeletonEngineerMetrics : engineerMetrics}
                        columns={5}
                        section="Engineer Task Backlog"
                    />

                    {/* QA Metric Cards */}
                    <MetricCards
                        metrics={isAnyMetricLoading ? skeletonQaMetrics : qaMetrics}
                        columns={5}
                        section="QA Task Backlog"
                    />
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <User className="h-5 w-5 mr-2" />
                                Team Members
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {isLoading ? (
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    {Array.from({ length: 6 }).map((_, idx) => (
                                        <Card key={idx}>
                                            <CardContent className="p-4">
                                                <div className="flex items-start justify-between mb-3">
                                                    <div className="flex items-center space-x-3">
                                                        <Skeleton className="h-10 w-10 rounded-full" />
                                                        <div className="space-y-2">
                                                            <Skeleton className="h-4 w-28" />
                                                            <Skeleton className="h-4 w-24" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="space-y-3">
                                                    <div className="flex items-center justify-between text-sm">
                                                        <Skeleton className="h-4 w-24" />
                                                        <Skeleton className="h-4 w-12" />
                                                    </div>
                                                    <div className="flex items-center justify-between text-sm">
                                                        <Skeleton className="h-4 w-24" />
                                                        <Skeleton className="h-4 w-12" />
                                                    </div>
                                                    <div className="flex items-center justify-between text-sm">
                                                        <Skeleton className="h-4 w-24" />
                                                        <Skeleton className="h-4 w-12" />
                                                    </div>
                                                    <div className="flex items-center justify-between text-sm">
                                                        <Skeleton className="h-4 w-24" />
                                                        <Skeleton className="h-4 w-12" />
                                                    </div>
                                                    <div className="flex items-center justify-between text-sm">
                                                        <Skeleton className="h-4 w-24" />
                                                        <Skeleton className="h-4 w-12" />
                                                    </div>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            ) : sortedEngineers.length === 0 ? (
                                <div className="flex flex-col items-center justify-center py-12 text-center">
                                    <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-4 mb-4">
                                        <User className="h-8 w-8 text-gray-400 dark:text-gray-600" />
                                    </div>
                                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                                        No Team Members Found
                                    </h3>
                                    <p className="text-gray-500 dark:text-gray-400 max-w-sm">
                                        No team member data is available for the selected date range.
                                        Try adjusting your date filter or check if tasks exist.
                                    </p>
                                </div>
                            ) : (
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    {sortedEngineers.map((engineer) => {
                                        const performance = getPerformanceLevel(
                                            engineer.totalHours
                                        );

                                        return (
                                            <Card key={engineer.name}>
                                                <CardContent className="p-4">
                                                    <div className="flex items-start justify-between mb-3">
                                                        <div className="flex items-center space-x-3">
                                                            <Avatar>
                                                                <AvatarFallback className="bg-primary text-primary-foreground tracking-wide flex items-center justify-center">
                                                                    {(() => {
                                                                        const initials = engineer.name
                                                                            .split(" ")
                                                                            .map((n) => n[0])
                                                                            .join("")
                                                                            .toUpperCase();
                                                                        return initials.length > 2
                                                                            ? initials.slice(0, 2)
                                                                            : initials;
                                                                    })()}
                                                                </AvatarFallback>
                                                            </Avatar>
                                                            <div>
                                                                <h3 className="font-semibold text-sm">
                                                                    {engineer.name}
                                                                </h3>
                                                                <Badge
                                                                    className={performance.color}
                                                                    variant="secondary"
                                                                >
                                                                    {performance.level} Activity
                                                                </Badge>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div className="space-y-2">
                                                        <div className="flex items-center justify-between text-sm">
                                                            <span className="flex items-center">
                                                                <Clock className="h-3 w-3 mr-1" />
                                                                Hours
                                                            </span>
                                                            <span className="font-medium">
                                                                {engineer.totalHours.toFixed(1)}h
                                                            </span>
                                                        </div>

                                                        <div className="flex items-center justify-between text-sm">
                                                            <span className="flex items-center">
                                                                <CheckCircle className="h-3 w-3 mr-1" />
                                                                Tasks
                                                            </span>
                                                            <span className="font-medium">
                                                                {engineer.taskCount}
                                                            </span>
                                                        </div>

                                                        <div className="flex items-center justify-between text-sm">
                                                            <span className="flex items-center">
                                                                <TrendingUp className="h-3 w-3 mr-1" />
                                                                Story Points
                                                            </span>
                                                            <span className="font-medium">
                                                                {engineer.storyPoints}
                                                            </span>
                                                        </div>

                                                        <div className="flex items-center justify-between text-sm">
                                                            <span className="flex items-center">
                                                                <TrendingUp className="h-3 w-3 mr-1" />
                                                                Efficiency
                                                            </span>
                                                            <span className="font-medium">
                                                                {engineer.efficiency.toFixed(1)}h/task
                                                            </span>
                                                        </div>

                                                        <div className="flex items-center justify-between text-sm">
                                                            <span className="flex items-center">
                                                                <Clock className="h-3 w-3 mr-1" />
                                                                Available At
                                                            </span>
                                                            <span className="font-medium">
                                                                {(() => {
                                                                    const id = engineer.assigneeId;
                                                                    if (!id) return '—';
                                                                    const info = availableAtMap[id];
                                                                    if (!info) return '—';
                                                                    if (info.isLoading) return <Skeleton className="h-4 w-24" />;
                                                                    return info.value ?? '—';
                                                                })()}
                                                            </span>
                                                        </div>

                                                        <div className="flex items-center justify-between text-sm">
                                                            <span className="flex items-center">
                                                                <Clock className="h-3 w-3 mr-1" />
                                                                Day Off
                                                            </span>
                                                            <span className="font-medium">
                                                                {engineer.dayOff.toFixed(1)}h
                                                            </span>
                                                        </div>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        );
                                    })}
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Analytics */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {isLoading ? (
                            <>
                                <Card>
                                    <CardHeader>
                                        <CardTitle>
                                            <Skeleton className="h-5 w-40" />
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-4">
                                            <Skeleton className="h-56 w-full" />
                                            <div className="flex items-center gap-3">
                                                <Skeleton className="h-4 w-20" />
                                                <Skeleton className="h-4 w-20" />
                                                <Skeleton className="h-4 w-20" />
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                                <Card>
                                    <CardHeader>
                                        <CardTitle>
                                            <Skeleton className="h-5 w-48" />
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-4">
                                            <Skeleton className="h-56 w-full" />
                                            <div className="grid grid-cols-3 gap-3">
                                                <Skeleton className="h-4 w-full" />
                                                <Skeleton className="h-4 w-full" />
                                                <Skeleton className="h-4 w-full" />
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </>
                        ) : (
                            <>
                                <TimeDistributionChart data={worklogEntries} role='sa' />
                                <ActivityChart data={worklogEntries} role='sa' />
                            </>
                        )}
                    </div>

                    {/* Task Table */}
                    {isLoading ? (
                        <Card>
                            <CardHeader>
                                <CardTitle>
                                    <Skeleton className="h-6 w-48" />
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="mb-4 flex flex-wrap gap-2">
                                    <Skeleton className="h-8 w-64" />
                                    <Skeleton className="h-8 w-32" />
                                    <Skeleton className="h-8 w-32" />
                                </div>
                                <div className="rounded-md border overflow-hidden">
                                    <div className="grid grid-cols-5">
                                        {Array.from({ length: 5 }).map((_, i) => (
                                            <div key={i} className="p-3 border-b bg-muted/30">
                                                <Skeleton className="h-4 w-24" />
                                            </div>
                                        ))}
                                    </div>
                                    <div>
                                        {Array.from({ length: 6 }).map((_, r) => (
                                            <div key={r} className="grid grid-cols-5 border-b">
                                                {Array.from({ length: 5 }).map((_, c) => (
                                                    <div key={c} className="p-3">
                                                        <Skeleton className="h-4 w-full" />
                                                    </div>
                                                ))}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ) : (
                        <TaskTable data={data?.tasks ?? []} />
                    )}
                </TabsContent>

                {/* Weekly Report Tab */}
                <TabsContent value="weekly_report" className="space-y-4">
                    <Suspense
                        fallback={
                            <div className="flex items-center justify-center h-60">
                                <LPLoading />
                            </div>
                        }
                    >
                        <WeeklyTaskReport />
                    </Suspense>
                </TabsContent>

                <TabsContent value="operation" className="space-y-6">
                    <Suspense
                        fallback={
                            <div className="flex items-center justify-center h-60">
                                <LPLoading />
                            </div>
                        }
                    >
                        <Operation />
                    </Suspense>
                </TabsContent>
            </Tabs>
        </div>
    );
}
