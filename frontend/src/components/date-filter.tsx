"use client";

import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "./ui/select";
import { But<PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import { Calendar, X } from "lucide-react";
import { DateRangePicker } from "./date-range-picker";
import { setDateRange, setQuickFilter } from "@/app/dateFilterSlice";
import { startOfWeek } from "date-fns";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/app/store";
import { useDateFilterSync } from "@/hooks/use-date-filter-sync";

export function DateFilter() {
  const dispatch = useDispatch();
  const { startDate, endDate, quickFilter } = useSelector(
    (state: RootState) => state.dateFilter
  );

  // Use the custom hook to keep date filter synchronized
  useDateFilterSync();

  const handleDateRangeChange = (
    start: Date | undefined,
    end: Date | undefined
  ) => {
    handleQuickFilterChange("custom");
    dispatch(
      setDateRange({
        startDate: start ? start.toISOString() : undefined,
        endDate: end ? end.toISOString() : undefined,
      })
    );
  };

  const hasActiveFilters =
    (quickFilter === "custom" && (startDate || endDate)) ||
    (quickFilter !== "this-week" && quickFilter !== "custom");

  const clearAllFilters = () => {
    const weekStart = startOfWeek(new Date(), { weekStartsOn: 1 });
    handleDateRangeChange(
      weekStart,
      new Date(new Date().setDate(weekStart.getDate() + 4))
    );
    handleQuickFilterChange("this-week");
  };

  const handleQuickFilterChange = (filter: string) => {
    dispatch(setQuickFilter(filter));
    if (filter !== "custom") {
      let start: Date | undefined = undefined;
      let end: Date | undefined = undefined;
      const now = new Date();
      if (filter === "today") {
        start = end = now;
      } else if (filter === "this-week") {
        start = startOfWeek(now, { weekStartsOn: 1 });
        end = new Date(new Date(start).setDate(start?.getDate() + 4));
      } else if (filter === "last-week") {
        const lastWeek = new Date(now);
        lastWeek.setDate(now.getDate() - 7);
        start = startOfWeek(lastWeek, { weekStartsOn: 1 });
        end = new Date(new Date(start).setDate(start?.getDate() + 4));
      } else if (filter === "this-month") {
        start = new Date(now.getFullYear(), now.getMonth(), 1);
        end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      } else if (filter === "last-month") {
        start = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        end = new Date(now.getFullYear(), now.getMonth(), 0);
      }
      dispatch(
        setDateRange({
          startDate: start ? start.toISOString() : undefined,
          endDate: end ? end.toISOString() : undefined,
        })
      );
    }
  };
  return (
    <div className="flex flex-col gap-2 sm:gap-3">
      <div className="flex flex-col sm:flex-row items-center gap-2 sm:gap-3">
        <Select value={quickFilter} onValueChange={handleQuickFilterChange}>
          <SelectTrigger className="w-full sm:w-45 min-w-[120px]">
            <Calendar className="h-4 w-4 mr-2 text-foreground" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="today">Today</SelectItem>
            <SelectItem value="this-week">This Week</SelectItem>
            <SelectItem value="last-week">Last Week</SelectItem>
            <SelectItem value="this-month">This Month</SelectItem>
            <SelectItem value="last-month">Last Month</SelectItem>
            <SelectItem value="custom">Custom Range</SelectItem>
          </SelectContent>
        </Select>
        <DateRangePicker
          startDate={startDate ? new Date(startDate) : undefined}
          endDate={endDate ? new Date(endDate) : undefined}
          onDateRangeChange={handleDateRangeChange}
          className="sm:min-w-[120px] w-full sm:w-auto"
        />
      </div>
      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="mt-3 flex flex-wrap items-center gap-2">
          <span className="text-xs sm:text-sm text-foreground">
            Active filters:
          </span>

          {/* Only show date badge if quickFilter is 'custom' */}
          {quickFilter === "custom" && (startDate || endDate) && (
            <Badge variant="secondary" className="text-xs">
              {startDate && endDate
                ? `${new Date(startDate).toLocaleDateString()} - ${new Date(
                    endDate
                  ).toLocaleDateString()}`
                : startDate
                ? `From ${new Date(startDate).toLocaleDateString()}`
                : endDate
                ? `Until ${new Date(endDate).toLocaleDateString()}`
                : null}
              <button
                className="ml-1 p-0 hover:bg-transparent cursor-pointer inline-flex items-center justify-center"
                style={{ pointerEvents: "auto" }}
                onClick={(e) => {
                  e.stopPropagation();
                  // Remove custom date filter: reset to this-week and default range
                  const weekStart = startOfWeek(new Date(), {
                    weekStartsOn: 1,
                  });
                  handleDateRangeChange(
                    weekStart,
                    new Date(new Date().setDate(weekStart.getDate() + 4))
                  );
                  handleQuickFilterChange("this-week");
                }}
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}

          {/* Show quickFilter badge only for non-default, non-custom filters */}
          {quickFilter !== "this-week" && quickFilter !== "custom" && (
            <Badge variant="secondary" className="text-xs">
              {quickFilter &&
                quickFilter.charAt(0).toUpperCase() +
                  quickFilter.slice(1).replace("-", " ")}
              <button
                className="ml-1 p-0 hover:bg-transparent cursor-pointer inline-flex items-center justify-center"
                style={{ pointerEvents: "auto" }}
                onClick={(e) => {
                  e.stopPropagation();
                  const weekStart = startOfWeek(new Date(), {
                    weekStartsOn: 1,
                  });
                  handleDateRangeChange(
                    weekStart,
                    new Date(new Date().setDate(weekStart.getDate() + 4))
                  );
                  handleQuickFilterChange("this-week");
                }}
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}

          <Button
            variant="ghost"
            size="sm"
            className="px-2 py-1 h-7"
            onClick={clearAllFilters}
          >
            Clear all
          </Button>
        </div>
      )}
    </div>
  );
}
