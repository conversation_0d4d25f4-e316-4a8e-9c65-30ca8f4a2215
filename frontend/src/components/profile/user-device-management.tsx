"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { DeviceManagement } from "@/components/profile/device-management";
import { toast } from "sonner";
import { apiClient } from "@/lib/api-client";

interface WebAuthnDevice {
  id: string;
  name: string;
  authenticatorType: "platform" | "cross-platform";
  createdAt: string;
  lastUsedAt?: string;
  isActive: boolean;
}

interface WebAuthnDeviceAPI {
  id: number;
  user_id: number;
  credential_id: string;
  attestation_type: string;
  sign_count: number;
  device_name: string;
  device_type: "platform" | "cross-platform";
  backup_eligible: boolean;
  backup_state: boolean;
  created_at: string;
  last_used_at?: string;
}

export function UserDeviceManagement() {
  const queryClient = useQueryClient();

  // Fetch user devices
  const {
    data: devices = [],
    isLoading,
    error,
    refetch,
  } = useQuery<WebAuthnDevice[]>({
    queryKey: ["user-devices"],
    queryFn: async () => {
      const response = await apiClient.get<WebAuthnDeviceAPI[]>("/api/auth/devices");
      
      if (!response.ok) {
        throw new Error(response.error || "Failed to fetch devices");
      }

      const apiDevices = response.data || [];
      
      // Map API response to component format
      return apiDevices.map((device): WebAuthnDevice => ({
        id: device.id.toString(),
        name: device.device_name,
        authenticatorType: device.device_type,
        createdAt: device.created_at,
        lastUsedAt: device.last_used_at,
        isActive: true, // Assuming all returned devices are active
      }));
    },
  });

  // Register device mutation
  const registerDeviceMutation = useMutation({
    mutationFn: async (deviceName: string) => {
      // Begin registration
      const beginResponse = await apiClient.post<{ publicKey: any; sessionId: string }>("/api/auth/devices/register", {
        device_name: deviceName
      });

      if (!beginResponse.ok) {
        throw new Error(beginResponse.error || "Failed to begin device registration");
      }

      const { publicKey, sessionId } = beginResponse.data!;

      // Create credential
      const credential = await navigator.credentials.create({
        publicKey: publicKey,
      });

      if (!credential) {
        throw new Error("Failed to create credential");
      }

      // Finish registration
      const finishResponse = await apiClient.request("/api/auth/devices/register/finish", {
        method: "POST",
        headers: {
          "X-Session-ID": sessionId,
        },
        body: JSON.stringify({
          device_name: deviceName,
          credential: {
            id: credential.id,
            rawId: Array.from(new Uint8Array((credential as any).rawId)),
            response: {
              clientDataJSON: Array.from(
                new Uint8Array((credential as any).response.clientDataJSON)
              ),
              attestationObject: Array.from(
                new Uint8Array((credential as any).response.attestationObject)
              ),
            },
            type: credential.type,
          },
        }),
      });

      if (!finishResponse.ok) {
        throw new Error(
          finishResponse.error || "Failed to complete device registration"
        );
      }

      return finishResponse.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["user-devices"] });
      toast.success("Device registered successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  // Delete device mutation
  const deleteDeviceMutation = useMutation({
    mutationFn: async (deviceId: string) => {
      const response = await apiClient.delete(`/api/auth/devices/${deviceId}`);
      
      if (!response.ok) {
        throw new Error(response.error || "Failed to delete device");
      }

      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["user-devices"] });
      toast.success("Device deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  const handleRegisterDevice = async (deviceName: string) => {
    await registerDeviceMutation.mutateAsync(deviceName);
  };

  const handleDeleteDevice = async (deviceId: string) => {
    await deleteDeviceMutation.mutateAsync(deviceId);
  };

  const handleRefreshDevices = async () => {
    await refetch();
  };

  return (
    <DeviceManagement
      devices={devices}
      isLoading={isLoading}
      error={error?.message}
      onRegisterDevice={handleRegisterDevice}
      onDeleteDevice={handleDeleteDevice}
      onRefreshDevices={handleRefreshDevices}
    />
  );
}
