"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Fingerprint,
  Shield,
  Smartphone,
  Plus,
  Trash2,
  Calendar,
  AlertTriangle,
} from "lucide-react";

interface WebAuthnDevice {
  id: string;
  name: string;
  authenticatorType: "platform" | "cross-platform";
  createdAt: string;
  lastUsedAt?: string;
  isActive: boolean;
}

interface DeviceManagementProps {
  devices: WebAuthnDevice[];
  isLoading?: boolean;
  error?: string;
  onRegisterDevice: (deviceName: string) => Promise<void>;
  onDeleteDevice: (deviceId: string) => Promise<void>;
  onRefreshDevices: () => Promise<void>;
}

// Function to check if a device was used in the current session
const isCurrentDevice = (device: WebAuthnDevice): boolean => {
  // Check if this device was recently used (within the last hour)
  // This is a heuristic to identify the currently logged device
  if (!device.lastUsedAt) return false;

  const lastUsed = new Date(device.lastUsedAt);
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

  return lastUsed > oneHourAgo;
};

export function DeviceManagement({
  devices,
  isLoading = false,
  error,
  onRegisterDevice,
  onDeleteDevice,
  onRefreshDevices,
}: DeviceManagementProps) {
  const [showRegisterDialog, setShowRegisterDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deviceName, setDeviceName] = useState("");
  const [deviceToDelete, setDeviceToDelete] = useState<WebAuthnDevice | null>(
    null
  );
  const [isRegistering, setIsRegistering] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    if (!showRegisterDialog) {
      setDeviceName("");
    }
  }, [showRegisterDialog]);

  const handleRegisterDevice = async () => {
    if (!deviceName.trim()) return;

    setIsRegistering(true);
    try {
      await onRegisterDevice(deviceName.trim());
      setShowRegisterDialog(false);
      await onRefreshDevices();
    } catch {
      // Error handled by parent component
    } finally {
      setIsRegistering(false);
    }
  };

  const handleDeleteDevice = async () => {
    if (!deviceToDelete) return;

    setIsDeleting(true);
    try {
      await onDeleteDevice(deviceToDelete.id);
      setShowDeleteDialog(false);
      setDeviceToDelete(null);
      await onRefreshDevices();
    } catch {
      // Error handled by parent component
    } finally {
      setIsDeleting(false);
    }
  };

  const getDeviceIcon = (authenticatorType: string) => {
    return authenticatorType === "platform" ? Fingerprint : Shield;
  };

  const getDeviceTypeLabel = (authenticatorType: string) => {
    return authenticatorType === "platform"
      ? "Built-in Passkey"
      : "External Passkey";
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Passkey Devices</h2>
          <p className="text-muted-foreground">
            Manage your passkey / biometric authentication devices
          </p>
        </div>
        {devices.length === 0 && (
          <Button
            onClick={() => setShowRegisterDialog(true)}
            disabled={isLoading}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Device
          </Button>
        )}
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid gap-4">
        {devices.length === 0 && !isLoading ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-8">
              <Shield className="w-12 h-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No Passkey Devices</h3>
              <p className="text-sm text-muted-foreground text-center mb-4">
                Add a passkey device to enable passkey / biometric
                authentication.
              </p>
              <Button
                onClick={() => setShowRegisterDialog(true)}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Your First Device
              </Button>
            </CardContent>
          </Card>
        ) : (
          devices.map((device) => {
            const DeviceIcon = getDeviceIcon(device.authenticatorType);
            const isCurrent = isCurrentDevice(device);
            return (
              <Card key={device.id}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 rounded-lg bg-primary/10">
                        <DeviceIcon className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <CardTitle className="text-lg">
                            {device.name}
                          </CardTitle>
                          {isCurrent && (
                            <Badge variant="outline" className="text-xs">
                              Current Session
                            </Badge>
                          )}
                        </div>
                        <CardDescription>
                          {getDeviceTypeLabel(device.authenticatorType)}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge
                        variant={device.isActive ? "default" : "secondary"}
                      >
                        {device.isActive ? "Active" : "Inactive"}
                      </Badge>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setDeviceToDelete(device);
                          setShowDeleteDialog(true);
                        }}
                        disabled={isLoading || isCurrent}
                        title={
                          isCurrent
                            ? "Cannot delete device used in current session"
                            : "Delete device"
                        }
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1 text-muted-foreground">
                        <Calendar className="w-4 h-4" />
                        <span>Registered: {formatDate(device.createdAt)}</span>
                      </div>
                    </div>
                    {device.lastUsedAt && (
                      <div className="flex items-center space-x-1 text-muted-foreground">
                        <span>Last used: {formatDate(device.lastUsedAt)}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>

      {/* Register Device Dialog */}
      <Dialog open={showRegisterDialog} onOpenChange={setShowRegisterDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Smartphone className="w-5 h-5" />
              <span>Register New Passkey Device</span>
            </DialogTitle>
            <DialogDescription>
              Give your passkey device a name to help you identify it later.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="device-name">Device Name</Label>
              <Input
                id="device-name"
                placeholder="e.g., iPhone Touch ID, Security Key"
                value={deviceName}
                onChange={(e) => setDeviceName(e.target.value)}
                disabled={isRegistering}
              />
            </div>
            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription>
                You'll be prompted to use your passkey authentication.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowRegisterDialog(false)}
              disabled={isRegistering}
            >
              Cancel
            </Button>
            <Button
              onClick={handleRegisterDevice}
              disabled={!deviceName.trim() || isRegistering}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200"
            >
              {isRegistering ? "Registering..." : "Register Device"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Device Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-destructive" />
              <span>Remove Passkey Device</span>
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to remove "{deviceToDelete?.name}"? This
              action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          {deviceToDelete && isCurrentDevice(deviceToDelete) ? (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Cannot delete this device as it was used in your current
                session. Please use a different device to remove this one.
              </AlertDescription>
            </Alert>
          ) : (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Removing this device will prevent you from using it for
                authentication. Make sure you have another way to access your
                account.
              </AlertDescription>
            </Alert>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowDeleteDialog(false);
                setDeviceToDelete(null);
              }}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteDevice}
              disabled={
                isDeleting ||
                (deviceToDelete ? isCurrentDevice(deviceToDelete) : false)
              }
            >
              {isDeleting ? "Removing..." : "Remove Device"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
