"use client";

import { useState, useEffect } from "react";
import CodeMirror from "@uiw/react-codemirror";
import { json } from "@codemirror/lang-json";
import { oneDark } from "@codemirror/theme-one-dark";
import { EditorView, keymap } from "@codemirror/view";
import { selectAll } from "@codemirror/commands";
import { useTheme } from "next-themes";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Upload, Trash2, Info, Copy, Check, Download } from "lucide-react";
import { toast } from "sonner";
import { processJsonWithComments } from "@/lib/utils";

export function JsonFormatterView() {
  const { resolvedTheme } = useTheme();
  const [input, setInput] = useState("");
  const [output, setOutput] = useState("");
  const [error, setError] = useState("");
  const [isValid, setIsValid] = useState(false);
  const [fixesApplied, setFixesApplied] = useState<string[]>([]);
  const [isCopied, setIsCopied] = useState(false);

  const validateAndFormatJson = (jsonString: string) => {
    if (!jsonString.trim()) {
      setOutput("");
      setError("");
      setIsValid(false);
      setFixesApplied([]);
      return;
    }

    // Handle JSON strings with multiple levels of escaping
    let processedString = jsonString.trim();

    // Recursively handle nested JSON strings
    const unescapeJsonString = (str: string, depth: number = 0): string => {
      // Prevent infinite recursion
      if (depth > 10) return str;

      // Check if it's a quoted JSON string
      if (str.startsWith('"') && str.endsWith('"')) {
        try {
          // Try to parse as a JSON string first
          const parsed = JSON.parse(str);
          if (typeof parsed === "string") {
            // If the parsed result is still a string, try to unescape it further
            return unescapeJsonString(parsed, depth + 1);
          }
          return parsed;
        } catch {
          // If parsing fails, manually remove outer quotes and unescape
          let unquoted = str.slice(1, -1);

          // Handle common escape sequences
          unquoted = unquoted
            .replace(/\\"/g, '"')
            .replace(/\\\\/g, "\\")
            .replace(/\\n/g, "\n")
            .replace(/\\r/g, "\r")
            .replace(/\\t/g, "\t");

          // Try to process the unescaped string again
          if (
            depth < 5 &&
            (unquoted.includes('{"') || unquoted.includes('["'))
          ) {
            return unescapeJsonString(unquoted, depth + 1);
          }

          return unquoted;
        }
      }

      return str;
    };

    processedString = unescapeJsonString(processedString);

    const result = processJsonWithComments(processedString);

    if (result.isValid) {
      setOutput(result.formattedJson);
      setError("");
      setIsValid(true);
      setFixesApplied(result.fixesApplied);

      // Show toast notification if fixes were applied
      if (result.fixesApplied.length > 0) {
        toast.success(`Auto-fixed: ${result.fixesApplied.join(", ")}`);
      }
    } else {
      setError(result.error || "Unknown error");
      setOutput("");
      setIsValid(false);
      setFixesApplied([]);
    }
  };

  // Auto-format when input changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      validateAndFormatJson(input);
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [input]);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === "application/json") {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        setInput(content);
      };
      reader.readAsText(file);
    } else {
      toast.error("Please select a valid JSON file");
    }
    event.target.value = "";
  };

  const handleClear = () => {
    setInput("");
    setOutput("");
    setError("");
    setIsValid(false);
    setFixesApplied([]);
  };

  const handlePasteAndFormat = async () => {
    try {
      const text = await navigator.clipboard.readText();
      setInput(text);
      toast.success("JSON pasted!");
    } catch {
      toast.error("Failed to paste from clipboard");
    }
  };

  return (
    <main className="space-y-4 md:space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">JSON Formatter</h1>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Input Section */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Input JSON</CardTitle>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePasteAndFormat}
                >
                  Paste
                </Button>
                <label htmlFor="file-upload">
                  <Button variant="outline" size="sm" asChild>
                    <span>
                      <Upload className="h-4 w-4 mr-1" />
                      Upload
                    </span>
                  </Button>
                </label>
                <input
                  id="file-upload"
                  type="file"
                  accept=".json"
                  onChange={handleFileUpload}
                  className="hidden"
                />
                <Button variant="outline" size="sm" onClick={handleClear}>
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-1">
            <div className="h-[500px] border rounded-md overflow-hidden">
              <CodeMirror
                value={input}
                height="500px"
                extensions={[json()]}
                onChange={(value) => setInput(value)}
                theme={resolvedTheme === "dark" ? oneDark : undefined}
                placeholder="Paste your JSON here..."
                basicSetup={{
                  lineNumbers: true,
                  foldGutter: true,
                  dropCursor: false,
                  allowMultipleSelections: false,
                  indentOnInput: true,
                  bracketMatching: true,
                  closeBrackets: true,
                  autocompletion: true,
                  highlightSelectionMatches: false,
                }}
              />
            </div>
          </CardContent>
        </Card>

        {/* Output Section */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CardTitle>Output</CardTitle>
                {isValid && (
                  <>
                    <Badge
                      variant="default"
                      className="bg-green-500 dark:bg-green-700"
                    >
                      {fixesApplied.length > 0
                        ? "Auto-fixed JSON"
                        : "Valid JSON"}
                    </Badge>
                    {fixesApplied.length > 0 && (
                      <Badge
                        variant="secondary"
                        className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mr-2"
                      >
                        <Info className="h-3 w-3 mr-1" />
                        {fixesApplied.length} fix
                        {fixesApplied.length > 1 ? "es" : ""}
                      </Badge>
                    )}
                  </>
                )}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={async () => {
                    try {
                      await navigator.clipboard.writeText(output);
                      setIsCopied(true);
                      toast.success("JSON copied to clipboard!");
                      setTimeout(() => setIsCopied(false), 2000);
                    } catch {
                      toast.error("Failed to copy to clipboard");
                    }
                  }}
                  disabled={!output}
                  className={
                    isCopied
                      ? "bg-green-100 dark:bg-green-900 border-green-500"
                      : ""
                  }
                >
                  {isCopied ? (
                    <Check className="h-4 w-4 mr-1 text-green-600" />
                  ) : (
                    <Copy className="h-4 w-4 mr-1" />
                  )}
                  {isCopied ? "Copied!" : "Copy"}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const blob = new Blob([output], {
                      type: "application/json",
                    });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement("a");
                    a.href = url;
                    a.download = `formatted-${Date.now()}.json`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                    toast.success("JSON file downloaded!");
                  }}
                  disabled={!output}
                >
                  <Download className="h-4 w-4 mr-1" />
                  Download
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-1">
            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>
                  <strong>JSON Error:</strong> {error}
                </AlertDescription>
              </Alert>
            )}

            {fixesApplied.length > 0 && (
              <Alert className="mb-4 border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
                <Info className="h-4 w-4" />
                <AlertDescription>
                  <strong>Auto-fixes applied:</strong>
                  <ul className="mt-1 list-disc pl-5 space-y-1">
                    {fixesApplied.map((fix) => (
                      <li key={fix} className="text-sm">
                        {fix}
                      </li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {output ? (
              <div className="h-[500px] border rounded-md overflow-hidden">
                <CodeMirror
                  value={output}
                  height="500px"
                  extensions={[
                    json(),
                    EditorView.editable.of(false),
                    keymap.of([
                      {
                        key: "Ctrl-a",
                        mac: "Cmd-a",
                        run: selectAll,
                        preventDefault: true,
                      },
                    ]),
                  ]}
                  theme={resolvedTheme === "dark" ? oneDark : undefined}
                  basicSetup={{
                    lineNumbers: true,
                    foldGutter: true,
                    dropCursor: false,
                    allowMultipleSelections: false,
                    indentOnInput: true,
                    bracketMatching: true,
                    closeBrackets: true,
                    autocompletion: true,
                    highlightSelectionMatches: false,
                  }}
                />
              </div>
            ) : (
              <div className="border p-4 rounded-md h-[500px] flex items-center justify-center text-muted-foreground">
                {error
                  ? "Fix the JSON errors to see formatted output"
                  : "Formatted JSON will appear here automatically"}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </main>
  );
}
