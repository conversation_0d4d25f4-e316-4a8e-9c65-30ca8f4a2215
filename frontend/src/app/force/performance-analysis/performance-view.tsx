"use client";

import { DateFilter } from "@/components/date-filter";
import { LPLoading } from "@/components/lp-loading";
import { TaskOverview } from "@/components/performance-analysis/task-overview";
import { Suspense } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/app/store";
import { format, startOfDay, endOfDay } from "date-fns";

export function PerformanceView() {
  const { startDate, endDate } = useSelector(
    (state: RootState) => state.dateFilter
  );

  // Format dates for JIRA JQL: "yyyy-MM-dd HH:mm".
  // For startDate we set time to 00:00, for endDate to 23:59.
  // Using 24-hour format (HH:mm) so start is 00:00 and end is 23:59.
  const formatDateForJQL = (dateString: string | undefined, isStart = false) => {
    if (!dateString) return null;
    const date = new Date(dateString);
    const adjusted = isStart ? startOfDay(date) : endOfDay(date);
    return format(adjusted, "yyyy-MM-dd HH:mm");
  };

  const formattedStartDate = formatDateForJQL(startDate, true);
  const formattedEndDate = formatDateForJQL(endDate, false);

  // Construct JQL query with dynamic date range. If formatted dates are present,
  // quote them as string literals for the DURING clause; otherwise use JIRA functions.
  const duringStart = formattedStartDate ? `"${formattedStartDate}"` : "startOfWeek()";
  const duringEnd = formattedEndDate ? `"${formattedEndDate}"` : "endOfWeek()";

  const jql = `project = Principal and status WAS IN ("In Progress", "In Testing Dev (Optional)", "In testing stg (OPTIONAL)") DURING (${duringStart}, ${duringEnd}) ORDER BY assignee`;

  return (
    <>
      <DateFilter />
      <Suspense fallback={<LPLoading />}>
        <TaskOverview jql={jql} />
      </Suspense>
    </>
  );
}
