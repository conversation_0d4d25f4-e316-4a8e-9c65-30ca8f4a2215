"use client";
import {<PERSON>, User, <PERSON><PERSON>lertIcon, CheckCircleIcon, Target} from "lucide-react";
import {Badge} from "@/components/ui/badge";
import {<PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardTitle,} from "@/components/ui/card";
import {Avatar, AvatarFallback} from "@/components/ui/avatar";
import Link from "next/link";
import {Button} from "@/components/ui/button";
import {countWeekdaysExcludingHolidays, SDETMembers} from "@/lib/utils";
import {MetricCards} from "@/components/metric-cards";
import React from "react";
import {format} from "date-fns";
import {useSelector} from "react-redux";
import {RootState} from "@/app/store";

// Utility: Extract initials for avatar fallback
const getInitials = (name: string) => {
    const initials = name
        .split(" ")
        .map((n) => n[0])
        .join("")
        .toUpperCase();
    return initials.length > 2 ? initials.slice(0, 2) : initials;
};
const getPerformanceColor = (level: string) => {
    if (level === "under") {
        return "bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200"
    }
    if (level === "over") {
        return "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200"
    }
    return "bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200"
}

interface TeamMembersSdetProps {
  dataSdet: {
      dataWorklog: any;
      dataProject: any;
      dataOnGoingDev: any;
      dataMaintenance: any;
      dataPending: any;
      dataCarryOverPending: any;
      dataCarryOverCompleted: any;
  }
}
interface SdetMetrics {
  totalHoursWorking: number;
  totalHoursProject: number;
  totalHoursOnGoingDevelopment: number;
  totalHoursRegression: number;
  totalHoursMaintenance: number;
  totalHoursOthers: number;
  totalHoursOnLeave: number;
  tasksDone: any[];
  storyPointsProject: number;
  storyPointsOnGoingDevelopment: number;
  storyPointsMaintenance: number;
  tasksPending: any[];
  storyPointsPending: number;
  tasksCarryOverPending: any[];
  storyPointsCarryOverPending: number;
  tasksCarryOverCompleted: any[];
  storyPointsCarryOverCompleted: number;
  performance: any;
  name: string;
  userID: string;
  pastCapacity: number;
  remainingStoryPoints: number;
}

const getTotalHours = (data, userId, type) => {
    if (type === "General") {
        return data.filter((item) => item.user_id === userId && item.type_task !== "Project Pareto" && item.type_task !== "Maintenance" && item.type_task !== "On Going Development" && item.type_task !== "Regression").reduce((total, worklog) => {
            return total + worklog.work_log_time_spent_hour;
        }, 0)
    }
    return data.filter((item) => item.user_id === userId && item.type_task === type).reduce((total, worklog) => {
        return total + worklog.work_log_time_spent_hour;
    }, 0)
}
const getAllTotalHours = (data, userId) => {
    return data.filter((item) => item.user_id === userId).reduce((total, worklog) => {
        return total + worklog.work_log_time_spent_hour;
    }, 0)
}
const getTotalHoursRegression = (data, userId) => {
    return data.filter((item) => item.user_id === userId && item.type === "Regression").reduce((total, worklog) => {
        return total + worklog.work_log_time_spent_hour;
    }, 0)
}
const getStoryPoints = (data, name) => {
    return data.filter((item) => item.assignee === name).reduce((total, worklog) => {
        return total + worklog.sp;
    }, 0)
}
const getDoneTasks = (data, name) => {
    return data.filter((item) => item.assignee === name)
}

export function TeamMembersLeaderboardSdet({ dataSdet }: TeamMembersSdetProps) {
  const { startDate, endDate } = useSelector(
      (state: RootState) => state.dateFilter
  );

  const endDateTimeDistribution = endDate ? endDate : startDate;
  let remainingDays = countWeekdaysExcludingHolidays(format(new Date(), "yyyy-MM-dd"), format(new Date(endDateTimeDistribution ?? 0), "yyyy-MM-dd"));
  let pastDays = countWeekdaysExcludingHolidays(format(new Date(), "yyyy-MM-dd"), format(new Date(startDate ?? 0), "yyyy-MM-dd"));
  if (new Date().setHours(0, 0, 0, 0) > new Date(endDateTimeDistribution ?? 0).setHours(0, 0, 0, 0)) {
      remainingDays = 0;
      pastDays = countWeekdaysExcludingHolidays(format(new Date(startDate ?? 0), "yyyy-MM-dd"), format(new Date(endDateTimeDistribution ?? 0), "yyyy-MM-dd"), );
  }
    if (new Date().setHours(0, 0, 0, 0) <= new Date(startDate ?? 0).setHours(0, 0, 0, 0)) {
        pastDays = 0;
    }
  const remainingCapacitySdet = (Object.keys(SDETMembers).length - 1) * 5 * remainingDays;
  const reducedCapacitySdet = ((dataSdet.dataWorklog.filter((worklog) => worklog.user_id !== "712020:66f39c08-1d2b-43db-856a-f15b448dc986" && worklog.type_task !== "Project Pareto" && worklog.type_task !== "Maintenance" && worklog.type_task !== "On Going Development").reduce((total, worklog) => total + worklog.work_log_time_spent_hour,  0)) / 8);
  const pastCapacitySdet = Math.floor(((Object.keys(SDETMembers).length - 1) * 5 * pastDays) - (reducedCapacitySdet * 5));

  const sdetMembersMetrics = Object.entries(SDETMembers).map(([userId, name]): SdetMetrics => {
      const totalGeneralHours = getTotalHours(dataSdet.dataWorklog, userId, "General");
      const reducedDaysCapacityPerMember = totalGeneralHours / 8;
      let pastCapacityPerMember = Math.floor((pastDays * 5) - (reducedDaysCapacityPerMember * 5));
      const metricsSdet = {
          userID: userId,
          name: name,
          totalHoursWorking: getAllTotalHours(dataSdet.dataWorklog, userId),
          totalHoursMaintenance: getTotalHours(dataSdet.dataWorklog, userId, "Maintenance"),
          totalHoursProject: getTotalHours(dataSdet.dataWorklog, userId, "Project Pareto"),
          totalHoursOnGoingDevelopment: getTotalHours(dataSdet.dataWorklog, userId, "On Going Development"),
          totalHoursRegression: getTotalHoursRegression(dataSdet.dataWorklog, userId),
          totalHoursOthers: totalGeneralHours,
          totalHoursOnLeave: 0,
          storyPointsMaintenance: getStoryPoints(dataSdet.dataMaintenance, name),
          storyPointsOnGoingDevelopment: getStoryPoints(dataSdet.dataOnGoingDev, name),
          storyPointsProject: getStoryPoints(dataSdet.dataProject, name),
          tasksDone: [
              ...getDoneTasks(dataSdet.dataProject, name),
              ...getDoneTasks(dataSdet.dataMaintenance, name),
              ...getDoneTasks(dataSdet.dataOnGoingDev, name),
          ],
          tasksPending: dataSdet.dataPending.filter((item) => item.assignee === name),
          storyPointsPending: dataSdet.dataPending.filter((item) => item.assignee === name).reduce((total, item) => {
              return total + item.sp
          }, 0),
          tasksCarryOverPending: dataSdet.dataCarryOverPending.filter((item) => item.assignee === name),
          storyPointsCarryOverPending: dataSdet.dataCarryOverPending.filter((item) => item.assignee === name).reduce((total, item) => {
              return total + item.sp
          }, 0),
          tasksCarryOverCompleted: dataSdet.dataCarryOverCompleted.filter((item) => item.assignee === name),
          storyPointsCarryOverCompleted: dataSdet.dataCarryOverCompleted.filter((item) => item.assignee === name).reduce((total, item) => {
              return total + item.sp
          }, 0),
          performance: null
      }
      if (userId === "712020:66f39c08-1d2b-43db-856a-f15b448dc986") {
          pastCapacityPerMember = Math.floor(pastCapacityPerMember - ((metricsSdet.totalHoursProject + metricsSdet.totalHoursOnGoingDevelopment) / 5));
      }
      const totalStoryPoint = metricsSdet.storyPointsProject + metricsSdet.storyPointsOnGoingDevelopment + metricsSdet.storyPointsMaintenance;
      const normalizeUpStoryPoint = totalStoryPoint * 1.1;
      const normalizeDownStoryPoint = totalStoryPoint * 0.9;
      return {
          ...metricsSdet,
          pastCapacity: pastCapacityPerMember,
          performance: {
              level: normalizeDownStoryPoint > pastCapacityPerMember ? "over" : normalizeUpStoryPoint < pastCapacityPerMember ? "under" : "meet"
          },
          remainingStoryPoints: pastCapacityPerMember - (metricsSdet.storyPointsProject + metricsSdet.storyPointsOnGoingDevelopment + metricsSdet.storyPointsMaintenance)
      }
  }).sort((a, b) => b.remainingStoryPoints - a.remainingStoryPoints);

  if (sdetMembersMetrics.length === 0) {
    return (
      <div className="flex flex-col gap-3">
        <Card>
          {/* Card Header */}
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              Team Members
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-4 mb-4">
                <User className="h-8 w-8 text-gray-400 dark:text-gray-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                No Team Members Found
              </h3>
              <p className="text-gray-500 dark:text-gray-400 max-w-sm">
                No team member data is available for the selected date
                range. Try adjusting your date filter or check if work logs
                exist.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

    const totalTaskProjectPending = dataSdet.dataPending.filter((data) => data.type_task === "Project Pareto");
    const totalStoryPointProjectPending = totalTaskProjectPending.reduce((total, issue) => {
        return total + issue.sp;
    }, 0);
    const totalStoryPointProjectDone = dataSdet.dataProject.reduce((total, issue) => {
        return total + issue.sp;
    }, 0);
    const totalStoryPointProjectDoneCarryOver = dataSdet.dataCarryOverCompleted.filter((data) => data.type_task === "Project Pareto").reduce((total, item) => {
        return total + item.sp;
    }, 0);
    const doneTasksPareto = dataSdet.dataProject.length;
    const doneTasksParetoCarryover = dataSdet.dataCarryOverCompleted.filter((data) => data.type_task === "Project Pareto").length
    const totalTaskCarryOverProject = dataSdet.dataCarryOverPending.filter((issue) => issue.type_task === "Project Pareto");
    const totalStoryPointCarryOverProject = totalTaskCarryOverProject.reduce((total, issue) => {
        return total + issue.sp;
    }, 0);

    const totalTaskOngoingDevPending = dataSdet.dataPending.filter((data) => data.type_task === "On Going Development");
    const totalStoryPointOngoingDevPending = totalTaskOngoingDevPending.reduce((total, issue) => {
        return total + issue.sp;
    }, 0);
    const totalStoryPointOngoingDevDone = dataSdet.dataOnGoingDev.reduce((total, issue) => {
        return total + issue.sp;
    }, 0);
    const totalStoryPointOngoingDevDoneCarryOver = dataSdet.dataCarryOverCompleted.filter((data) => data.type_task === "On Going Development").reduce((total, item) => {
        return total + item.sp;
    }, 0);
    const doneTasksOngoingDev =  dataSdet.dataOnGoingDev.length;
    const doneTasksOngoingDevCarryOver = dataSdet.dataCarryOverCompleted.filter((data) => data.type_task === "On Going Development").length;
    const totalTaskCarryOverOngoingDev = dataSdet.dataCarryOverPending.filter((issue) => issue.type_task === "On Going Development");
    const totalStoryPointCarryOverOngoingDev = totalTaskCarryOverOngoingDev.reduce((total, issue) => {
        return total + issue.sp;
    }, 0);
    const totalHour = [...dataSdet.dataProject, ...dataSdet.dataOnGoingDev, ...dataSdet.dataCarryOverCompleted.filter((data) => data.type_task === "On Going Development" || data.type_task === "Project Pareto")].reduce((total, issue) => {
        return total + issue.time_spent_hours
    }, 0);
    const speedHourSp = totalHour > 0 ? ((totalStoryPointProjectDone + totalStoryPointOngoingDevDone) / totalHour).toFixed(2) : "0";

    const metricsSummaryDoneProjectPareto = [
        {
            title: "Done TC / Story Point Pareto",
            value: `${doneTasksPareto} TC (${totalStoryPointProjectDone} SP)`,
            description: "Total TC / SP Project Pareto",
            icon: Target,
            iconColor: "text-blue-600 dark:text-blue-400",
            isWarning: totalStoryPointProjectDone > 0 ? (totalStoryPointProjectDone + totalStoryPointOngoingDevDone) < pastCapacitySdet : false
        },
        {
            title: "Done Carrypver TC / Story Point Pareto",
            value: `${doneTasksParetoCarryover} TC (${totalStoryPointProjectDoneCarryOver} SP)`,
            description: "Total Done Carryover TC / SP Project Pareto",
            icon: Target,
            iconColor: "text-blue-600 dark:text-blue-400"
        },
        {
            title: "Backlog TC / Story Point Pareto",
            value: `${totalTaskProjectPending.length} TC (${totalStoryPointProjectPending} SP)`,
            description: "Backlog TC / SP This Week",
            icon: Target,
            iconColor: "text-blue-600 dark:text-blue-400",
        },
        {
            title: "Carryover TC / Story Point Pareto",
            value: `${totalTaskCarryOverProject.length} TC (${totalStoryPointCarryOverProject} SP)`,
            description: "Carryover TC / SP Last Week",
            icon: Target,
            iconColor: "text-blue-600 dark:text-blue-400",
            isWarning: totalStoryPointCarryOverProject > 0
        },
    ];
    const metricsSummarySpeedPerHour = [
        {
            title: "Speed AT Development",
            value: `${speedHourSp} SP/Hour`,
            description: "Speed Development Story Point per Hour",
            icon: Target,
            iconColor: "text-blue-600 dark:text-blue-400",
            isWarning: parseFloat(speedHourSp) > 0 ? parseFloat(speedHourSp) < 5/8 : false
        },
        {
            title: "Available Capacity SDET",
            value: `${pastCapacitySdet} SP`,
            description: "Available Capacity Story Point SDET",
            icon: Target,
            iconColor: "text-blue-600 dark:text-blue-400"
        },
    ]
    const metricsSummaryOngoingDevelopment = [
        {
            title: "Done TC / Story Point Ongoing Development",
            value: `${doneTasksOngoingDev} TC (${totalStoryPointOngoingDevDone} SP)`,
            description: "Total TC / SP Ongoing Development",
            icon: Target,
            iconColor: "text-blue-600 dark:text-blue-400",
            isWarning: totalStoryPointOngoingDevDone > 0 ? (totalStoryPointProjectDone + totalStoryPointOngoingDevDone) < pastCapacitySdet : false
        },
        {
            title: "Done Carryover TC / Story Point Ongoing Development",
            value: `${doneTasksOngoingDevCarryOver} TC (${totalStoryPointOngoingDevDoneCarryOver} SP)`,
            description: "Total Done Carryover TC / SP Ongoing Development",
            icon: Target,
            iconColor: "text-blue-600 dark:text-blue-400"
        },
        {
            title: "Backlog TC / Story Point Ongoing Development",
            value: `${totalTaskOngoingDevPending.length} TC (${totalStoryPointOngoingDevPending} SP)`,
            description: "Backlog TC / SP This Week",
            icon: Target,
            iconColor: "text-blue-600 dark:text-blue-400",
            isWarning: totalStoryPointOngoingDevPending > 0 ? (totalStoryPointProjectPending + totalStoryPointOngoingDevPending) > remainingCapacitySdet : false
        },
        {
            title: "Carryover TC / Story Point Ongoing Development",
            value: `${totalTaskCarryOverOngoingDev.length} TC (${totalStoryPointCarryOverOngoingDev} SP)`,
            description: "Carryover TC / SP Last Week",
            icon: Target,
            iconColor: "text-blue-600 dark:text-blue-400",
            isWarning: totalStoryPointCarryOverOngoingDev > 0
        },
    ];

  return (
    <div className="flex flex-col gap-3">
        <MetricCards metrics={metricsSummarySpeedPerHour} columns={1} section={"Summary"} />
        <MetricCards metrics={metricsSummaryDoneProjectPareto} columns={2} section={"Summary Pareto"} />
        <MetricCards metrics={metricsSummaryOngoingDevelopment} columns={2} section={"Summary Ongoing Development"} />
      <Card>
        {/* Card Header */}
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="h-5 w-5 mr-2" />
            Team SDET Members Leaderboard
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {sdetMembersMetrics.map((sdet) => {
              const initials = getInitials(sdet.name);
              return (
                  <Card key={sdet.name}>
                    <CardContent className="p-4">
                      {/* Profile & performance */}
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <Avatar>
                            <AvatarFallback className="bg-primary text-primary-foreground tracking-wide flex items-center justify-center">
                              {initials}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <h3 className="font-semibold text-sm">
                              {sdet.name}
                            </h3>
                            <Badge
                                className={getPerformanceColor(sdet.performance.level)}
                                variant="secondary"
                            >
                              {sdet.performance.level.toUpperCase()} EXPECTATION
                            </Badge>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                          <div className="flex items-center justify-between font-bold">
                              <span className="flex items-center ">
                                LOG WORKING HOUR
                              </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <Clock className="h-3 w-3 mr-1" />
                                Total Working Hours
                              </span>
                              <span className="font-bold">
                                {sdet.totalHoursWorking.toFixed(1)}h
                              </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <Clock className="h-3 w-3 mr-1" />
                                Total Project Hours (Pareto)
                              </span>
                              <span className="font-bold">
                                {sdet.totalHoursProject.toFixed(1)}h
                              </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <Clock className="h-3 w-3 mr-1" />
                                Total On Going Dev Hours
                              </span>
                              <span className="font-bold">
                                {sdet.totalHoursOnGoingDevelopment.toFixed(1)}h
                              </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <Clock className="h-3 w-3 mr-1" />
                                Total Maintenance Hours
                              </span>
                              <span className="font-bold">
                                  {sdet.totalHoursMaintenance.toFixed(1)}h
                              </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <Clock className="h-3 w-3 mr-1" />
                                Total Regression Hours
                              </span>
                              <span className="font-bold">
                                  {sdet.totalHoursRegression.toFixed(1)}h
                              </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <Clock className="h-3 w-3 mr-1" />
                                Total General Hours
                              </span>
                              <span className="font-bold">
                                  {sdet.totalHoursOthers.toFixed(1)}h
                              </span>
                          </div>
                          <hr />
                          <div className="flex items-center justify-between font-bold">
                              <span className="flex items-center ">
                                COMPLETED TASKS THIS WEEK
                              </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <CheckCircleIcon className="h-3 w-3 mr-1" />
                                  Completed All Tasks (SP)
                              </span>
                              <span className="font-bold">
                                  {sdet.tasksDone.length} Tickets ({sdet.tasksDone.reduce((total, task) => { return total + task.sp}, 0)} SP / {sdet.pastCapacity} SP)
                              </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <CheckCircleIcon className="h-3 w-3 mr-1" />
                                  Completed Project (SP)
                              </span>
                              <span className="font-bold">
                                  {sdet.tasksDone.filter((task) => task.type_task === "Project Pareto").length} Tickets ({sdet.tasksDone.filter((task) => task.type_task === "Project Pareto").reduce((total, task) => { return total + task.sp}, 0)} SP)
                              </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <CheckCircleIcon className="h-3 w-3 mr-1" />
                                  Completed Development (SP)
                              </span>
                              <span className="font-bold">
                                  {sdet.tasksDone.filter((task) => task.type_task === "On Going Development").length} Tickets ({sdet.tasksDone.filter((task) => task.type_task === "On Going Development").reduce((total, task) => { return total + task.sp}, 0)} SP)
                              </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <CheckCircleIcon className="h-3 w-3 mr-1" />
                                  Completed Maintenance (SP)
                              </span>
                              <span className="font-bold">
                                  {sdet.tasksDone.filter((task) => task.type_task === "Maintenance").length} Tickets ({sdet.tasksDone.filter((task) => task.type_task === "Maintenance").reduce((total, task) => { return total + task.sp}, 0)} SP)
                              </span>
                          </div>
                          <hr />
                          <div className="flex items-center justify-between font-bold">
                              <span className="flex items-center ">
                                PENDING TASKS THIS WEEK
                              </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <ClockAlertIcon className="h-3 w-3 mr-1" />
                                  Pending Project (SP)
                              </span>
                              <span className="font-bold">
                                  {sdet.tasksPending.filter((task) => task.type_task === "Project Pareto").length} Tickets ({sdet.tasksPending.filter((task) => task.type_task === "Project Pareto").reduce((total, task) => { return total + task.sp}, 0)} SP)
                              </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <ClockAlertIcon className="h-3 w-3 mr-1" />
                                  Pending Development (SP)
                              </span>
                              <span className="font-bold">
                                  {sdet.tasksPending.filter((task) => task.type_task === "On Going Development").length} Tickets ({sdet.tasksPending.filter((task) => task.type_task === "On Going Development").reduce((total, task) => { return total + task.sp}, 0)} SP)
                              </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <ClockAlertIcon className="h-3 w-3 mr-1" />
                                  Pending Maintenance
                              </span>
                              <span className="font-bold">
                                  {sdet.tasksPending.filter((task) => task.type_task === "Maintenance").length} Tickets ({sdet.tasksPending.filter((task) => task.type_task === "Maintenance").reduce((total, task) => { return total + task.sp}, 0)} SP)
                              </span>
                          </div>
                          <hr />
                          <div className="flex items-center justify-between font-bold">
                              <span className="flex items-center ">
                                CARRY OVER TASKS
                              </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <CheckCircleIcon className="h-3 w-3 mr-1" />
                                  Completed Carry Over (SP)
                              </span>
                              <span className="font-bold">
                                  {sdet.tasksCarryOverCompleted.length} Tickets ({sdet.storyPointsCarryOverCompleted} SP)
                              </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <CheckCircleIcon className="h-3 w-3 mr-1" />
                                  Pending Carry Over (SP)
                              </span>
                              <span className="font-bold">
                                  {sdet.tasksCarryOverPending.length} Tickets ({sdet.storyPointsCarryOverPending} SP)
                              </span>
                          </div>
                      </div>
                    </CardContent>
                    <CardFooter>
                        <Button className="w-full" variant="outline" asChild>
                            <Link
                                href={{
                                    pathname: `/sdet/performance-analysis/detail`,
                                    query: { user_id: sdet.userID },
                                }}
                            >
                                View Details
                            </Link>
                        </Button>
                    </CardFooter>
                  </Card>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
