"use client";

import axios from "axios";
import { RootState } from "@/app/store";
import { useQuery } from "@tanstack/react-query";
import { useSelector } from "react-redux";
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, Clock, CheckCircle, TrendingUp, ClipboardList } from "lucide-react";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { API_BASE_URL } from "@/constants";
import { TaskTable } from "./components/task-table";
import { SprintFilter } from "./components/sprint-filter";
import { LPLoading } from "@/components/lp-loading";
import { formatDecimalHours } from "./utils/time-format";

interface SprintOverviewProps {
  jql?: string;
}

interface Task {
  assignee: string;
  created: string;
  labels: string[];
  parent_link: string;
  parent_name: string;
  priority: string;
  reporter: string;
  sp: number;
  status: string;
  task_link: string;
  task_name: string;
  time_spent_hours: number;
  time_spent_human: string;
  time_spent_second: number;
  type: string;
  updated: string;
  worklogs: Array<{
    created_at: string;
    description: string;
    name: string;
    time_spent_hour: number;
    time_spent_human: string;
    time_spent_second: number;
    type: string;
    user_id: string;
  }>;
  // Additional fields for sprint tracking
  sprintId?: number;
  sprintName?: string;
}

interface Sprint {
  id: number;
  name: string;
  state: string;
  startDate?: string;
  endDate?: string;
  originBoardId?: number;
  // Alias untuk SprintFilter compatibility
  start_date?: string;
  end_date?: string;
  complete_date?: string;
  board_id?: number;
  goal?: string;
  boardId?: number;
  completeDate?: string;
}

export function SprintOverview({ jql }: SprintOverviewProps) {
  const { startDate, endDate } = useSelector((state: RootState) => state.dateFilter);

  // State for selected sprints with persistence
  const [selectedSprints, setSelectedSprints] = useState<Sprint[]>([]);
  const [hasManualSelection, setHasManualSelection] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [previousDateFilter, setPreviousDateFilter] = useState({ startDate, endDate });

  // Load persisted state on mount
  useEffect(() => {
    const persistedSprints = sessionStorage.getItem("sprint-overview-selected-sprints");
    const persistedHasManual = sessionStorage.getItem("sprint-overview-has-manual-selection");

    if (persistedSprints) {
      try {
        const parsed = JSON.parse(persistedSprints);
        setSelectedSprints(parsed);
      } catch (e) {
        console.error(e);
      }
    }

    if (persistedHasManual) {
      setHasManualSelection(persistedHasManual === "true");
    }

    setIsInitialized(true);
  }, []);

  // Clear persisted state when date filter changes (for task queries)
  useEffect(() => {
    if (isInitialized) {
      const hasDateChanged = previousDateFilter.startDate !== startDate || previousDateFilter.endDate !== endDate;

      if (hasDateChanged) {
        setPreviousDateFilter({ startDate, endDate });
      }
    }
  }, [startDate, endDate, isInitialized, previousDateFilter]);

  // Fetch sprint data from API (without date filter - get all sprints)
  const sprintQuery = useQuery({
    queryKey: ["sprints", "all"],
    queryFn: async () => {
      const res = await axios.get(API_BASE_URL + `/api/jira/sprint`, {
        headers: {
          Accept: "application/json",
        },
        params: {
          board_id: 75,
        },
      });

      // Transform data to include alias fields for SprintFilter compatibility
      const transformedSprints =
        res.data.values?.map((sprint: Sprint) => ({
          ...sprint,
          start_date: sprint.startDate,
          end_date: sprint.endDate,
          board_id: sprint.originBoardId,
          boardId: sprint.originBoardId,
          completeDate: "",
          complete_date: "",
          goal: "",
        })) || [];

      return { values: transformedSprints };
    },
  });

  // Auto-select active sprints when data is loaded (only if no manual selection yet and no persisted data)
  useEffect(() => {
    if (sprintQuery.data?.values && selectedSprints.length === 0 && !hasManualSelection && isInitialized) {
      const activeSprints = sprintQuery.data.values.filter((sprint: Sprint) => sprint.state === "active");
      if (activeSprints.length > 0) {
        setSelectedSprints(activeSprints);
      }
    }
  }, [sprintQuery.data, selectedSprints.length, hasManualSelection, isInitialized]);

  // Handle sprint selection with persistence
  const handleSprintSelect = (sprints: Sprint[]) => {
    setSelectedSprints(sprints);
    setHasManualSelection(true);

    // Persist to sessionStorage (no date filter needed)
    sessionStorage.setItem("sprint-overview-selected-sprints", JSON.stringify(sprints));
    sessionStorage.setItem("sprint-overview-has-manual-selection", "true");
  };

  // Task query - fetch tasks for each sprint separately to track sprint-specific hours
  const taskQuery = useQuery({
    queryKey: ["tasks", selectedSprints.map((s) => s.id).join(","), selectedSprints.length],
    queryFn: async () => {
      // If we have selected sprints, fetch each sprint separately
      if (selectedSprints.length > 0) {
        const sprintTasksPromises = selectedSprints.map(async (sprint) => {
          const sprintJql = `project = GQA AND sprint IN (${sprint.id})`;
          const res = await axios.get(API_BASE_URL + `/api/jira/task`, {
            headers: {
              Accept: "application/json",
            },
            params: {
              jql: sprintJql,
            },
          });

          // Add sprint info to each task
          const tasksWithSprint = res.data.tasks.map((task: Task) => ({
            ...task,
            sprintId: sprint.id,
            sprintName: sprint.name,
          }));

          return {
            sprint,
            tasks: tasksWithSprint,
          };
        });

        const sprintTasksResults = await Promise.all(sprintTasksPromises);

        // Combine all tasks while preserving sprint information
        const allTasks = sprintTasksResults.flatMap((result) => result.tasks);

        return {
          tasks: allTasks,
          sprintResults: sprintTasksResults,
        };
      }

      // Fallback to original behavior for default JQL
      const res = await axios.get(API_BASE_URL + `/api/jira/task`, {
        headers: {
          Accept: "application/json",
        },
        params: {
          jql: jql || `project = GQA AND sprint IN (1849)`,
        },
      });
      return {
        tasks: res.data.tasks,
        sprintResults: [],
      };
    },
    // Only fetch tasks after sprint query is done and initialization is complete
    enabled:
      sprintQuery.isSuccess &&
      isInitialized &&
      (selectedSprints.length > 0 || (hasManualSelection && selectedSprints.length === 0) || !!jql),
  });

  // Show error state for any API failures
  if (sprintQuery.error || taskQuery.error) {
    return (
      <div className="flex flex-col items-center justify-center h-full animate-fade-in bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="flex items-center bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded mb-4 shadow-md animate-fade-in">
          <svg
            className="w-6 h-6 mr-3 text-red-500"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M18.364 5.636l-12.728 12.728M5.636 5.636l12.728 12.728"
            />
          </svg>
          <div>
            <span className="font-bold">Oops! Something went wrong.</span>
            <div className="text-sm mt-1">
              {sprintQuery.error && "Failed to load sprint data. "}
              {taskQuery.error && "Failed to load task data. "}
              Please check your connection or try again.
            </div>
          </div>
        </div>
        <button
          className="bg-red-500 to-yellow-400 hover:bg-red-600 text-white font-bold py-2 px-6 rounded shadow-lg focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-50 transition"
          onClick={() => window.location.reload()}
          aria-label="Retry loading data"
        >
          Retry
        </button>
      </div>
    );
  }

  // Show loading state only for sprint data (full page)
  if (sprintQuery.isLoading) {
    return (
      <div className="flex items-center justify-center h-60">
        <LPLoading />
      </div>
    );
  }

  // Calculate individual engineer metrics from tasks
  const engineerMetricsRecords =
    taskQuery.data?.tasks.reduce((acc, task) => {
      if (task.assignee === "") {
        return acc; // Skip tasks without an assignee
      }

      if (!acc[task.assignee]) {
        acc[task.assignee] = {
          totalHours: 0,
          tasks: new Set(),
          doneTasks: new Set(),
          storyPoints: 0,
          doneStoryPoints: 0,
          taskCount: 0,
          doneTaskCount: 0,
          sprintHours: {}, // Track hours per sprint
        };
      }

      // Calculate hours based on new logic:
      // 1. If there's an "In Progress" worklog, use only that
      // 2. If no "In Progress" worklog, use task's total time_spent_hours
      // 3. If time_spent_hours is 0 but worklogs exist, sum all worklog hours
      let taskHours = 0;
      const inProgressWorklog = task.worklogs?.find((worklog) => worklog.type === "In Progress");

      if (inProgressWorklog) {
        // Use only the "In Progress" worklog hours
        taskHours = inProgressWorklog.time_spent_hour;
      } else if (task.time_spent_hours > 0) {
        // Use task's total hours if available
        taskHours = task.time_spent_hours;
      } else if (task.worklogs && task.worklogs.length > 0) {
        // Fallback: if time_spent_hours is 0 but worklogs exist, sum all worklog hours
        taskHours = task.worklogs.reduce((total, worklog) => total + (worklog.time_spent_hour || 0), 0);
      }

      acc[task.assignee].totalHours += taskHours;
      acc[task.assignee].tasks.add(task.task_link);
      acc[task.assignee].storyPoints += task.sp;

      // Track hours per sprint if sprintId is available
      if (task.sprintId) {
        if (!acc[task.assignee].sprintHours[task.sprintId]) {
          acc[task.assignee].sprintHours[task.sprintId] = {
            hours: 0,
            sprintName: task.sprintName,
          };
        }
        acc[task.assignee].sprintHours[task.sprintId].hours += taskHours;
      }

      if (task.status === "Done") {
        acc[task.assignee].doneStoryPoints += task.sp;
        acc[task.assignee].doneTasks.add(task.task_link);
      }
      acc[task.assignee].taskCount = acc[task.assignee].tasks.size;
      acc[task.assignee].doneTaskCount = acc[task.assignee].doneTasks.size;

      return acc;
    }, {} as Record<string, any>) || {};

  const sortedEngineers = Object.entries(engineerMetricsRecords)
    .map(([name, metrics]) => {
      const m = metrics as {
        totalHours: number;
        tasks: Set<any>;
        doneTasks: Set<any>;
        storyPoints: number;
        doneStoryPoints: number;
        taskCount: number;
        doneTaskCount: number;
        sprintHours: Record<string, { hours: number; sprintName: string }>;
      };
      return {
        name,
        ...m,
        efficiency: m.taskCount > 0 ? m.totalHours / m.taskCount : 0,
      };
    })
    .sort((a, b) => b.doneStoryPoints - a.doneStoryPoints);

  const getPerformanceLevel = (doneStoryPoints: number) => {
    if (doneStoryPoints >= 50)
      return {
        level: "Highest",
        color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
      };
    if (doneStoryPoints >= 30)
      return {
        level: "High",
        color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      };
    if (doneStoryPoints >= 20)
      return {
        level: "Medium",
        color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      };
    if (doneStoryPoints >= 10)
      return {
        level: "Low",
        color: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
      };
    return {
      level: "Lowest",
      color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    };
  };

  const getActivityPerformanceLevel = (totalHours: number) => {
    if (totalHours >= 80)
      return {
        level: "Highest",
        color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
      };
    if (totalHours >= 60)
      return {
        level: "High",
        color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      };
    if (totalHours >= 30)
      return {
        level: "Medium",
        color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      };
    if (totalHours >= 10)
      return {
        level: "Low",
        color: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
      };
    return {
      level: "Lowest",
      color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    };
  };

  return (
    <div className="space-y-6">
      {/* Sprint Filter */}
      <SprintFilter
        sprints={sprintQuery.data?.values || []}
        loading={sprintQuery.isLoading}
        onSprintSelect={handleSprintSelect}
        filteredSprints={selectedSprints}
      />

      {/* Show loading state for task data */}
      {taskQuery.isLoading ? (
        <div className="flex items-center justify-center py-20">
          <LPLoading />
        </div>
      ) : hasManualSelection && selectedSprints.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-20 text-center">
          <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-4 mb-4">
            <User className="h-8 w-8 text-gray-400 dark:text-gray-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">No Sprints Selected</h3>
          <p className="text-gray-500 dark:text-gray-400 max-w-sm">
            Please select at least one sprint from the filter above to view team member and task data.
          </p>
        </div>
      ) : (
        <>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="h-5 w-5 mr-2" />
                Team Members
              </CardTitle>
            </CardHeader>
            <CardContent>
              {sortedEngineers.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-4 mb-4">
                    <User className="h-8 w-8 text-gray-400 dark:text-gray-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">No Team Members Found</h3>
                  <p className="text-gray-500 dark:text-gray-400 max-w-sm">
                    No team member data is available for the selected date range. Try adjusting your date filter or
                    check if tasks exist.
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {sortedEngineers.map((engineer) => {
                    const performance = getPerformanceLevel(engineer.doneStoryPoints);
                    const activityPerformance = getActivityPerformanceLevel(engineer.totalHours);

                    return (
                      <Card key={engineer.name}>
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center space-x-3">
                              <Avatar>
                                <AvatarFallback className="bg-primary text-primary-foreground tracking-wide flex items-center justify-center">
                                  {(() => {
                                    const initials = engineer.name
                                      .split(" ")
                                      .map((n) => n[0])
                                      .join("")
                                      .toUpperCase();
                                    // If initials are 3+ letters, only show first 2
                                    return initials.length > 2 ? initials.slice(0, 2) : initials;
                                  })()}
                                </AvatarFallback>
                              </Avatar>
                              <div className="space-x-2">
                                <h3 className="font-semibold text-sm">{engineer.name}</h3>
                                <Badge className={performance.color} variant="secondary">
                                  {performance.level} SP
                                </Badge>
                                <Badge className={activityPerformance.color} variant="secondary">
                                  {activityPerformance.level} Activity
                                </Badge>
                              </div>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center font-medium">
                                <TrendingUp className="h-3 w-3 mr-1" />
                                Done Story Points
                              </span>
                              <span className="font-bold text-base text-green-600">{engineer.doneStoryPoints} SP</span>
                            </div>

                            <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center">
                                <ClipboardList className="h-3 w-3 mr-1" />
                                Total Story Points
                              </span>
                              <span className="font-medium">{engineer.storyPoints}</span>
                            </div>

                            <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Tasks (Done/Total)
                              </span>
                              <span className="font-medium">
                                {engineer.doneTaskCount}/{engineer.taskCount}
                              </span>
                            </div>

                            <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center">
                                <TrendingUp className="h-3 w-3 mr-1" />
                                SP to Hours
                              </span>
                              <span className="font-medium">{formatDecimalHours(engineer.doneStoryPoints * 1.2)}</span>
                            </div>

                            <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center">
                                <Clock className="h-3 w-3 mr-1" />
                                Total Hours
                              </span>
                              <span className="font-medium">{formatDecimalHours(engineer.totalHours)}</span>
                            </div>

                            {/* Sprint Hours Breakdown */}
                            {Object.keys(engineer.sprintHours || {}).length > 0 && (
                              <div className="mt-3 pt-2 border-t border-gray-200 dark:border-gray-700">
                                <div className="text-xs text-gray-500 dark:text-gray-400 mb-2 font-medium">
                                  Sprint Hours:
                                </div>
                                <div className="space-y-1">
                                  {Object.entries(engineer.sprintHours).map(([sprintId, sprintData]) => (
                                    <div key={sprintId} className="flex items-center justify-between text-xs">
                                      <span className="text-gray-600 dark:text-gray-300 truncate flex-1 mr-2">
                                        {sprintData.sprintName || `Sprint ${sprintId}`}
                                      </span>
                                      <span className="font-medium text-blue-600 dark:text-blue-400">
                                        {formatDecimalHours(sprintData.hours)}
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Task Table */}
          {taskQuery.data && <TaskTable data={taskQuery.data.tasks} />}
        </>
      )}
    </div>
  );
}
