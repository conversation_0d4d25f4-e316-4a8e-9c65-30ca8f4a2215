"use client";

import { useSuspenseQuery } from "@tanstack/react-query";
import axios from "axios";
import { Button } from "@/components/ui/button";
import {
    ArrowLeft,
} from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { RootState } from "@/app/store";
import { useSelector } from "react-redux";
import { format} from "date-fns";
import React, { useEffect } from "react";
import { API_BASE_URL } from "@/constants";
import {WorkLogTable} from "@/components/work-log-table";
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card";
import {Badge} from "@/components/ui/badge";
import {DataTable} from "@/components/ui/data-table";
import {sdetIssueColumns} from "@/components/performance-analysis/engineer-issue-columns";
import {IssueWorklogs} from "@/app/pe/performance-analysis/detail/issue-work-logs";
const sdetTeamMembers = {
    "712020:b0f4204d-cf46-477e-b09e-056718804914": "Naufal Athallah Iwel",
    "712020:66f39c08-1d2b-43db-856a-f15b448dc986": "Muhammad Zaenal Arifin",
    "712020:f75eae69-9502-4115-8613-d359b0d6611e": "Hanif Afianto DN",
    "712020:cf7bd17f-d166-4fcc-a70e-c709a201235e": "Adit Triyadi",
    "712020:59e84dde-4969-411e-8938-74c3a95144bf": "Septian Ega",
    "712020:52427388-9068-45a2-8d15-6a9221932e29": "andri.sukma",
    "5b62b8084a8e796a6c40f4af": "ayuanriyani",
};

export function SdetDetailView() {
  const router = useRouter();
  const params = useSearchParams();
  const userID = params.get("user_id") || "";

  useEffect(() => {
    if (!userID) {
      router.push("/sdet/performance-analysis");
    }
  }, [userID, router]);

  const { startDate, endDate } = useSelector(
    (state: RootState) => state.dateFilter
  );
  const jql = `worklogAuthor IN (${userID}) and worklogDate >= %s and worklogDate <= %s`;

    const resultQuery = useSuspenseQuery({
        queryKey: [`detail-sdet-${userID}`, startDate, endDate],
        queryFn: async () => {
            const reportReq = axios.get(API_BASE_URL + `/api/jira/report`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    start_date: startDate
                        ? format(new Date(startDate), "yyyy-MM-dd")
                        : undefined,
                    end_date: endDate
                        ? format(new Date(endDate), "yyyy-MM-dd")
                        : undefined,
                    jql,
                },
            });
            const formattedStartDate = startDate ? format(new Date(startDate), "yyyy-MM-dd") : "";

            const formattedEndDate = endDate ? format(new Date(endDate), "yyyy-MM-dd") : "";
            const taskJqlSdet = `assignee = ${userID} AND project = GQA AND Type = "Task" AND "Type Task[Dropdown]" IN ("Project Pareto", "Maintenance", "On Going Development") AND status CHANGED TO "Done" DURING ("${formattedStartDate}", "${formattedEndDate}")`;
            const resSdetReq = axios.get(API_BASE_URL + `/api/jira/task`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: taskJqlSdet,
                },
            });

            const pendingTaskJqlSdet = `assignee = ${userID} AND project = GQA AND Type = "Task" AND "Type Task[Dropdown]" IN ("Project Pareto", "Maintenance", "On Going Development") AND status != "Done" AND status CHANGED DURING ("${formattedStartDate}", "${formattedEndDate}")`;
            const pendingResSdetReq = axios.get(API_BASE_URL + `/api/jira/task`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: pendingTaskJqlSdet,
                },
            });

            const completedCarryOverTaskJqlSdet = `assignee = ${userID} AND project = GQA AND Type = "Task" AND "Type Task[Dropdown]" IN ("Project Pareto", "Maintenance", "On Going Development") AND status CHANGED before "${formattedStartDate}" AND status CHANGED TO "Done" DURING ("${formattedStartDate}", "${formattedEndDate}")`;
            const completedCarryOverTaskSdetReq = axios.get(API_BASE_URL + `/api/jira/task`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: completedCarryOverTaskJqlSdet,
                },
            });


            const pendingCarryOverTaskJqlSdet = `assignee = ${userID} AND project = GQA AND Type = "Task" AND "Type Task[Dropdown]" IN ("Project Pareto", "Maintenance", "On Going Development") AND status != "Done" AND status CHANGED before "${formattedStartDate}"`;
            const pendingCarryOverTaskSdetReq =  axios.get(API_BASE_URL + `/api/jira/task`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: pendingCarryOverTaskJqlSdet,
                },
            });

            const [
                res,
                resSdet,
                pendingResSdet,
                completedCarryOverTaskSdet,
                pendingCarryOverTaskSdet,
            ] = await Promise.all([
                reportReq,
                resSdetReq,
                pendingResSdetReq,
                completedCarryOverTaskSdetReq,
                pendingCarryOverTaskSdetReq,
            ]);

            const excludeCompletedCarryOver = new Set(completedCarryOverTaskSdet.data.tasks.map((task) => task.task_link));
            const completedThisWeek = resSdet.data.tasks.filter((task) => !excludeCompletedCarryOver.has(task.task_link));
            return {
                dataWorklog: res.data.issues.filter((e) => !e.name.includes("Bian") && !e.name.includes("andrean") && !e.name.includes("Adam Nasrudin") && !e.name.includes("Tedja")),
                dataProject: completedThisWeek.filter((task) => task.type_task === "Project Pareto"),
                dataOnGoingDev: completedThisWeek.filter((task) => task.type_task === "On Going Development"),
                dataMaintenance: completedThisWeek.filter((task) => task.type_task === "Maintenance"),
                dataPending: pendingResSdet.data.tasks,
                dataCarryOverPending: pendingCarryOverTaskSdet.data.tasks,
                dataCarryOverCompleted: completedCarryOverTaskSdet.data.tasks
            };
        },
    });
  const dataWorklog = resultQuery.data.dataWorklog;
  const dataCompletedTask = [...resultQuery.data.dataProject, ...resultQuery.data.dataOnGoingDev, ...resultQuery.data.dataMaintenance];
  const dataPendingTask = [...resultQuery.data.dataPending];
  const dataCarryOvertask = [...resultQuery.data.dataCarryOverPending, ...resultQuery.data.dataCarryOverCompleted];

  return (
    <>
      {/* Header */}
      <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between px-2 sm:px-0">
        <div className="flex items-center gap-2 sm:gap-4 w-full">
          <Button
            variant="ghost"
            size="sm"
            className="w-fit"
            onClick={() => router.push("/sdet/performance-analysis?tab=members")}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div className="flex-1 min-w-0 w-full">
            <h1 className="text-xl sm:text-2xl font-bold capitalize text-ellipsis">
                {sdetTeamMembers[userID]}
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Individual Performance Analysis
            </p>
          </div>
        </div>
      </div>
        <Card className="border-2">
            <CardHeader>
                <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
                    <div className="flex-1 min-w-0">
                        <div className="flex flex-wrap items-center gap-2 mb-2 relative">
                            <CardTitle
                                className={`text-base sm:text-lg px-2 py-1 w-fit border-l-6 font-semibold border-violet-600 dark:border-violet-400`}
                            >
                                Carry Over SDET Tasks
                            </CardTitle>
                        </div>
                        <div className="flex flex-col gap-2 text-xs sm:text-sm text-muted-foreground">
                            <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between w-full">
                                <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4 flex-1 min-w-0">
                                    <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                Issues:
                              </span>
                                        <Badge
                                            variant="outline"
                                            className="px-2 py-0.5 text-xs"
                                        >
                                            {dataCarryOvertask.length}
                                        </Badge>
                                    </div>
                                    <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                Total SP:
                              </span>
                                        <Badge
                                            variant="outline"
                                            className="px-2 py-0.5 text-xs"
                                        >
                                            {dataCarryOvertask.reduce((total, task) => { return total + task.sp}, 0)}
                                        </Badge>
                                    </div>
                                    <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                Done SP:
                              </span>
                                        <Badge
                                            variant="outline"
                                            className="px-2 py-0.5 text-xs"
                                        >
                                            {dataCarryOvertask.filter((task) => task.status === "Done").reduce((total, task) => { return total + task.sp}, 0)}
                                        </Badge>
                                    </div>
                                    <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                Pending SP:
                              </span>
                                        <Badge
                                            variant="outline"
                                            className="px-2 py-0.5 text-xs"
                                        >
                                            {dataCarryOvertask.filter((task) => task.status !== "Done").reduce((total, task) => { return total + task.sp}, 0)}
                                        </Badge>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                <DataTable
                    columns={sdetIssueColumns}
                    data={dataCarryOvertask}
                    initialSort={[{ id: "status", desc: true }]}
                    renderExpandedRow={(row) => {
                        return <IssueWorklogs worklogs={row.original.worklogs} />;
                    }}
                />
            </CardContent>
        </Card>
        <Card className="border-2">
            <CardHeader>
                <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
                    <div className="flex-1 min-w-0">
                        <div className="flex flex-wrap items-center gap-2 mb-2 relative">
                            <CardTitle
                                className={`text-base sm:text-lg px-2 py-1 w-fit border-l-6 font-semibold border-violet-600 dark:border-violet-400`}
                            >
                                SDET Tasks This Week
                            </CardTitle>
                        </div>
                        <div className="flex flex-col gap-2 text-xs sm:text-sm text-muted-foreground">
                            <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between w-full">
                                <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4 flex-1 min-w-0">
                                    <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                Issues:
                              </span>
                                        <Badge
                                            variant="outline"
                                            className="px-2 py-0.5 text-xs"
                                        >
                                            {[...dataCompletedTask, ...dataPendingTask].length}
                                        </Badge>
                                    </div>
                                    <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                Total SP:
                              </span>
                                        <Badge
                                            variant="outline"
                                            className="px-2 py-0.5 text-xs"
                                        >
                                            {[...dataCompletedTask, ...dataPendingTask].reduce((total, task) => { return total + task.sp}, 0)}
                                        </Badge>
                                    </div>
                                    <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                Done SP:
                              </span>
                                        <Badge
                                            variant="outline"
                                            className="px-2 py-0.5 text-xs"
                                        >
                                            {dataCompletedTask.reduce((total, task) => { return total + task.sp}, 0)}
                                        </Badge>
                                    </div>
                                    <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                Pending SP:
                              </span>
                                        <Badge
                                            variant="outline"
                                            className="px-2 py-0.5 text-xs"
                                        >
                                            {dataPendingTask.reduce((total, task) => { return total + task.sp}, 0)}
                                        </Badge>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                <DataTable
                    columns={sdetIssueColumns}
                    data={[...dataCompletedTask, ...dataPendingTask]}
                    initialSort={[{ id: "status", desc: true }]}
                    renderExpandedRow={(row) => {
                        return <IssueWorklogs worklogs={row.original.worklogs} />;
                    }}
                />
            </CardContent>
        </Card>
      <WorkLogTable data={dataWorklog} key="1" />
    </>
  );
}
