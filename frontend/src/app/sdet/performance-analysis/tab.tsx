"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, TabsContent } from "@/components/ui/tabs";
import { LPLoading } from "@/components/lp-loading";
import { Suspense, useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import UnderConstructionPage from "./under-construction";
import { WeeklyOverview } from "./weekly-overview";
import {TeamSdetOverview} from "@/app/sdet/performance-analysis/team-sdet-overview";

export function Tab() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState("");

  // Load tab from URL on mount
  useEffect(() => {
    const tabFromUrl = searchParams.get("tab");
    if (tabFromUrl && ["members", "report", "bugs", "sprint"].includes(tabFromUrl)) {
      setActiveTab(tabFromUrl);
    } else {
        setActiveTab("members");
    }
  }, [searchParams]);

  // Handle tab change and update URL
  const handleTabChange = (value: string) => {
    setActiveTab(value);

    // Update URL without triggering navigation
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("tab", value);

    // Replace current URL with new tab parameter
    router.replace(`?${newSearchParams.toString()}`, { scroll: false });
  };

  return (
    <div className="space-y-6">
      {/* Tabs for team views */}
      <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
        <TabsList className="grid w-full md:w-2/3 grid-cols-4 mb-4">
          <TabsTrigger value="members">Team Members</TabsTrigger>
          <TabsTrigger value="report">Report</TabsTrigger>
          <TabsTrigger value="bugs">Bugs</TabsTrigger>
          <TabsTrigger value="sprint">Sprint</TabsTrigger>
        </TabsList>

        <TabsContent value="members" className="space-y-4">
          <Suspense
              fallback={
                <div className="flex items-center justify-center h-60">
                  <LPLoading />
                </div>
              }
          >
            <TeamSdetOverview />
          </Suspense>
        </TabsContent>

        <TabsContent value="report" className="space-y-4">
          <Suspense
            fallback={
              <div className="flex items-center justify-center h-60">
                <LPLoading />
              </div>
            }
          >
            <WeeklyOverview />
          </Suspense>
        </TabsContent>

        <TabsContent value="bugs" className="space-y-4">
          <UnderConstructionPage />
        </TabsContent>

        <TabsContent value="sprint" className="space-y-4">
          <Suspense
            fallback={
              <div className="flex items-center justify-center h-60">
                <LPLoading />
              </div>
            }
          >
            <UnderConstructionPage />
          </Suspense>
        </TabsContent>
      </Tabs>
    </div>
  );
}
