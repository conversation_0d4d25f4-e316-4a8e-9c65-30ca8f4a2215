"use client";

import { Construction, Clock10 } from "lucide-react";

export default function UnderConstructionPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-zinc-900 dark:to-zinc-800 px-6">
      <div
        className="bg-white dark:bg-zinc-900 dark:text-white shadow-lg rounded-2xl p-10 text-center space-y-6 max-w-md w-full"
        style={{
          animation: "fadeIn 0.5s ease-out forwards",
        }}
      >
        <style>
          {`@keyframes fadeIn {
              from { opacity: 0; transform: translateY(20px); }
              to { opacity: 1; transform: translateY(0); }
          }`}
        </style>

        <div className="flex justify-center text-yellow-500 dark:text-yellow-400">
          <Construction className="w-16 h-16" />
        </div>

        <h1 className="text-2xl font-semibold text-gray-800 dark:text-white">
          Page Under Construction
        </h1>

        <p className="text-gray-600 dark:text-gray-300">
          We're working hard to finish the development of this page. Please check back later.
        </p>

        <div className="flex items-center justify-center text-gray-400 dark:text-gray-400 text-sm gap-2">
          <Clock10 className="w-4 h-4" />
          <span>Last updated: {new Date().toLocaleDateString()}</span>
        </div>
      </div>
    </div>
  );
}
