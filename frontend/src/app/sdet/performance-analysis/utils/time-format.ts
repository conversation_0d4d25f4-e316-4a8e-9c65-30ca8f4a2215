/**
 * SDET-specific utility functions for time formatting
 */

/**
 * Convert decimal hours to human-readable format (e.g., 2.7 → "2h 42m")
 * @param hours number of hours in decimal format
 */
export function formatDecimalHours(hours: number): string {
  if (hours === 0) return "0m";

  const wholeHours = Math.floor(hours);
  const minutes = Math.round((hours - wholeHours) * 60);

  if (wholeHours === 0) {
    return `${minutes}m`;
  } else if (minutes === 0) {
    return `${wholeHours}h`;
  } else {
    return `${wholeHours}h ${minutes}m`;
  }
}
