"use client";
import {<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>} from "@/components/ui/card";
import React from "react";
import {SomethingWentWrong} from "@/components/something-went-wrong";
import {useSuspenseQuery} from "@tanstack/react-query";
import axios from "axios";
import {API_BASE_URL} from "@/constants";
import {
    paretoEpicColumns,
    paretoTaskColumns,
} from "@/components/performance-analysis/engineer-issue-columns";
import {DataTable} from "@/components/ui/data-table";
import {IssueWorklogs} from "@/app/sdet/performance-analysis/components/issue-work-logs";
import {MetricCards} from "@/components/metric-cards";
import {countWeekdaysExcludingHolidaysForSDET, SDETMembers} from "@/lib/utils";
import {Target} from "lucide-react";
import {format} from "date-fns";


export function WeeklyOverview() {
    const { data, isError } = useSuspenseQuery({
        queryKey: ["report"],
        queryFn: async () => {
            // const formattedStartDate = startDate ? format(new Date(startDate), "yyyy-MM-dd") : "";
            // const formattedEndDate = endDate ? format(new Date(endDate), "yyyy-MM-dd") : "";
            const jqlEpicPareto = `project = GQA AND Type = "Epic" AND "Type Task[Dropdown]" IN ("Project Pareto")`;
            const reqEpicPareto = axios.get(API_BASE_URL + `/api/jira/task`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: jqlEpicPareto,
                },
            });

            const jqlTaskEpicPareto = `project = GQA AND Type = "Task" AND "Type Task[Dropdown]" IN ("Project Pareto")`;
            const reqTaskEpicPareto = axios.get(API_BASE_URL + `/api/jira/task`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: jqlTaskEpicPareto,
                },
            });

            const jqlRegression = `project = GQA AND Type = "Regression"`;
            const reqRegression = axios.get(API_BASE_URL + `/api/jira/task`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: jqlRegression,
                },
            });

            const [
                resEpicPareto,
                resTaskEpicPareto,
                resRegression
            ] =  await Promise.all([
                reqEpicPareto,
                reqTaskEpicPareto,
                reqRegression
            ]);

            return {
                dataPareto: resEpicPareto.data.tasks.map((epic) => {
                    return {
                        ...epic,
                        issues: resTaskEpicPareto.data.tasks.filter((task) => task.parent_link === epic.task_link)
                    }
                }),
                dataRegression: resRegression.data.tasks
            };
        },
    });

    const totalTasks = data.dataPareto.reduce((total, epic) => {
        return total + epic.issues.length;
    }, 0);
    const totalTasksDone = data.dataPareto.reduce((total, epic) => {
        return total + epic.issues.filter((issue) => issue.status === "Done").length;
    }, 0);
    const totalStoryPoint = data.dataPareto.reduce((total, epic) => {
        return total + epic.issues.reduce((totalPerEpic, issue) => {
            return totalPerEpic + issue.sp;
        }, 0)
    }, 0);
    const totalStoryPointDone = data.dataPareto.reduce((total, epic) => {
        return total + epic.issues.filter((issue) => issue.status === "Done").reduce((totalPerEpic, issue) => {
            return totalPerEpic + issue.sp;
        }, 0)
    }, 0);
    const totalHour = data.dataPareto.reduce((total, epic) => {
        return total + epic.time_spent_hours
    }, 0);

    if (isError) {
        return (
            <SomethingWentWrong />
        );
    }

    const metricsSummary = [
        {
            title: "Total TC / Story Point Pareto",
            value: `${totalTasks} TC (${totalStoryPoint} SP)`,
            description: "Total TC / SP",
            icon: Target,
            iconColor: "text-purple-600 dark:text-purple-400",
        },
        {
            title: "Done TC / Story Point Pareto",
            value: `${totalTasksDone} TC (${totalStoryPointDone}  SP)`,
            description: "Tota TC / SP Done",
            icon: Target,
            iconColor: "text-blue-600 dark:text-blue-400",
        },
        {
            title: "Progress Pareto",
            value: `${(totalStoryPointDone / totalStoryPoint * 100).toFixed(2)} %`,
            description: "Percentage Progress Pareto",
            icon: Target,
            iconColor: "text-purple-600 dark:text-purple-400",
        }
    ];
    const targetDoneProjectPareto = "2025-10-03";
    const remainingDays = countWeekdaysExcludingHolidaysForSDET(format(new Date(targetDoneProjectPareto), "yyyy-MM-dd"), format(new Date(), "yyyy-MM-dd"));
    const projectParetoLoad = Math.ceil((totalStoryPoint - totalStoryPointDone) / remainingDays);
    const availableLoadSdetPerDay = (0.75 * (Object.keys(SDETMembers).length - 1) * 5);
    const metricsDeadline = [
        {
            title: "Remaining Days",
            value: `${remainingDays} Days`,
            description: "Deadline at 3 October 2025",
            icon: Target,
            iconColor: "text-purple-600 dark:text-purple-400",
        },
        {
            title: "Need Story Point / Day",
            value: `${projectParetoLoad}  SP`,
            description: "SP / Day if want to achieve deadline",
            icon: Target,
            iconColor: "text-blue-600 dark:text-blue-400",
            isWarning: projectParetoLoad >= availableLoadSdetPerDay,
        },
        {
            title: "SP / Hour Pareto",
            value: `${(totalStoryPointDone / totalHour).toFixed(2)} SP / Hour`,
            description: "Speed AT Development",
            icon: Target,
            iconColor: "text-blue-600 dark:text-blue-400",
        }
    ];

    const regressions = data.dataRegression;
    return (
        <div className="space-y-6">
            <MetricCards metrics={metricsSummary} columns={3} section={"Summary"} />
            <MetricCards metrics={metricsDeadline} columns={3} section={"Deadline"} />
            <Card>
                <CardHeader>
                    <CardTitle>
                        Project Pareto
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <DataTable
                        columns={paretoEpicColumns}
                        data={data.dataPareto}
                        initialSort={[{ id: "sp", desc: true }]}
                        renderExpandedRow={(row) => {
                            return <div className="py-2 px-4">
                                <DataTable
                                    columns={paretoTaskColumns}
                                    data={row.original.issues}
                                    initialSort={[{ id: "sp", desc: true }]}
                                />
                            </div>;
                        }}
                    />
                </CardContent>
            </Card>
            <Card>
                <CardHeader>
                    <CardTitle>
                        Regressions
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <DataTable
                        columns={paretoTaskColumns}
                        data={regressions}
                        renderExpandedRow={(row) => {
                            return <IssueWorklogs worklogs={row.original.worklogs} />;
                        }}
                    />
                </CardContent>
            </Card>
        </div>
    );
}
