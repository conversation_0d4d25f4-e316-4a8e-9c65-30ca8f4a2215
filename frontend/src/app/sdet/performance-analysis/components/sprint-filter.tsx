import React, { useState, useMemo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Filter, X, CheckCircle2 } from "lucide-react";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format, differenceInDays } from "date-fns";

interface Sprint {
  id: number;
  name: string;
  state: string;
  startDate?: string;
  endDate?: string;
  originBoardId?: number;
  // Alias untuk compatibility dengan format API berbeda
  start_date?: string;
  end_date?: string;
  complete_date?: string;
  board_id?: number;
  goal?: string;
  boardId?: number;
  completeDate?: string;
}

interface SprintFilterProps {
  sprints: Sprint[];
  loading: boolean;
  onSprintSelect?: (sprints: Sprint[]) => void;
  filteredSprints?: Sprint[];
}

export function SprintFilter({ sprints, loading, onSprintSelect, filteredSprints = [] }: SprintFilterProps) {
  const [selectedSprintIds, setSelectedSprintIds] = useState<number[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isOpen, setIsOpen] = useState(false);

  // Sync selectedSprintIds with filteredSprints from parent
  React.useEffect(() => {
    const newSelectedIds = filteredSprints.map((sprint) => sprint.id);
    if (JSON.stringify(newSelectedIds.sort()) !== JSON.stringify(selectedSprintIds.sort())) {
      setSelectedSprintIds(newSelectedIds);
    }
  }, [filteredSprints, selectedSprintIds]);

  // Utility functions for sprint information
  const formatSprintDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    try {
      return format(new Date(dateString), "MMM dd, yyyy");
    } catch {
      return "Invalid Date";
    }
  };

  const getDaysRemaining = (sprint: Sprint) => {
    const endDate = sprint.endDate || sprint.end_date;
    if (!endDate) return null;
    try {
      const end = new Date(endDate);
      const now = new Date();
      const days = differenceInDays(end, now);
      return days;
    } catch {
      return null;
    }
  };

  const getStateColor = (state: string) => {
    switch (state.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "future":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "closed":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  // Filter and sort sprints for better UX
  const sortedSprints = sprints
    .filter((sprint) => {
      const hasStartDate = sprint.startDate || sprint.start_date;
      const hasEndDate = sprint.endDate || sprint.end_date;
      return hasStartDate && hasEndDate;
    }) // Only sprints with dates
    .sort((a, b) => {
      // Sort by state priority (active > future > closed) then by start date
      const statePriority = { active: 3, future: 2, closed: 1 };
      const aPriority = statePriority[a.state as keyof typeof statePriority] || 0;
      const bPriority = statePriority[b.state as keyof typeof statePriority] || 0;

      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }

      // If same priority, sort by start date (newest first)
      const aStartDate = a.startDate || a.start_date || "";
      const bStartDate = b.startDate || b.start_date || "";
      return new Date(bStartDate).getTime() - new Date(aStartDate).getTime();
    });

  // Filter sprints based on search term
  const filteredSprintsForSearch = useMemo(() => {
    if (!searchTerm) return sortedSprints;
    return sortedSprints.filter(
      (sprint) =>
        sprint.name.toLowerCase().includes(searchTerm.toLowerCase()) || sprint.id.toString().includes(searchTerm)
    );
  }, [sortedSprints, searchTerm]);

  // Get selected sprints objects
  const selectedSprints = sprints.filter((sprint) => selectedSprintIds.includes(sprint.id));

  const handleSprintToggle = (sprintId: number) => {
    const newSelectedIds = selectedSprintIds.includes(sprintId)
      ? selectedSprintIds.filter((id) => id !== sprintId)
      : [...selectedSprintIds, sprintId];

    setSelectedSprintIds(newSelectedIds);

    const selectedSprintsObjects = sprints.filter((sprint) => newSelectedIds.includes(sprint.id));
    onSprintSelect?.(selectedSprintsObjects);
  };

  const handleRemoveSprint = (sprintId: number) => {
    const newSelectedIds = selectedSprintIds.filter((id) => id !== sprintId);
    setSelectedSprintIds(newSelectedIds);

    const selectedSprintsObjects = sprints.filter((sprint) => newSelectedIds.includes(sprint.id));
    onSprintSelect?.(selectedSprintsObjects);
  };

  const clearAllSprints = () => {
    setSelectedSprintIds([]);
    onSprintSelect?.([]);
  };

  const formatSprintLabel = (sprint: Sprint) => {
    const formatDate = (dateString: string) => {
      try {
        return format(new Date(dateString), "MMM dd");
      } catch {
        return "Invalid";
      }
    };

    const startDate = sprint.startDate || sprint.start_date;
    const endDate = sprint.endDate || sprint.end_date;
    const dateRange = startDate && endDate ? `${formatDate(startDate)} - ${formatDate(endDate)}` : "No dates";

    return `${sprint.name} (${dateRange})`;
  };

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex flex-col gap-4">
          {/* Sprint Multi-Selector */}
          <div className="flex gap-3 items-end max-w-2xl">
            <div className="flex-1 min-w-0">
              <Popover open={isOpen} onOpenChange={setIsOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={isOpen}
                    className="w-full justify-between min-w-[300px]"
                    disabled={loading}
                  >
                    {loading
                      ? "Loading sprints..."
                      : selectedSprintIds.length === 0
                      ? "Select sprints..."
                      : `${selectedSprintIds.length} sprint(s) selected`}
                    <CheckCircle2 className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-[400px] p-0" align="start">
                  <Command>
                    <CommandInput placeholder="Search sprints..." value={searchTerm} onValueChange={setSearchTerm} />
                    <CommandEmpty>No sprints found.</CommandEmpty>
                    <CommandGroup>
                      <CommandList className="max-h-[300px]">
                        {filteredSprintsForSearch.map((sprint) => (
                          <CommandItem
                            key={sprint.id}
                            value={sprint.name}
                            onSelect={() => handleSprintToggle(sprint.id)}
                            className="flex items-center space-x-2 cursor-pointer"
                          >
                            <Checkbox
                              checked={selectedSprintIds.includes(sprint.id)}
                              onChange={() => handleSprintToggle(sprint.id)}
                            />
                            <div className="flex-1 flex flex-col">
                              <span className="font-medium">{formatSprintLabel(sprint)}</span>
                              {sprint.goal && (
                                <span className="text-xs text-gray-500 dark:text-gray-400 mt-0.5 truncate">
                                  {sprint.goal}
                                </span>
                              )}
                            </div>
                            <Badge className={getStateColor(sprint.state)} variant="secondary">
                              {sprint.state}
                            </Badge>
                          </CommandItem>
                        ))}
                      </CommandList>
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>

            {/* Clear All Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={clearAllSprints}
              className="whitespace-nowrap"
              disabled={selectedSprintIds.length === 0}
            >
              <Filter className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          </div>

          {/* Selected Sprints Tags */}
          {selectedSprints.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {selectedSprints.map((sprint) => (
                <Badge key={sprint.id} variant="outline" className="flex items-center gap-2 pr-1 pl-3 py-1">
                  <span className="text-sm">{sprint.name}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 hover:bg-red-100 hover:text-red-600"
                    onClick={() => handleRemoveSprint(sprint.id)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}
            </div>
          )}
        </div>

        {/* Sprint Details Section - Compact Layout */}
        {filteredSprints.length > 0 && (
          <div className="mt-4">
            <h4 className="font-semibold text-sm text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
              Sprint Information ({filteredSprints.length})
            </h4>
            <div className="space-y-2">
              {filteredSprints.map((sprint) => {
                const daysRemaining = getDaysRemaining(sprint);
                return (
                  <div
                    key={sprint.id}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-900 transition-colors"
                  >
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      <Badge className={getStateColor(sprint.state)} variant="secondary">
                        {sprint.state}
                      </Badge>
                      <div className="min-w-0 flex-1">
                        <h5 className="font-medium text-sm truncate" title={sprint.name}>
                          {sprint.name}
                        </h5>
                        <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400 mt-1">
                          <span>ID: {sprint.id}</span>
                          {(sprint.startDate || sprint.start_date) && (sprint.endDate || sprint.end_date) && (
                            <>
                              <span>•</span>
                              <span>
                                {formatSprintDate(sprint.startDate || sprint.start_date)} -{" "}
                                {formatSprintDate(sprint.endDate || sprint.end_date)}
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    {daysRemaining !== null && (
                      <span
                        className={`px-2 py-1 rounded text-xs font-medium whitespace-nowrap ${
                          daysRemaining > 0
                            ? "bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300"
                            : daysRemaining === 0
                            ? "bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300"
                            : "bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300"
                        }`}
                      >
                        {daysRemaining > 0
                          ? `${daysRemaining}d left`
                          : daysRemaining === 0
                          ? "Today"
                          : `${Math.abs(daysRemaining)}d ago`}
                      </span>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
