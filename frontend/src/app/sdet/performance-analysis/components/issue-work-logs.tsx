import * as React from "react";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Clock, Calendar, FileText } from "lucide-react";
import { getActivityColor } from "@/lib/utils";

export function IssueWorklogs({ worklogs }: { worklogs: any[] }) {
  if (!worklogs || worklogs.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-center">
        <div className="rounded-full bg-muted/50 p-3 mb-3">
          <FileText className="h-6 w-6 text-muted-foreground" />
        </div>
        <h3 className="text-base font-medium text-foreground mb-1">No work logs available</h3>
        <p className="text-xs text-muted-foreground">Work logs will appear here once they are created.</p>
      </div>
    );
  }

  return (
    <div className="space-y-0">
      {worklogs.map((log, idx) => (
        <React.Fragment key={idx}>
          <div className="flex items-center gap-3 p-3 hover:bg-muted/50 transition-colors">
            {/* User and Type Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-medium text-sm text-foreground truncate">{log.name || "Unknown User"}</span>
                {log.type && (
                  <Badge className={`${getActivityColor(log.type)} text-xs shrink-0`} variant="secondary">
                    {log.type}
                  </Badge>
                )}
              </div>

              {log.description && (
                <p className="text-sm text-muted-foreground line-clamp-2" title={log.description}>
                  {log.description}
                </p>
              )}
            </div>

            {/* Time and Date Info */}
            <div className="flex items-center gap-3 text-xs text-muted-foreground shrink-0">
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span className="font-medium">{log.time_spent_human || "0h"}</span>
              </div>

              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span className="whitespace-nowrap">
                  {log.created_at
                    ? new Date(log.created_at).toLocaleDateString("en-US", {
                        month: "short",
                        day: "numeric",
                        hour: "2-digit",
                        minute: "2-digit",
                      })
                    : "No date"}
                </span>
              </div>
            </div>
          </div>

          {/* Separator between items, but not after the last item */}
          {idx < worklogs.length - 1 && <Separator />}
        </React.Fragment>
      ))}
    </div>
  );
}
