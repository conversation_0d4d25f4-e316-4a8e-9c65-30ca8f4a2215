"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import { taskColumns, Task } from "./task-columns";
import React from "react";
import { IssueWorklogs } from "./issue-work-logs";

interface TaskTableProps {
  data: Task[];
}

export function TaskTable({ data }: TaskTableProps) {
  const initialSort = [
    {
      id: "updated",
      desc: true,
    },
  ];

  const assigneeOptions = React.useMemo(() => {
    const assignees = Array.from(new Set((Array.isArray(data) ? data : []).map((task: Task) => task?.assignee ?? "")));
    return assignees
      .filter(Boolean)
      .sort((a, b) => a.localeCompare(b, undefined, { sensitivity: "base" }))
      .map((assignee) => ({ label: assignee, value: assignee }));
  }, [data]);

  const statusOptions = React.useMemo(() => {
    const statuses = Array.from(new Set((Array.isArray(data) ? data : []).map((task: Task) => task?.status ?? "")));
    return statuses
      .filter(Boolean)
      .sort((a, b) => a.localeCompare(b))
      .map((status) => ({
        label: status,
        value: status.trim(), // Normalize value to match filter function
      }));
  }, [data]);

  const priorityOptions = React.useMemo(() => {
    const priorities = Array.from(new Set((Array.isArray(data) ? data : []).map((task: Task) => task?.priority ?? "")));
    return priorities
      .filter(Boolean)
      .sort((a, b) => a.localeCompare(b))
      .map((priority) => ({ label: priority, value: priority }));
  }, [data]);

  const typeOptions = React.useMemo(() => {
    const types = Array.from(new Set((Array.isArray(data) ? data : []).map((task: Task) => task?.type ?? "")));
    return types
      .filter(Boolean)
      .sort((a, b) => a.localeCompare(b))
      .map((type) => ({ label: type, value: type }));
  }, [data]);

  const parentNameOptions = React.useMemo(() => {
    const parentNames = Array.from(
      new Set((Array.isArray(data) ? data : []).map((task: Task) => task?.parent_name ?? ""))
    );
    return parentNames
      .filter(Boolean)
      .sort((a, b) => a.localeCompare(b, undefined, { sensitivity: "base" }))
      .map((parentName) => ({ label: parentName, value: parentName }));
  }, [data]);

  // Add Sprint Name filter options for SDET-specific filtering
  const sprintNameOptions = React.useMemo(() => {
    const sprintNames = Array.from(
      new Set((Array.isArray(data) ? data : []).map((task: Task) => task?.sprintName ?? ""))
    );

    return sprintNames
      .filter(Boolean)
      .sort((a, b) => a.localeCompare(b, undefined, { sensitivity: "base" }))
      .map((sprintName) => ({
        label: sprintName,
        value: sprintName.trim(), // Normalize value
      }));
  }, [data]);

  const filterColumns = [
    {
      id: "assignee",
      placeholder: "Assignee",
      options: assigneeOptions,
    },
    {
      id: "status",
      placeholder: "Status",
      options: statusOptions,
    },
    {
      id: "priority",
      placeholder: "Priority",
      options: priorityOptions,
    },
    {
      id: "type",
      placeholder: "Type",
      options: typeOptions,
    },
    {
      id: "parent_name",
      placeholder: "Parent Name",
      options: parentNameOptions,
    },
    // Add Sprint Name filter for SDET
    ...(sprintNameOptions.length > 1
      ? [
          {
            id: "sprintName",
            placeholder: "Sprint",
            options: sprintNameOptions,
          },
        ]
      : []),
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>SDET Task Details</CardTitle>
      </CardHeader>
      <CardContent>
        <DataTable
          columns={taskColumns}
          data={data || []}
          filterColumns={filterColumns}
          initialSort={initialSort}
          renderExpandedRow={(row) => {
            return <IssueWorklogs worklogs={row.original.worklogs} />;
          }}
        />
      </CardContent>
    </Card>
  );
}
