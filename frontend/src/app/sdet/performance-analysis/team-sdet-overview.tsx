"use client";

import React from "react";
import axios from "axios";
import { RootState } from "@/app/store";
import { useSuspenseQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import {usePathname} from "next/navigation";
import { useSelector } from "react-redux";
import { API_BASE_URL } from "@/constants";
import {SomethingWentWrong} from "@/components/something-went-wrong";
import {TimeDistributionChart} from "@/components/performance-analysis/time-distribution-chart";
import {ActivityChart} from "@/components/performance-analysis/activity-chart";
import {WorkLogTable} from "@/components/work-log-table";
import {DateFilter} from "@/components/date-filter";
import {TeamMembersLeaderboardSdet} from "@/app/sdet/performance-analysis/team-members-leaderboard-sdet";

export function TeamSdetOverview() {
  const pathname = usePathname();
  const role = pathname.split("/")[1] || "sdet";

  const { startDate, endDate } = useSelector(
    (state: RootState) => state.dateFilter
  );
  const jql = "worklogAuthor IN (" +
      "712020:59e84dde-4969-411e-8938-74c3a95144bf, " +
      "712020:52427388-9068-45a2-8d15-6a9221932e29, " +
      "712020:b0f4204d-cf46-477e-b09e-056718804914, " +
      "712020:f75eae69-9502-4115-8613-d359b0d6611e, " +
      "712020:66f39c08-1d2b-43db-856a-f15b448dc986, " +
      "5b62b8084a8e796a6c40f4af, " +
      "712020:cf7bd17f-d166-4fcc-a70e-c709a201235e" +
      ") AND worklogDate >= %s AND worklogDate <= %s";

  const { data, isError } = useSuspenseQuery({
    queryKey: ["worklog", startDate, endDate, role],
    queryFn: async () => {
      const reportReq = axios.get(API_BASE_URL + `/api/jira/report`, {
        headers: {
          Accept: "application/json",
        },
        params: {
          start_date: startDate
            ? format(new Date(startDate), "yyyy-MM-dd")
            : undefined,
          end_date: endDate
            ? format(new Date(endDate), "yyyy-MM-dd")
            : undefined,
          jql,
        },
      });
      const formattedStartDate = startDate ? format(new Date(startDate), "yyyy-MM-dd") : "";

      const formattedEndDate = endDate ? format(new Date(endDate), "yyyy-MM-dd") : "";
      const taskJqlSdet = `project = GQA AND assignee IS NOT EMPTY AND Type = "Task" AND "Type Task[Dropdown]" IN ("Project Pareto", "Maintenance", "On Going Development") AND status CHANGED TO "Done" DURING ("${formattedStartDate}", "${formattedEndDate}")`;
      const resSdetReq = axios.get(API_BASE_URL + `/api/jira/task`, {
        headers: {
            Accept: "application/json",
        },
        params: {
            jql: taskJqlSdet,
        },
      });

        const pendingTaskJqlSdet = `project = GQA AND assignee IS NOT EMPTY AND Type = "Task" AND "Type Task[Dropdown]" IN ("Project Pareto", "Maintenance", "On Going Development") AND status != "Done" AND status CHANGED DURING ("${formattedStartDate}", "${formattedEndDate}")`;
        const pendingResSdetReq = axios.get(API_BASE_URL + `/api/jira/task`, {
            headers: {
                Accept: "application/json",
            },
            params: {
                jql: pendingTaskJqlSdet,
            },
        });

        const completedCarryOverTaskJqlSdet = `project = GQA AND assignee IS NOT EMPTY AND Type = "Task" AND "Type Task[Dropdown]" IN ("Project Pareto", "Maintenance", "On Going Development") AND status CHANGED before "${formattedStartDate}" AND status CHANGED TO "Done" DURING ("${formattedStartDate}", "${formattedEndDate}")`;
        const completedCarryOverTaskSdetReq = axios.get(API_BASE_URL + `/api/jira/task`, {
            headers: {
                Accept: "application/json",
            },
            params: {
                jql: completedCarryOverTaskJqlSdet,
            },
        });


        const pendingCarryOverTaskJqlSdet = `project = GQA AND assignee IS NOT EMPTY AND Type = "Task" AND "Type Task[Dropdown]" IN ("Project Pareto", "Maintenance", "On Going Development") AND status != "Done" AND status CHANGED before "${formattedStartDate}"`;
        const pendingCarryOverTaskSdetReq = axios.get(API_BASE_URL + `/api/jira/task`, {
            headers: {
                Accept: "application/json",
            },
            params: {
                jql: pendingCarryOverTaskJqlSdet,
            },
        });

        const [
            res,
            resSdet,
            pendingResSdet,
            completedCarryOverTaskSdet,
            pendingCarryOverTaskSdet,
        ] = await Promise.all([
            reportReq,
            resSdetReq,
            pendingResSdetReq,
            completedCarryOverTaskSdetReq,
            pendingCarryOverTaskSdetReq,
        ]);

        const excludeCompletedCarryOver = new Set(completedCarryOverTaskSdet.data.tasks.map((task) => task.task_link));
        const completedThisWeek = resSdet.data.tasks.filter((task) => !excludeCompletedCarryOver.has(task.task_link));
      return {
        dataWorklog: res.data.issues,
        dataProject: completedThisWeek.filter((task) => task.type_task === "Project Pareto"),
          dataOnGoingDev: completedThisWeek.filter((task) => task.type_task === "On Going Development"),
          dataMaintenance: completedThisWeek.filter((task) => task.type_task === "Maintenance"),
          dataPending: pendingResSdet.data.tasks,
          dataCarryOverPending: pendingCarryOverTaskSdet.data.tasks,
          dataCarryOverCompleted: completedCarryOverTaskSdet.data.tasks
      };
    },
  });

  if (isError) {
    return (
        <SomethingWentWrong />
    );
  }

  return (
    <div className="space-y-6">
      {/* Date Filter */}
      <DateFilter />
        <TeamMembersLeaderboardSdet dataSdet={data} />
      <div className="grid grid-cols-1 lg:grid-cols-2 md:grid-cols-2 gap-6">
        <TimeDistributionChart data={data.dataWorklog} role="sdet" />
        <ActivityChart data={data.dataWorklog} role="sdet" />
      </div>

      {/* Work Log Table */}
      <WorkLogTable data={data.dataWorklog} key="1" />
    </div>
  );
}
