import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { startOfWeek, isSameWeek } from "date-fns";

interface DateFilterState {
  startDate?: string;
  endDate?: string;
  quickFilter: string;
  activity: any;
}

const initialState: DateFilterState = {
  startDate: startOfWeek(new Date(), { weekStartsOn: 1 }).toISOString(),
  endDate: new Date(
    new Date().setDate(
      startOfWeek(new Date(), { weekStartsOn: 1 }).getDate() + 4
    )
  ).toISOString(),
  quickFilter: "this-week",
  activity: {},
};

const weekStart = startOfWeek(new Date(), { weekStartsOn: 1 });
const weekEnd = new Date(new Date().setDate(weekStart.getDate() + 4));

const dateFilterSlice = createSlice({
  name: "dateFilter",
  initialState,
  reducers: {
    setStartDate(state, action: PayloadAction<string | undefined>) {
      state.startDate = action.payload ?? weekStart.toISOString();
    },
    setEndDate(state, action: PayloadAction<string | undefined>) {
      state.endDate = action.payload ?? weekEnd.toISOString();
    },
    setQuickFilter(state, action: PayloadAction<string>) {
      state.quickFilter = action.payload;
    },
    setDateRange(
      state,
      action: PayloadAction<{ startDate?: string; endDate?: string }>
    ) {
      state.startDate = action.payload.startDate ?? weekStart.toISOString();
      state.endDate = action.payload.endDate ?? weekEnd.toISOString();
    },
    resetFilters(state) {
      state.startDate = weekStart.toISOString();
      state.endDate = new Date(
        new Date().setDate(weekStart.getDate() + 4)
      ).toISOString();
      state.quickFilter = "this-week";
    },
    checkAndUpdateDateFilters(state) {
      if (state.quickFilter && state.quickFilter !== "custom") {
        let shouldUpdate = false;
        let newStartDate: Date | undefined;
        let newEndDate: Date | undefined;
        const now = new Date();

        switch (state.quickFilter) {
          case "today":
            // Check if stored date is not today
            if (state.startDate) {
              const storedDate = new Date(state.startDate);
              if (storedDate.toDateString() !== now.toDateString()) {
                newStartDate = newEndDate = now;
                shouldUpdate = true;
              }
            }
            break;

          case "this-week":
            // Check if stored week is not current week
            if (state.startDate) {
              const storedStartDate = new Date(state.startDate);
              const currentWeekStart = startOfWeek(now, { weekStartsOn: 1 });
              if (
                !isSameWeek(storedStartDate, currentWeekStart, {
                  weekStartsOn: 1,
                })
              ) {
                newStartDate = currentWeekStart;
                newEndDate = new Date(
                  new Date().setDate(currentWeekStart.getDate() + 4)
                );
                shouldUpdate = true;
              }
            }
            break;

          case "last-week":
            // Check if stored week is not last week
            if (state.startDate) {
              const lastWeek = new Date(now);
              lastWeek.setDate(now.getDate() - 7);
              const lastWeekStart = startOfWeek(lastWeek, { weekStartsOn: 1 });
              const storedStartDate = new Date(state.startDate);
              if (
                !isSameWeek(storedStartDate, lastWeekStart, { weekStartsOn: 1 })
              ) {
                newStartDate = lastWeekStart;
                newEndDate = new Date(
                  new Date().setDate(lastWeekStart.getDate() + 4)
                );
                shouldUpdate = true;
              }
            }
            break;

          case "this-month":
            // Check if stored month is not current month
            if (state.startDate) {
              const storedDate = new Date(state.startDate);
              const currentMonthStart = new Date(
                now.getFullYear(),
                now.getMonth(),
                1
              );
              if (
                storedDate.getMonth() !== now.getMonth() ||
                storedDate.getFullYear() !== now.getFullYear()
              ) {
                newStartDate = currentMonthStart;
                newEndDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
                shouldUpdate = true;
              }
            }
            break;

          case "last-month":
            // Check if stored month is not last month
            if (state.startDate) {
              const storedDate = new Date(state.startDate);
              const lastMonthStart = new Date(
                now.getFullYear(),
                now.getMonth() - 1,
                1
              );
              if (
                storedDate.getMonth() !== lastMonthStart.getMonth() ||
                storedDate.getFullYear() !== lastMonthStart.getFullYear()
              ) {
                newStartDate = lastMonthStart;
                newEndDate = new Date(now.getFullYear(), now.getMonth(), 0);
                shouldUpdate = true;
              }
            }
            break;
        }

        if (shouldUpdate && newStartDate && newEndDate) {
          state.startDate = newStartDate.toISOString();
          state.endDate = newEndDate.toISOString();
        }
      }
    },
  },
});

export const {
  setStartDate,
  setEndDate,
  setQuickFilter,
  setDateRange,
  resetFilters,
  checkAndUpdateDateFilters,
} = dateFilterSlice.actions;

export default dateFilterSlice.reducer;
