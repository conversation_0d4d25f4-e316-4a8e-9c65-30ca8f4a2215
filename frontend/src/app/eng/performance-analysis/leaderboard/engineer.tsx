import { EngineeringTaskLeaderboardTasks } from "@/components/performance-analysis/eng/engineer-leaderboard-tasks";
import { engineeringEngineerLeaderBoard } from "@/components/performance-analysis/eng/engineering-engineer-leaderboard";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import { EngLeaderBoard } from "@/types/eng/leaderboard";
import { User } from "lucide-react";

export function EngineerLeaderboard({
  members,
}: {
  members: EngLeaderBoard[];
}) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="h-5 w-5 mr-2" />
            Engineer
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={engineeringEngineerLeaderBoard}
            data={members}
            initialSort={[{ id: "hours_per_sp", desc: false }]}
            searchPlaceHolder="Search Name..."
            renderExpandedRow={(row) => {
                return <EngineeringTaskLeaderboardTasks tasks={row.original.tasks} />;
              }}
          />
        </CardContent>
      </Card>
    </div>
  );
}
