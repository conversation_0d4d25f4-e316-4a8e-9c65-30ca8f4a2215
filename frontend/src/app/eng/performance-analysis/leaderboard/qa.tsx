import { EngineeringTaskLeaderboardTasks } from "@/components/performance-analysis/eng/engineer-leaderboard-tasks";
import { engineeringQALeaderBoard } from "@/components/performance-analysis/eng/engineering-engineer-leaderboard";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import { MemberLeaderBoard } from "@/types/eng/leaderboard";
import { User } from "lucide-react";

export function QALeaderboard({
  members,
}: {
  members: MemberLeaderBoard[];
}) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="h-5 w-5 mr-2" />
            QA
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={engineeringQALeaderBoard}
            data={members}
            initialSort={[{ id: "hours_per_sp", desc: false }]}
            searchPlaceHolder="Search Name..."
            renderExpandedRow={(row) => {
                return <EngineeringTaskLeaderboardTasks tasks={row.original.tasks} />;
              }}
          />
        </CardContent>
      </Card>
    </div>
  );
}
