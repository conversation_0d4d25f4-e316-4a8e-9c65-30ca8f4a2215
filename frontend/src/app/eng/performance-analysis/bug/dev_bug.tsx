import { devBugList } from "@/components/performance-analysis/eng/engineering-bug-columns";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import { DevBugResponse } from "@/types/eng/bug";
import { Bug } from "lucide-react";

export function EngDevBug({ devBugs }: { devBugs: DevBugResponse }) {
  const severity = Object.entries(devBugs.severity).sort((a, b) => b[1] - a[1]);
  const accidentType = Object.entries(devBugs.accident_type).sort(
    (a, b) => b[1] - a[1]
  );
  const byEngineer = Object.entries(devBugs.engineer).sort(
    (a, b) => b[1] - a[1]
  );
  const squad = Object.entries(devBugs.squad).sort((a, b) => b[1] - a[1]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Bug className="h-5 w-5 mr-2" />
          Development Bugs
        </CardTitle>
      </CardHeader>
      <CardContent>
        {!devBugs.bugs ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-4 mb-4">
              <Bug className="h-8 w-8 text-gray-400 dark:text-gray-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              No Bugs Found
            </h3>
            <p className="text-gray-500 dark:text-gray-400 max-w-sm">
              No bug data is available for the selected date range. Try
              adjusting your date filter or check if bugs exist.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            <div>
              <div className="flex flex-wrap gap-2">
                <Badge key="total" variant="outline" className="text-xs">
                  Total: {devBugs.total}
                </Badge>
              </div>
            </div>
            {/* Prod Severity */}
            {severity.length > 0 && (
              <div>
                <h4 className="text-sm font-semibold mb-3">Bugs by Severity</h4>
                <div className="flex flex-wrap gap-2">
                  {severity.map(([key, count]) => (
                    <Badge key={key} variant="outline" className="text-xs">
                      {key}: {count}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Prod Accident Bug */}
            {accidentType.length > 0 && (
              <div>
                <h4 className="text-sm font-semibold mb-3">
                  Bugs by Accident Bug
                </h4>
                <div className="flex flex-wrap gap-2">
                  {accidentType.map(([key, count]) => (
                    <Badge key={key} variant="outline" className="text-xs">
                      {key}: {count}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Prod Function */}
            {byEngineer.length > 0 && (
              <div>
                <h4 className="text-sm font-semibold mb-3">Bugs by Engineer</h4>
                <div className="flex flex-wrap gap-2">
                  {byEngineer.map(([key, count]) => (
                    <Badge key={key} variant="outline" className="text-xs">
                      {key}: {count}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Prod Squad */}
            {squad.length > 0 && (
              <div>
                <h4 className="text-sm font-semibold mb-3">Bugs by Squad</h4>
                <div className="flex flex-wrap gap-2">
                  {squad.map(([key, count]) => (
                    <Badge key={key} variant="outline" className="text-xs">
                      {key}: {count}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Bug Table */}
            <div>
              <h4 className="text-sm font-semibold mb-3">Bug Details</h4>
              <DataTable
                columns={devBugList}
                data={devBugs.bugs}
                searchPlaceHolder="Search Summary"
                initialSort={[{ id: "created_date", desc: true }]}
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
