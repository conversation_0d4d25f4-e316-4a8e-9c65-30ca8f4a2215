"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import { LPLoading } from "@/components/lp-loading";
import { Suspense, useEffect, useState } from "react";
import { Summary } from "./summary";
import { Epic } from "./epic/epic";
import { Leaderboard } from "./leaderboard/leaderboard";
import { Bug } from "./bug/bug";
import { Analytics } from "./analytics/analytics";

const STORAGE_KEY = "eng-perf-active-tab";
const VALID_TABS = new Set(["summary", "epics", "teams", "bugs", "analytics"]);

export function Tab() {
  const [tab, setTab] = useState("summary");

  // load initial value from localStorage
  useEffect(() => {
    const saved = localStorage.getItem(STORAGE_KEY);
    if (saved && VALID_TABS.has(saved)) {
      setTab(saved);
    }
  }, []);

  // persist whenever tab changes
  const handleChange = (next: string) => {
    if (VALID_TABS.has(next)) {
      setTab(next);
      localStorage.setItem(STORAGE_KEY, next);
    }
  };

  return (
    <div className="space-y-6">
      {/* Tabs for team views */}
      <Tabs value={tab} onValueChange={handleChange} className="w-full">
        <TabsList className="grid w-full md:w-2/3 grid-cols-5 mb-4">
          <TabsTrigger value="summary">Summary</TabsTrigger>
          <TabsTrigger value="epics">Epics</TabsTrigger>
          <TabsTrigger value="teams">Leader Board</TabsTrigger>
          <TabsTrigger value="bugs">Bugs</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="summary" className="space-y-4">
          <Suspense
            fallback={
              <div className="flex items-center justify-center h-60">
                <LPLoading />
              </div>
            }
          >
            <Summary />
          </Suspense>
        </TabsContent>

        <TabsContent value="epics" className="space-y-4">
          <Suspense
            fallback={
              <div className="flex items-center justify-center h-60">
                <LPLoading />
              </div>
            }
          >
            <Epic />
          </Suspense>
        </TabsContent>

        <TabsContent value="teams" className="space-y-4">
          <Suspense
            fallback={
              <div className="flex items-center justify-center h-60">
                <LPLoading />
              </div>
            }
          >
            <Leaderboard />
          </Suspense>
        </TabsContent>

        <TabsContent value="bugs" className="space-y-4">
        <Suspense
            fallback={
              <div className="flex items-center justify-center h-60">
                <LPLoading />
              </div>
            }
          >
            <Bug />
          </Suspense>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
        <Suspense
            fallback={
              <div className="flex items-center justify-center h-60">
                <LPLoading />
              </div>
            }
          >
            <Analytics />
          </Suspense>
        </TabsContent>
      </Tabs>
    </div>
  );
}
