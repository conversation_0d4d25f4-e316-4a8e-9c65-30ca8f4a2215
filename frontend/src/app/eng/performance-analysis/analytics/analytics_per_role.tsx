import { analyticPerRolesLists } from "@/components/performance-analysis/eng/engineering-analytics-columns";
import { DataTable } from "@/components/ui/data-table";
import { EngAnalyticsPerRole } from "@/types/eng/analytics";
import { AnalyticsPerMembers } from "./analytics_per_member";

export function AnalyticsPerRoles({
  perRoles,
}: {
  perRoles: EngAnalyticsPerRole[];
}) {
  return (
    <div className="space-y-6">
      <DataTable
        columns={analyticPerRolesLists}
        data={perRoles}
        disableSearch={true}
        renderExpandedRow={(row) => <AnalyticsPerMembers perMember={row.original.per_members} />}
      />
    </div>
  );
}
