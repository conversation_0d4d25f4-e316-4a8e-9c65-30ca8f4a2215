import { analyticPerMemberLists } from "@/components/performance-analysis/eng/engineering-analytics-columns";
import { DataTable } from "@/components/ui/data-table";
import { EngAnalyticsPerMember } from "@/types/eng/analytics";

export function AnalyticsPerMembers({
  perMember,
}: {
  perMember: EngAnalyticsPerMember[];
}) {
  return (
    <div className="space-y-6">
      <DataTable
        columns={analyticPerMemberLists}
        data={perMember}
        initialSort={[{ id: "total_sp", desc: true }]}
        searchPlaceHolder="Search Name...."
      />
    </div>
  );
}
