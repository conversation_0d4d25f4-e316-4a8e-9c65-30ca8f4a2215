import { RootState } from "@/app/store";
import { analyticLists } from "@/components/performance-analysis/eng/engineering-analytics-columns";
import { DataTable } from "@/components/ui/data-table";
import { API_BASE_URL } from "@/constants";
import { GetEngAnalyticsResponse } from "@/types/eng/analytics";
import { useSuspenseQuery } from "@tanstack/react-query";
import axios from "axios";
import { format } from "date-fns";
import { useSelector } from "react-redux";
import { AnalyticsPerRoles } from "./analytics_per_role";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { BarChart3 } from "lucide-react";
import { Badge } from "@/components/ui/badge";

export function Analytics() {
  const { startDate, endDate } = useSelector(
    (state: RootState) => state.dateFilter
  );

  const { data, isError } = useSuspenseQuery({
    queryKey: ["eng-analytics", startDate, endDate],
    queryFn: async () => {
      const res = await axios.get(API_BASE_URL + `/api/eng/analytics`, {
        headers: {
          Accept: "application/json",
        },
        params: {
          start_date: startDate
            ? format(new Date(startDate), "yyyy-MM-dd")
            : undefined,
          end_date: endDate
            ? format(new Date(endDate), "yyyy-MM-dd")
            : undefined,
        },
      });
      return res.data as GetEngAnalyticsResponse;
    },
    refetchOnWindowFocus: false,
  });

  const bugs = Object.entries(data.summary.bugs).sort((a, b) => b[1] - a[1]);

  if (isError)
    return (
      <div className="flex flex-col items-center justify-center h-full animate-fade-in bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="flex items-center bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded mb-4 shadow-md animate-fade-in">
          <svg
            className="w-6 h-6 mr-3 text-red-500"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M18.364 5.636l-12.728 12.728M5.636 5.636l12.728 12.728"
            />
          </svg>
          <div>
            <span className="font-bold">Oops! Something went wrong.</span>
            <div className="text-sm mt-1">
              We couldn't load the data. Please check your connection or try
              again.
            </div>
          </div>
        </div>
        <button
          className="bg-red-500 to-yellow-400 hover:bg-red-600 text-white font-bold py-2 px-6 rounded shadow-lg focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-50 transition"
          onClick={() => window.location.reload()}
          aria-label="Retry loading data"
        >
          Retry
        </button>
      </div>
    );

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Summary
          </CardTitle>
          <div>
            <h4 className="text-sm font-semibold mb-3">Summary</h4>
            <div className="flex flex-wrap gap-2">
              <Badge key="total_epic" variant="outline" className="text-xs">
                Total Epic: {data.summary.total_epic}
              </Badge>
              <Badge key="total_sp" variant="outline" className="text-xs">
                Total SP: {data.summary.total_sp}
              </Badge>
              <Badge key="total_manpower" variant="outline" className="text-xs">
                Total Manpower: {data.summary.total_manpower}
              </Badge>
              <Badge
                key="sp_per_manpower"
                variant="outline"
                className="text-xs"
              >
                SP per Manpower: {data.summary.sp_per_manpower}
              </Badge>
              <Badge key="total_hours" variant="outline" className="text-xs">
                Total Hours: {data.summary.total_hours}
              </Badge>
              <Badge key="hours_per_sp" variant="outline" className="text-xs">
                Hours per SP: {data.summary.hours_per_sp}
              </Badge>
            </div>
          </div>
          <div>
            <h4 className="text-sm font-semibold mb-3">Engineer</h4>
            <div className="flex flex-wrap gap-2">
              <Badge key="total_sp_eng" variant="outline" className="text-xs">
                Total SP: {data.summary.total_sp_eng}
              </Badge>

              <Badge key="total_eng" variant="outline" className="text-xs">
                Total Manpower: {data.summary.total_eng}
              </Badge>
              <Badge key="sp_per_eng" variant="outline" className="text-xs">
                SP per Manpower: {data.summary.sp_per_eng}
              </Badge>
              <Badge
                key="total_eng_hours"
                variant="outline"
                className="text-xs"
              >
                Total Hours: {data.summary.total_eng_hours}
              </Badge>
              <Badge
                key="eng_hours_per_sp"
                variant="outline"
                className="text-xs"
              >
                Hours per SP: {data.summary.eng_hours_per_sp}
              </Badge>
            </div>
          </div>
          <div>
            <h4 className="text-sm font-semibold mb-3">QA</h4>
            <div className="flex flex-wrap gap-2">
              <Badge key="total_sp_qa" variant="outline" className="text-xs">
                Total SP: {data.summary.total_sp_qa}
              </Badge>

              <Badge key="total_qa" variant="outline" className="text-xs">
                Total Manpower: {data.summary.total_qa}
              </Badge>
              <Badge key="sp_per_qa" variant="outline" className="text-xs">
                SP per Manpower: {data.summary.sp_per_qa}
              </Badge>
              <Badge key="total_qa_hours" variant="outline" className="text-xs">
                Total Hours: {data.summary.total_qa_hours}
              </Badge>
              <Badge
                key="qa_hours_per_sp"
                variant="outline"
                className="text-xs"
              >
                Hours per SP: {data.summary.qa_hours_per_sp}
              </Badge>
            </div>
            {bugs.length > 0 && (
              <div>
                <h4 className="text-sm font-semibold mb-3">Bugs</h4>
                <div className="flex flex-wrap gap-2">
                  {bugs.map(([key, count]) => (
                    <Badge key={key} variant="outline" className="text-xs">
                      {key}: {count}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div>
            <h4 className="text-sm font-semibold mb-3">Details</h4>
            <DataTable
              columns={analyticLists}
              data={data.data}
              initialSort={[{ id: "release_date", desc: true }]}
              searchPlaceHolder="Search Release Date..."
              renderExpandedRow={(row) => (
                <AnalyticsPerRoles perRoles={row.original.per_role} />
              )}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
