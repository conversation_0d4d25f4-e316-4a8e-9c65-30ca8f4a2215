"use client";

import { useSelector } from "react-redux";
import { RootState } from "@/app/store";
import {
  LucideIcon,
  <PERSON>,
  Gauge,
  <PERSON>,
  ListChecks,
  RotateCcw,
  ShieldCheck,
  User,
  RefreshCw,
} from "lucide-react";
import { MetricCards } from "@/components/metric-cards";
import axios from "axios";
import { useSuspenseQuery } from "@tanstack/react-query";
import { API_BASE_URL } from "@/constants";
import { format } from "date-fns";
import { GetEngEpicResponse } from "@/types/eng/epic";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import { engineeringEpicList } from "@/components/performance-analysis/eng/engineering-epic-column";
import React from "react";
import { EpicList } from "./epic_list";
import { Button } from "@/components/ui/button";

type MetricItem = {
  title: string;
  value: number;
  icon: LucideIcon;
  iconColor: string;
};

type MetricSection = {
  key: string;
  metrics: MetricItem[];
  section: string;
  columns?: 2 | 3 | 4 | 5;
};

export function Epic() {
  const { startDate, endDate } = useSelector(
    (state: RootState) => state.dateFilter
  );

  const [statusFilter, setStatusFilter] = React.useState<string>("true");

  const { data, isError, refetch, isFetching } = useSuspenseQuery({
    queryKey: ["eng-epic", startDate, endDate, statusFilter],
    queryFn: async () => {
      const res = await axios.get(API_BASE_URL + `/api/eng/epic`, {
        headers: {
          Accept: "application/json",
        },
        params: {
          start_date: startDate
            ? format(new Date(startDate), "yyyy-MM-dd")
            : undefined,
          end_date: endDate
            ? format(new Date(endDate), "yyyy-MM-dd")
            : undefined,
          is_active_only: statusFilter,
        },
      });
      return res.data as GetEngEpicResponse;
    },
    refetchOnWindowFocus: false,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  const sortedEpics = React.useMemo(() => {
    return [...(data?.epics || [])].sort((a, b) => {
      const dateA = new Date(a.expected_release_date).getTime();
      const dateB = new Date(b.expected_release_date).getTime();
      if (dateA === dateB) {
        return a.name.localeCompare(b.name);
      }
      return dateA - dateB;
    });
  }, [data?.epics]);

  if (isError)
    return (
      <div className="flex flex-col items-center justify-center h-full animate-fade-in bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="flex items-center bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded mb-4 shadow-md animate-fade-in">
          <svg
            className="w-6 h-6 mr-3 text-red-500"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M18.364 5.636l-12.728 12.728M5.636 5.636l12.728 12.728"
            />
          </svg>
          <div>
            <span className="font-bold">Oops! Something went wrong.</span>
            <div className="text-sm mt-1">
              We couldn't load the data. Please check your connection or try
              again.
            </div>
          </div>
        </div>
        <button
          className="bg-red-500 to-yellow-400 hover:bg-red-600 text-white font-bold py-2 px-6 rounded shadow-lg focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-50 transition"
          onClick={() => window.location.reload()}
          aria-label="Retry loading data"
        >
          Retry
        </button>
      </div>
    );

  const metricSections: MetricSection[] = [
    {
      key: "metrics-1",
      section: "",
      columns: 3,
      metrics: [
        {
          title: "Total Task",
          value: data.total_ticket,
          icon: ListChecks,
          iconColor: "text-gray-700",
        },
        {
          title: "Total SP",
          value: data.total_sp,
          icon: Gauge,
          iconColor: "text-violet-700",
        },
        {
          title: "Total SP Engineer",
          value: data.total_sp_engineer,
          icon: Hammer,
          iconColor: "text-blue-600",
        },
      ],
    },
    {
      key: "metrics-2",
      section: "",
      columns: 3,
      metrics: [
        {
          title: "Total SP QA",
          value: data.total_sp_qa,
          icon: ShieldCheck,
          iconColor: "text-green-600",
        },
        {
          title: "% Ontime",
          value: data.ontime_percent,
          icon: Clock,
          iconColor: "text-emerald-600",
        },
        {
          title: "% Avg Retest Hours",
          value: data.avg_retest_hour,
          icon: RotateCcw,
          iconColor: "text-amber-600",
        },
      ],
    },
  ];

  return (
    <div className="space-y-6">
      {metricSections.map((props) => (
        <MetricCards
          key={props.key}
          metrics={props.metrics}
          section={props.section}
          columns={props.columns as 2 | 3 | 4 | 5}
        />
      ))}
      <Card>
        <CardHeader className="flex justify-between items-center">
          <CardTitle className="flex items-center">
            <User className="h-5 w-5 mr-2" />
            Epics
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            disabled={isFetching}
            className="flex items-center space-x-1"
          >
            <RefreshCw
              className={`h-4 w-4 ${isFetching ? "animate-spin" : ""}`}
            />
            <span>{isFetching ? "Refreshing..." : "Refresh"}</span>
          </Button>
        </CardHeader>
        <CardContent>
          {!data?.epics || data.epics.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-4 mb-4">
                <User className="h-8 w-8 text-gray-400 dark:text-gray-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                No Epics Found
              </h3>
              <p className="text-gray-500 dark:text-gray-400 max-w-sm">
                No epic data is available for the selected date range. Try
                adjusting your date filter or check if epics exist.
              </p>
            </div>
          ) : (
            <div className="space-y-4 sm:space-y-6">
              <DataTable
                columns={engineeringEpicList}
                data={sortedEpics}
                initialSort={[{ id: "expected_release_date", desc: false }]}
                searchPlaceHolder="Search Name..."
                filterData={[
                  { label: "All", value: "false" },
                  { label: "Active Only", value: "true" },
                ]}
                selectedFilter={statusFilter}
                onFilterChange={(value) => setStatusFilter(value)}
                renderExpandedRow={(row) => <EpicList epic={row.original} />}
              />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
