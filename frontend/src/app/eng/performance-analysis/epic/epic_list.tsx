import { engineeringTaskList } from "@/components/performance-analysis/eng/engineering-task-columns";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import { Epic } from "@/types/eng/epic";
import React from "react";

export function EpicList({ epic }: { epic: Epic }) {
  const sortedTasks = React.useMemo(() => {
    return [...(epic?.tasks || [])].sort((a, b) => {
      const dateA = new Date(a.expected_start_date).getTime();
      const dateB = new Date(b.expected_start_date).getTime();
      if (dateA === dateB) {
        return a.key.localeCompare(b.key); // secondary sort by epic key
      }
      return dateA - dateB;
    });
  }, [epic?.tasks]);

  return (
    <Card key={epic.url} className="border-2">
      <CardHeader>
        <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex flex-col gap-2 text-xs sm:text-sm text-muted-foreground">
              {/* First row: Reporter */}
              <div className="flex items-center gap-2 w-full">
                <div className="flex items-center gap-1 min-w-0 flex-1">
                  <span className="font-semibold text-foreground text-xs whitespace-nowrap">
                    Reporter:
                  </span>
                  <Badge
                    variant="outline"
                    className="px-2 py-0.5 text-xs truncate"
                  >
                    {epic.reporter}
                  </Badge>
                </div>
              </div>
              {/* Second row: Grooming */}
              <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4 sm:flex-wrap">
                <div className="flex items-center gap-1">
                  <span className="font-semibold text-foreground text-xs">
                    Grooming:
                  </span>
                  <Badge variant="outline" className="px-2 py-0.5 text-xs">
                    {new Intl.DateTimeFormat("en-GB", {
                      day: "2-digit",
                      month: "short",
                      year: "numeric",
                    }).format(new Date(epic.grooming_date))}
                  </Badge>
                  <span className="font-semibold text-foreground text-xs">
                    Expected Release Date:
                  </span>
                  <Badge variant="outline" className="px-2 py-0.5 text-xs">
                    {new Intl.DateTimeFormat("en-GB", {
                      day: "2-digit",
                      month: "short",
                      year: "numeric",
                    }).format(new Date(epic.expected_release_date))}
                  </Badge>
                </div>
              </div>
              {/* Third row: Task */}
              <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4 sm:flex-wrap">
                {epic.grooming_date && (
                  <div className="flex items-center gap-1">
                    <span className="font-semibold text-foreground text-xs">
                      Total Task:
                    </span>
                    <Badge
                      variant="outline"
                      className="px-2 py-0.5 text-xs truncate"
                    >
                      {epic.total_ticket}
                    </Badge>
                    <span className="font-semibold text-foreground text-xs">
                      Additional From Product:
                    </span>
                    <Badge
                      variant="outline"
                      className="px-2 py-0.5 text-xs truncate"
                    >
                      {epic.add_from_product}
                    </Badge>
                    <span className="font-semibold text-foreground text-xs">
                      Additional From SA:
                    </span>
                    <Badge
                      variant="outline"
                      className="px-2 py-0.5 text-xs truncate"
                    >
                      {epic.add_from_sa}
                    </Badge>
                  </div>
                )}
              </div>
              {/* Fourth row: SP */}
              <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4 sm:flex-wrap">
                {epic.grooming_date && (
                  <div className="flex items-center gap-1">
                    <span className="font-semibold text-foreground text-xs">
                      Total SP:
                    </span>
                    <Badge
                      variant="outline"
                      className="px-2 py-0.5 text-xs truncate"
                    >
                      {epic.total_sp}
                    </Badge>
                    <span className="font-semibold text-foreground text-xs">
                      Total Hours:
                    </span>
                    <Badge
                      variant="outline"
                      className="px-2 py-0.5 text-xs truncate"
                    >
                      {epic.total_hours}
                    </Badge>
                    <span className="font-semibold text-foreground text-xs">
                      Total Hours per SP:
                    </span>
                    <Badge
                      variant="outline"
                      className="px-2 py-0.5 text-xs truncate"
                    >
                      {epic.hours_per_sp}
                    </Badge>
                  </div>
                )}
              </div>
              {/* Fifth row: Dev Bug */}
              <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4 sm:flex-wrap">
                {epic.grooming_date && (
                  <div className="flex items-center gap-1">
                    <span className="font-semibold text-foreground text-xs">
                      Total Dev Bug:
                    </span>
                    <Badge
                      variant="outline"
                      className="px-2 py-0.5 text-xs truncate"
                    >
                      {epic.total_bug}
                    </Badge>
                    <span className="font-semibold text-foreground text-xs">
                      Retest Hours:
                    </span>
                    <Badge
                      variant="outline"
                      className="px-2 py-0.5 text-xs truncate"
                    >
                      {epic.retest_hours}
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <DataTable
          columns={engineeringTaskList}
          data={sortedTasks}
          searchPlaceHolder="Search Key..."
          initialSort={[{ id: "expected_start_date", desc: false }]}
        />
      </CardContent>
    </Card>
  );
}
