"use client";

import axios from "axios";
import { RootState } from "@/app/store";
import { useSuspenseQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { useSelector } from "react-redux";

import { API_BASE_URL } from "@/constants";
import { MetricCards } from "@/components/metric-cards";
import {
  AlertCircle,
  Bug,
  Code,
  FlaskConical,
  Hammer,
  HelpCircle,
  Layers,
  LucideIcon,
  PauseCircle,
  Rocket,
  ShieldCheck,
  Slash,
  Timer,
  Users,
  Users2,
} from "lucide-react";
import { GetEngSummaryResponse } from "@/types/eng/summary";

type MetricItem = {
  title: string;
  value: number;
  icon: LucideIcon;
  iconColor: string;
};

type MetricSection = {
  key: string;
  metrics: MetricItem[];
  section: string;
  columns?: 2 | 3 | 4 | 5;
};

export function Summary() {
  const { startDate, endDate } = useSelector(
    (state: RootState) => state.dateFilter
  );

  const { data, isError } = useSuspenseQuery({
    queryKey: ["eng-summary", startDate, endDate],
    queryFn: async () => {
      const res = await axios.get(API_BASE_URL + `/api/eng/summary`, {
        headers: {
          Accept: "application/json",
        },
        params: {
          start_date: startDate
            ? format(new Date(startDate), "yyyy-MM-dd")
            : undefined,
          end_date: endDate
            ? format(new Date(endDate), "yyyy-MM-dd")
            : undefined,
        },
      });
      return res.data as GetEngSummaryResponse;
    },
  });

  const metricSections: MetricSection[] = [
    {
      key: "production-bugs-1",
      section: "Production Bugs",
      columns: 3,
      metrics: [
        {
          title: "Total",
          value: data?.production_bug?.total,
          icon: AlertCircle,
          iconColor: "text-gray-700",
        },
        {
          title: "From Product",
          value: data?.production_bug?.from_product,
          icon: Users,
          iconColor: "text-yellow-600",
        },
        {
          title: "From SA",
          value: data?.production_bug?.from_sa,
          icon: HelpCircle,
          iconColor: "text-blue-600",
        },
      ],
    },
    {
      key: "production-bugs-2",
      section: "",
      columns: 3,
      metrics: [
        {
          title: "From Engineering",
          value: data?.production_bug?.from_engineer,
          icon: Code,
          iconColor: "text-red-600",
        },
        {
          title: "Others",
          value: data?.production_bug?.others,
          icon: Bug,
          iconColor: "text-gray-500",
        },
        {
          title: "Not Defined",
          value: data?.production_bug?.not_defined,
          icon: Slash,
          iconColor: "text-zinc-400",
        },
      ],
    },
    {
      key: "epic-section",
      section: "Epic",
      columns: 4,
      metrics: [
        {
          title: "Total",
          value: data?.epic?.total,
          icon: Layers,
          iconColor: "text-gray-700",
        },
        {
          title: "In Development",
          value: data?.epic?.in_development,
          icon: Hammer,
          iconColor: "text-orange-500",
        },
        {
          title: "In Testing",
          value: data?.epic?.in_testing,
          icon: FlaskConical,
          iconColor: "text-indigo-600",
        },
        {
          title: "Ready to Release",
          value: data?.epic?.ready_to_release,
          icon: Rocket,
          iconColor: "text-green-600",
        },
      ],
    },
    {
      key: "member-engineer",
      section: "Member",
      columns: 3,
      metrics: [
        {
          title: "Total Engineer",
          value: data?.member?.engineer?.total,
          icon: Users2,
          iconColor: "text-blue-700",
        },
        {
          title: "Idle Engineer",
          value: data?.member?.engineer?.idle,
          icon: PauseCircle,
          iconColor: "text-yellow-500",
        },
        {
          title: "Engineer Avg Hours/SP",
          value: data?.member?.engineer?.hour_per_sp,
          icon: Timer,
          iconColor: "text-cyan-600",
        },
      ],
    },
    {
      key: "member-qa",
      section: "",
      columns: 3,
      metrics: [
        {
          title: "Total QA",
          value: data?.member?.qa?.total,
          icon: ShieldCheck,
          iconColor: "text-blue-700",
        },
        {
          title: "Idle QA",
          value: data?.member?.qa?.idle,
          icon: PauseCircle,
          iconColor: "text-yellow-500",
        },
        {
          title: "QA Avg Hours/SP",
          value: data?.member?.qa?.hour_per_sp,
          icon: Timer,
          iconColor: "text-cyan-600",
        },
      ],
    },
  ];

  if (isError)
    return (
      <div className="flex flex-col items-center justify-center h-full animate-fade-in bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="flex items-center bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded mb-4 shadow-md animate-fade-in">
          <svg
            className="w-6 h-6 mr-3 text-red-500"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M18.364 5.636l-12.728 12.728M5.636 5.636l12.728 12.728"
            />
          </svg>
          <div>
            <span className="font-bold">Oops! Something went wrong.</span>
            <div className="text-sm mt-1">
              We couldn't load the data. Please check your connection or try
              again.
            </div>
          </div>
        </div>
        <button
          className="bg-red-500 to-yellow-400 hover:bg-red-600 text-white font-bold py-2 px-6 rounded shadow-lg focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-50 transition"
          onClick={() => window.location.reload()}
          aria-label="Retry loading data"
        >
          Retry
        </button>
      </div>
    );

  return (
    <div className="space-y-6">
      {metricSections.map((props) => (
        <MetricCards
          key={props.key}
          metrics={props.metrics}
          section={props.section}
          columns={props.columns as 2 | 3 | 4 | 5}
        />
      ))}
    </div>
  );
}
