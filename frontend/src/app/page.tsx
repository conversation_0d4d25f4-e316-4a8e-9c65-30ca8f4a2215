"use client";

import { Badge } from "@/components/ui/badge";

export default function Dashboard() {
  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-700 dark:to-purple-700 rounded-lg p-8 text-white">
        <h1 className="text-4xl font-bold mb-4">Welcome to Pentools</h1>
        <p className="text-xl text-blue-100 dark:text-blue-200 mb-6">
          Your comprehensive platform for performance analysis and team insights
        </p>
        <div className="flex flex-wrap gap-3">
          <Badge
            variant="secondary"
            className="bg-white/20 text-white border-white/30"
          >
            Real-time Analytics
          </Badge>
          <Badge
            variant="secondary"
            className="bg-white/20 text-white border-white/30"
          >
            Team Performance
          </Badge>
          <Badge
            variant="secondary"
            className="bg-white/20 text-white border-white/30"
          >
            JIRA Integration
          </Badge>
        </div>
      </div>
    </div>
  );
}
