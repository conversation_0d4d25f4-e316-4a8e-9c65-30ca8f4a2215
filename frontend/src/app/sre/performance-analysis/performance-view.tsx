"use client";

import { DateFilter } from "@/components/date-filter";
import { LPLoading } from "@/components/lp-loading";
import { TeamOverview } from "@/components/performance-analysis/team-overview";
import { Suspense } from "react";

export function PerformanceView() {
  return (
    <>
      <DateFilter />
      <Suspense fallback={<LPLoading />}>
        <TeamOverview jql="worklogAuthor IN (61e4bb6f38041c00683a78fe, 712020:faea7bdc-e5ff-400c-a336-f56697796438, 712020:76590e2c-27fe-4c53-81d6-af8bed1cb75f, 712020:9fa20be7-9da5-4ba7-8342-fc5d1309caf0) and worklogDate >= %s and worklogDate <= %s" />
      </Suspense>
    </>
  );
}
