import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Provider } from "./providers";
import { AuthGuard } from "@/components/auth/auth-guard";
import { AuthenticatedLayout } from "@/components/auth/authenticated-layout";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Pentools",
  description: "Platform Engineer Dashboard Tools",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Provider>
          <AuthGuard>
            <AuthenticatedLayout>
              {children}
            </AuthenticatedLayout>
          </AuthGuard>
        </Provider>
      </body>
    </html>
  );
}
