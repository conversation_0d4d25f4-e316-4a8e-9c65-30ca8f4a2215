"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import {WeeklyReportBreakdown} from "@/app/pe/performance-analysis/components/weekly-report-breakdown";
import {RootState} from "@/app/store";
import {useSelector} from "react-redux";
import {WeeklyReportOperation} from "@/app/pe/performance-analysis/components/weekly-report-operation";
import {WeeklyReportBug} from "@/app/pe/performance-analysis/components/weekly-report-bug";

interface WeeklyReportProps {
  uniqueUserIds: string[];
}

function getLast4WeeksRange(today = new Date()) {
    const result = [];

    // Pastikan hari ini adalah akhir minggu (Jumat) atau sesudah
    const dayOfWeek = today.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday
    const diffToFriday = (5 - dayOfWeek + 7) % 7;
    const lastFriday = new Date(today);
    lastFriday.setDate(today.getDate() + diffToFriday); // Ju<PERSON> minggu ini

    // Fungsi untuk format tanggal menjadi "DD MMMM YYYY"
    function formatDate(date) {
        return date.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric'
        }).replace(/ /g, ' ');
    }

    for (let i = 3; i >= 0; i--) {
        const end = new Date(lastFriday);
        end.setDate(end.getDate() - i * 7);
        const start = new Date(end);
        start.setDate(end.getDate() - 4); // Senin = Jumat - 4 hari

        result.push({
            start_date: formatDate(start),
            end_date: formatDate(end),
            data: []
        });
    }

    return result;
}

export function WeeklyReport({ uniqueUserIds }: WeeklyReportProps) {
    const { startDate } = useSelector(
        (state: RootState) => state.dateFilter
    );
    const lastWeeksRange = getLast4WeeksRange(new Date(startDate));

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          Weekly Report
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-y-3">
        <WeeklyReportBreakdown uniqueUserIds={uniqueUserIds} lastWeeksRange={lastWeeksRange} />
        <WeeklyReportOperation lastWeeksRange={lastWeeksRange} />
        <WeeklyReportBug lastWeeksRange={lastWeeksRange} />
      </CardContent>
    </Card>
  );
}
