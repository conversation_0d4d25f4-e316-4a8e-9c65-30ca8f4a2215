"use client";
import { Clock, User } from "lucide-react";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { format } from "date-fns";
import { useSelector } from "react-redux";
import { RootState } from "@/app/store";
import {
  getBacklogEpics,
  getDoneBacklogEpics,
  getMondayWednesdayEpics,
  getThursdayFridayEpics,
  getThursdayFridayStoryPoints,
  getMondayWednesdayStoryPoints,
  getDoneWeeklyEpics,
  getTotalStoryPointsFromBacklog,
  getCalculateDoneStoryPoints,
  getTotalBreakdownHours,
  EngineerMetrics,
  calculateRecommendation,
  getLoadColor,
  cn,
} from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

interface TeamMembersEpicProps {
  dataEpics: any[];
}

// Utility: Extract initials for avatar fallback
const getInitials = (name: string) => {
  const initials = name
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase();
  return initials.length > 2 ? initials.slice(0, 2) : initials;
};

export function TeamMembersEpic({ dataEpics }: TeamMembersEpicProps) {
  const { startDate, endDate } = useSelector(
    (state: RootState) => state.dateFilter
  );
  const filterDate = {
    start_date: format(
      new Date(startDate ?? Date.now()).setHours(0, 0, 0, 0),
      "yyyy-MM-dd"
    ),
    end_date: format(
      new Date(endDate ?? Date.now()).setHours(0, 0, 0, 0),
      "yyyy-MM-dd"
    ),
  };
  const engineerIssues = dataEpics
    .flatMap((epic) => epic.issues)
    .filter(
      (e) =>
        !e.name.includes("andrean") &&
        !e.name.includes("Adam Nasrudin") &&
        !e.name.includes("Tedja") &&
        !e.name.includes("Bian")
    );
  const engineerMetricsMap: any = engineerIssues.reduce(
    (acc: Record<string, EngineerMetrics>, issue: any) => {
      const name = issue.name;
      if (!acc[name]) {
        acc[name] = {
          totalHoursBreakdown: 0,
          totalStoryPointsBreakdown: 0,
          breakdownSla: 0,
          totalBacklogEpic: 0,
          totalBacklogStoryPoint: 0,
          totalMondayWednesdayEpic: 0,
          totalMondayWednesdayStoryPoint: 0,
          totalThursdayFridayEpic: 0,
          totalThursdayFridayStoryPoint: 0,
          totalBacklogEpicDone: 0,
          totalBacklogStoryPointDone: 0,
          totalMondayWednesdayEpicDone: 0,
          totalMondayWednesdayStoryPointDone: 0,
          totalThursdayFridayEpicDone: 0,
          totalThursdayFridayStoryPointDone: 0,
          epic: [],
          name: name,
          loadRecommendations: {
            isOverload: false,
            handoverRecommendation: [],
            tasksRecommendation: [],
            remainingCapacity: 0,
          },
          userID: issue.user_id,
        };
      }
      return acc;
    },
    {} as Record<string, EngineerMetrics>
  );
  const engineers = Object.entries(engineerMetricsMap).map(
    ([name, metrics]) => {
      const m = metrics as EngineerMetrics;
      m.epic = dataEpics
        .map((epic) => {
          const filteredIssues = epic.issues.filter(
            (issue) => issue.name === name
          );
          if (filteredIssues.length > 0) {
            return {
              ...epic,
              issues: filteredIssues,
            };
          }
          return null;
        })
        .filter((epic) => epic !== null);
      m.totalBacklogEpic = getBacklogEpics(m.epic, filterDate).length;
      m.totalBacklogEpicDone = getDoneBacklogEpics(m.epic, filterDate).length;
      m.totalBacklogStoryPoint = getTotalStoryPointsFromBacklog(
        m.epic,
        filterDate
      );
      m.totalBacklogStoryPointDone = getCalculateDoneStoryPoints(
        getBacklogEpics(m.epic, filterDate),
        filterDate
      );
      m.totalMondayWednesdayEpic = getMondayWednesdayEpics(
        m.epic,
        filterDate
      ).length;
      m.totalMondayWednesdayEpicDone = getDoneWeeklyEpics(
        getMondayWednesdayEpics(m.epic, filterDate)
      ).length;
      m.totalMondayWednesdayStoryPoint = getMondayWednesdayStoryPoints(
        m.epic,
        filterDate
      );
      m.totalMondayWednesdayStoryPointDone = getCalculateDoneStoryPoints(
        getMondayWednesdayEpics(m.epic, filterDate),
        filterDate
      );
      m.totalThursdayFridayEpic = getThursdayFridayEpics(
        m.epic,
        filterDate
      ).length;
      m.totalThursdayFridayEpicDone = getDoneWeeklyEpics(
        getThursdayFridayEpics(m.epic, filterDate)
      ).length;
      m.totalThursdayFridayStoryPoint = getThursdayFridayStoryPoints(
        m.epic,
        filterDate
      );
      m.totalThursdayFridayStoryPointDone = getCalculateDoneStoryPoints(
        getThursdayFridayEpics(m.epic, filterDate),
        filterDate
      );
      m.totalHoursBreakdown = getTotalBreakdownHours(m.epic, filterDate);
      m.totalStoryPointsBreakdown =
        m.totalBacklogStoryPointDone +
        m.totalMondayWednesdayStoryPointDone +
        m.totalThursdayFridayStoryPointDone;
      m.breakdownSla =
        m.totalHoursBreakdown > 0
          ? m.totalStoryPointsBreakdown / m.totalHoursBreakdown
          : 0;
      return m;
    }
  );
  engineers.forEach((engineer) => {
    engineer.loadRecommendations = calculateRecommendation(
      startDate,
      endDate,
      engineer
    );
  });
  const sortedEngineers = engineers.sort((a, b) =>
    a.name.localeCompare(b.name)
  );

  return (
    <div className="flex flex-col gap-3">
      <Card>
        {/* Card Header */}
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="h-5 w-5 mr-2" />
            Team Members
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {sortedEngineers.map((engineer) => {
              const initials = getInitials(engineer.name);
              return (
                <Card key={engineer.name}>
                  <CardContent>
                    {/* Profile & performance */}
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <Avatar>
                          <AvatarFallback className="bg-primary text-primary-foreground tracking-wide flex items-center justify-center">
                            {initials}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h3 className="font-semibold text-sm">
                            {engineer.name}
                          </h3>
                          <h6 className="font-normal text-xs">
                            Remaining Capacity:{" "}
                            {engineer.loadRecommendations.remainingCapacity.toFixed(
                              0
                            )}{" "}
                            SP
                          </h6>
                        </div>
                      </div>
                      <Badge
                        className={cn(
                          getLoadColor(engineer.loadRecommendations.isOverload),
                          "text-xs p-1 font-semibold"
                        )}
                        variant="secondary"
                      >
                        {engineer.loadRecommendations.isOverload
                          ? "OVERLOAD"
                          : "AVAILABLE"}
                      </Badge>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center ">
                          <Clock className="h-3 w-3 mr-1" />
                          Backlog Epic (SP)
                        </div>
                        <div className="font-medium">
                          {engineer.totalBacklogEpic} (
                          {engineer.totalBacklogStoryPoint} SP)
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center ">
                          <Clock className="h-3 w-3 mr-1" />
                          Backlog Epic Done (SP)
                        </div>
                        <div className="font-medium">
                          {engineer.totalBacklogEpicDone} (
                          {engineer.totalBacklogStoryPointDone} SP)
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center ">
                          <Clock className="h-3 w-3 mr-1" />
                          Monday - Wednesday Epic (SP)
                        </div>
                        <div className="font-medium">
                          {engineer.totalMondayWednesdayEpic} (
                          {engineer.totalMondayWednesdayStoryPoint} SP)
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center ">
                          <Clock className="h-3 w-3 mr-1" />
                          Monday - Wednesday Epic Done (SP)
                        </div>
                        <div className="font-medium">
                          {engineer.totalMondayWednesdayEpicDone} (
                          {engineer.totalMondayWednesdayStoryPointDone} SP)
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center ">
                          <Clock className="h-3 w-3 mr-1" />
                          Thursday - Friday Epic (SP)
                        </div>
                        <div className="font-medium">
                          {engineer.totalThursdayFridayEpic} (
                          {engineer.totalThursdayFridayStoryPoint} SP)
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center ">
                          <Clock className="h-3 w-3 mr-1" />
                          Thursday - Friday Epic Done (SP)
                        </div>
                        <div className="font-medium">
                          {engineer.totalThursdayFridayEpicDone} (
                          {engineer.totalThursdayFridayStoryPointDone} SP)
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button className="w-full" variant="outline" asChild>
                      <Link
                        href={{
                          pathname: `/pe/performance-analysis/detail`,
                          query: { user_id: engineer.userID },
                        }}
                      >
                        View Details
                      </Link>
                    </Button>
                  </CardFooter>
                </Card>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
