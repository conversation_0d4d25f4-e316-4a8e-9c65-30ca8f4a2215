"use client";

import {useQueries} from "@tanstack/react-query";
import axios from "axios";
import {<PERSON>, CardContent, CardHeader, CardTitle} from "@/components/ui/card";
import {DataTable} from "@/components/ui/data-table";
import {ColumnDef} from "@tanstack/react-table";
import {API_BASE_URL} from "@/constants";
import { format } from "date-fns";
import type {BugResponse} from "@/types/bug";
import {deploymentReleaseDate, isDateBetween} from "@/app/pe/performance-analysis/components/weekly-report-breakdown";


export function calculatePercentageChange(previous = 0, current = 0) {

  // Handle kondisi khusus
  if (previous === 0) {
    if (current === 0) return 0;       // Tidak ada perubahan
    if (current > 0) return 100;       // Dari 0 ke angka > 0 → 100% naik
  }

  if (previous > 0 && current === 0) {
    return -100; // Dari angka ke 0 → anggap jadi -100%
  }

  // Hitung perubahan normal
  return ((current - previous) / previous) * 100;
}
export const PercentageChange = ({ value, indexColumn }) => {
  if (indexColumn === 0) return "";
  const className = value > 0
      ? "text-green-600"
      : value < 0
          ? "text-red-600"
          : "text-gray-500"; // opsional untuk 0

  return <span className={className}>&nbsp;{value.toFixed(1)}%</span>;
};


export function WeeklyReportBug({ lastWeeksRange }) {
  const weeklyReportColumns: ColumnDef<any>[] = [
    {
      accessorKey: "/",
      header: "/",
      cell: ({ row }) => (
          <div className="truncate font-bold">
            {row.original.type_data} <br />
            PIC: {row.original.role}
          </div>
      ),
      enableSorting: false,
      enableColumnFilter: true,
    },
  ];
  const weeklyQueries = useQueries({
    queries: lastWeeksRange.map((week) => ({
      queryKey: ["team-bugs", week.start_date, week.end_date],
      queryFn: async () => {
        const res = await axios.get(API_BASE_URL + `/api/jira/bug`, {
          headers: {
            Accept: "application/json",
          },
          params: {
            start_date: week.start_date
                ? format(new Date(week.start_date), "yyyy-MM-dd")
                : undefined,
            end_date: week.end_date
                ? format(new Date(week.end_date), "yyyy-MM-dd")
                : undefined,
          },
        });
        const emptyData: BugResponse = {
          bugs: [],
        };
        return res.data ?? emptyData;
      },
      suspense: true, // jika kamu pakai suspense
    })),
  });
  const weeklyReportRawData = lastWeeksRange.map((week, index) => {
    const bugData = (weeklyQueries[index] as any).data;
    const tagRelease = deploymentReleaseDate.some((deployment) => {
      return isDateBetween(deployment, week.start_date, week.end_date);
    });
    weeklyReportColumns.push({
      accessorKey: `${week.start_date} - ${week.end_date}`,
      header: `${week.start_date} - ${week.end_date} ${tagRelease ? '(R)' : ''}`,
      cell: ({row}) => {
        const percentageChanges = index > 0 ? calculatePercentageChange(row.original.data[index-1], row.original.data[index]) : 0;
        return (
            <div className="truncate max-w-[120px] font-bold">
              {row.original.data[index] || 0}
              {<PercentageChange indexColumn={index} value={percentageChanges} />}
            </div>
        )
      },
      enableSorting: false,
      enableColumnFilter: true,
    });

    const bugsByAccidentBug = bugData.bugs.reduce((acc, bug) => {
      if (bug.accident_bug) {
        acc[bug.accident_bug] = (acc[bug.accident_bug] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>)

    const bugsBySegmentation = bugData.bugs.filter((bug) => {
      return bug.accident_bug === "Bug Case Not Covered";
    }).reduce((acc, bug) => {
      if (bug.bug_segmentation) {
        acc[bug.bug_segmentation] = (acc[bug.bug_segmentation] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    return {
      start_date: week.start_date,
      end_date: week.end_date,
      data: bugsByAccidentBug,
      dataSegmentation: bugsBySegmentation,
      totalDataBug: bugData.bugs.length
    }
  });
  const weeklyReportData = [
    {
      type_data: "Total Bug",
      role: "All Team",
      data: [
        weeklyReportRawData[0].totalDataBug,
        weeklyReportRawData[1].totalDataBug,
        weeklyReportRawData[2].totalDataBug,
        weeklyReportRawData[3].totalDataBug,
      ]
    },
    {
      type_data: "Bug Case Not Covered",
      role: "Platform Engineer",
      data: [
        weeklyReportRawData[0].data["Bug Case Not Covered"],
        weeklyReportRawData[1].data["Bug Case Not Covered"],
        weeklyReportRawData[2].data["Bug Case Not Covered"],
        weeklyReportRawData[3].data["Bug Case Not Covered"],
      ]
    },
    {
      type_data: "Bug in Test Case",
      role: "Software Engineer",
      data: [
        weeklyReportRawData[0].data["Bug in Test Case"],
        weeklyReportRawData[1].data["Bug in Test Case"],
        weeklyReportRawData[2].data["Bug in Test Case"],
        weeklyReportRawData[3].data["Bug in Test Case"],
      ]
    },
    {
      type_data: "Code Collision",
      role: "Software Engineer",
      data: [
        weeklyReportRawData[0].data["Code Collison"],
        weeklyReportRawData[1].data["Code Collison"],
        weeklyReportRawData[2].data["Code Collison"],
        weeklyReportRawData[3].data["Code Collison"],
      ]
    },
    {
      type_data: "Adjustment Test Case",
      role: "Software Quality Assurance",
      data: [
        weeklyReportRawData[0].data["Adjustment Test Case"],
        weeklyReportRawData[1].data["Adjustment Test Case"],
        weeklyReportRawData[2].data["Adjustment Test Case"],
        weeklyReportRawData[3].data["Adjustment Test Case"],
      ]
    },
    {
      type_data: "Out of Requirement",
      role: "Product",
      data: [
        weeklyReportRawData[0].data["Out of Requirement"],
        weeklyReportRawData[1].data["Out of Requirement"],
        weeklyReportRawData[2].data["Out of Requirement"],
        weeklyReportRawData[3].data["Out of Requirement"],
      ]
    },
    {
      type_data: "Changes Requirement",
      role: "Product",
      data: [
        weeklyReportRawData[0].data["Changes Requirement"],
        weeklyReportRawData[1].data["Changes Requirement"],
        weeklyReportRawData[2].data["Changes Requirement"],
        weeklyReportRawData[3].data["Changes Requirement"],
      ]
    },
    {
      type_data: "Changes Design",
      role: "Designer",
      data: [
        weeklyReportRawData[0].data["Changes Design"],
        weeklyReportRawData[1].data["Changes Design"],
        weeklyReportRawData[2].data["Changes Design"],
        weeklyReportRawData[3].data["Changes Design"],
      ]
    },
    {
      type_data: "Bug Existing Feature",
      role: "All Team",
      data: [
        weeklyReportRawData[0].data["Bug Existing Feature"],
        weeklyReportRawData[1].data["Bug Existing Feature"],
        weeklyReportRawData[2].data["Bug Existing Feature"],
        weeklyReportRawData[3].data["Bug Existing Feature"],
      ]
    },
  ];

  const bugSegmentationArray = [
    "INSUFFICIENT GROOMING",
    "COMMUNICATION BREAKDOWN",
    "HUMAN ERROR",
    "PAIRING ISSUE",
    "WRITING SKILLS",
    "OTHERS"
  ]
  const weeklyBugSegmentation = bugSegmentationArray.map((segment) => {
    return {
      type_data: segment,
      role: "Platform Engineer",
      data: Array.from({ length: 4 }).map((_, i) => {
        return weeklyReportRawData[i].dataSegmentation[segment];
      })
    }
  })

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          Bug Report
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-y-6">
        <DataTable
            columns={weeklyReportColumns}
            data={weeklyReportData}
        />
        <div className="flex flex-col gap-y-3 font-bold">
          Bug Case Not Covered Segmentation
          <DataTable
              columns={weeklyReportColumns}
              data={weeklyBugSegmentation}
          />
        </div>
      </CardContent>
    </Card>
  );
}
