"use client";

import {useQueries} from "@tanstack/react-query";
import axios from "axios";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { API_BASE_URL } from "@/constants";
import { format } from "date-fns";
import {calculatePercentageChange, PercentageChange} from "@/app/pe/performance-analysis/components/weekly-report-bug";
import {deploymentReleaseDate, isDateBetween} from "@/app/pe/performance-analysis/components/weekly-report-breakdown";
import {getGroupedActivity} from "@/lib/utils";


export function WeeklyReportOperation({ lastWeeksRange }) {
  const weeklyReportColumns: ColumnDef<any>[] = [
    {
      accessorKey: "/",
      header: "/",
      cell: ({ row }) => (
          <div className="truncate font-bold">
            {row.original.type_data}
          </div>
      ),
      enableSorting: false,
      enableColumnFilter: true,
    },
  ];
  const weeklyQueries = useQueries({
    queries: lastWeeksRange.map((week) => ({
      queryKey: ["worklog", week.start_date, week.end_date, "pe"],
      queryFn: async () => {
        const res = await axios.get(API_BASE_URL + `/api/jira/report`, {
          headers: {
            Accept: "application/json",
          },
          params: {
            start_date: week.start_date
                ? format(new Date(week.start_date), "yyyy-MM-dd")
                : undefined,
            end_date: week.end_date
                ? format(new Date(week.end_date), "yyyy-MM-dd")
                : undefined,
          },
        });
        return res.data.issues;
      },
      suspense: true, // jika kamu pakai suspense
    })),
  });
  const weeklyReportRawData = lastWeeksRange.map((week, index) => {
    const data = (weeklyQueries[index] as any).data;
    const tagRelease = deploymentReleaseDate.some((deployment) => {
      return isDateBetween(deployment, week.start_date, week.end_date);
    });
    weeklyReportColumns.push({
      accessorKey: `${week.start_date} - ${week.end_date}`,
      header: `${week.start_date} - ${week.end_date} ${tagRelease ? '(R)' : ''}`,
      cell: ({ row }) => {
        const percentageChanges = index > 0 ? calculatePercentageChange(row.original.data[index-1], row.original.data[index]) : 0;
        return (
          <div className="truncate max-w-[120px] font-bold">
            {row.original.data[index]?.toFixed(2) || 0}
            {<PercentageChange indexColumn={index} value={percentageChanges} />}
          </div>
        )
      },
      enableSorting: false,
      enableColumnFilter: true,
    });

    const activityCounts = data.reduce((acc, item) => {
      const match = item.work_log_comment.match(/\[([^\]]+)\]/);
      const activity = match ? match[1] : item.work_log_comment.split("\n")[0];
      const groupedActivity = getGroupedActivity(item, activity);
      acc[groupedActivity] = (acc[groupedActivity] || 0) + item.work_log_time_spent_hour;
      return acc;
    }, {} as Record<string, number>);

    const codeSceneCounts = data.reduce((acc, item) => {
      if (item.work_log_comment.includes("CODESCENE")) {
        const codesSceneComment = item.work_log_comment.split("\n");
        codesSceneComment.forEach((line) => {
          const match = line.match(/\[([^\]]+)\]/);
          const codeScene = match ? match[1] : "";
          const codeSceneScore = codeScene.split(",");
          if (codeSceneScore) {
            acc[codeSceneScore[1]] = codeSceneScore[2];
          }
        })
      }
      return acc;
    }, {} as Record<string, number>)

    return {
      start_date: week.start_date,
      end_date: week.end_date,
      data: activityCounts,
      codeScene: codeSceneCounts
    }
  });
  const weeklyReportData = [
    {
      type_data: "Pairing & Review / Support",
      data: weeklyReportRawData.map((week) => {
        return week.data["OPERATION"]
      })
    },
    {
      type_data: "Project SA",
      data: weeklyReportRawData.map((week) => {
        return week.data["PROJECT SA"]
      })
    },
    {
      type_data: "Meeting Hours",
      data: weeklyReportRawData.map((week) => {
        return week.data["MEETING"]
      })
    },
    {
      type_data: "Resolve Issue",
      data: weeklyReportRawData.map((week) => {
        return week.data["ISSUE"]
      })
    }
  ];

  const weeklyReportCodeScene = [
    {
      type_data: "Hydra",
      data: weeklyReportRawData.map((week) => {
        return parseFloat(week.codeScene["HYDRA"]) || 0
      })
    },
    {
      type_data: "Horde",
      data: weeklyReportRawData.map((week) => {
        return parseFloat(week.codeScene["HORDE"]) || 0
      })
    },
    {
      type_data: "Gober",
      data: weeklyReportRawData.map((week) => {
        return parseFloat(week.codeScene["GOBER"]) || 0
      })
    },
    {
      type_data: "Pegasus",
      data: weeklyReportRawData.map((week) => {
        return parseFloat(week.codeScene["PEGASUS"]) || 0
      })
    },
    {
      type_data: "Medusa",
      data: weeklyReportRawData.map((week) => {
        return parseFloat(week.codeScene["MEDUSA"]) || 0
      })
    },
    {
      type_data: "Account",
      data: weeklyReportRawData.map((week) => {
        return parseFloat(week.codeScene["ACCOUNT"]) || 0
      })
    },
    {
      type_data: "Payroll",
      data: weeklyReportRawData.map((week) => {
        return parseFloat(week.codeScene["PAYROLL"]) || 0
      })
    },
    {
      type_data: "Dispatch",
      data: weeklyReportRawData.map((week) => {
        return parseFloat(week.codeScene["DISPATCH"]) || 0
      })
    },
    {
      type_data: "Shipment",
      data: weeklyReportRawData.map((week) => {
        return parseFloat(week.codeScene["SHIPMENT"]) || 0
      })
    },
    {
      type_data: "Average",
      data: weeklyReportRawData.map((week) => {
        const totalCodescene = Object.entries(week.codeScene).reduce((sum, score) => {
          return sum + parseFloat((score as any)[1]);
        }, 0);


        return (totalCodescene / Object.entries(week.codeScene).length)
      })
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          Operation Report
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-y-6">
        <DataTable
            columns={weeklyReportColumns as any}
            data={weeklyReportData as any}
        />
        <div className="flex flex-col gap-y-3 font-bold">
          Codescene Report
          <DataTable
              columns={weeklyReportColumns as any}
              data={weeklyReportCodeScene as any}
          />
        </div>
      </CardContent>
    </Card>
  );
}
