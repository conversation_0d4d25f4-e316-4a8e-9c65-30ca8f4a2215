"use client";

import {useQueries} from "@tanstack/react-query";
import axios from "axios";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { API_BASE_URL } from "@/constants";
import {format} from "date-fns";
import {calculatePercentageChange, PercentageChange} from "@/app/pe/performance-analysis/components/weekly-report-bug";
import {
  getBacklogEpics,
  getDoneBacklogEpics,
  getMondayWednesdayEpics,
  getThursdayFridayEpics,
  getThursdayFridayStoryPoints,
  getMondayWednesdayStoryPoints,
  getDoneWeeklyEpics,
  getTotalStoryPointsFromBacklog,
  getCalculateDoneStoryPoints,
  getTotalBreakdownHours,
  getNotDoneWeeklyEpics
} from "@/lib/utils";

interface WeeklyReportBreakdownProps {
  uniqueUserIds: string[];
  lastWeeksRange: any[];
}
export const deploymentReleaseDate = ["2025-06-12", "2025-06-19", "2025-06-26", "2025-07-03", "2025-07-17", "2025-07-09", "2025-07-24", "2025-07-31"];

export function isDateBetween(targetDate, startDate, endDate) {
  const target = new Date(targetDate);
  const start = new Date(startDate);
  const end = new Date(endDate);

  return target >= start && target <= end;
}

export function WeeklyReportBreakdown({ uniqueUserIds, lastWeeksRange }: WeeklyReportBreakdownProps) {

  const weeklyReportColumns: ColumnDef<any>[] = [
    {
      accessorKey: "/",
      header: "/",
      cell: ({ row }) => (
          <div className="font-bold">
            {row.original.type_data}
          </div>
      ),
      enableSorting: false,
      enableColumnFilter: true,
    },
  ];
  const weeklyQueries = useQueries({
    queries: lastWeeksRange.map((week) => ({
      queryKey: ["team-epics", week.start_date, week.end_date, uniqueUserIds],
      queryFn: async () => {
        if (uniqueUserIds.length === 0) return { epics: [] };

        const res = await axios.get(API_BASE_URL + `/api/jira/epic`, {
          headers: { Accept: "application/json" },
          params: {
            start_date: week.start_date
                ? format(new Date(week.start_date), "yyyy-MM-dd")
                : undefined,
            end_date: week.end_date
                ? format(new Date(week.end_date), "yyyy-MM-dd")
                : undefined,
            user_ids: uniqueUserIds,
          },
        });

        return {
          epics: res.data.epics,
        }
      },
      suspense: true
    })),
  });
  const weeklyReportRawData = lastWeeksRange.map((week, index) => {
    const epicData = (weeklyQueries[index] as any).data;

    const tagRelease = deploymentReleaseDate.some((deployment) => {
      return isDateBetween(deployment, week.start_date, week.end_date);
    });
    weeklyReportColumns.push({
      accessorKey: `${week.start_date} - ${week.end_date}`,
      header: `${week.start_date} - ${week.end_date} ${tagRelease ? '(R)' : ''}`,
      cell: ({ row }) => {
        const currentData = row.original.data[index];
        const prevData = row.original.data[index - 1];

        if (!currentData) return null;

        if (row.original.type_data === "Breakdown SLA (SP/Hours)") {
          const percentageChangesSLA = index > 0
              ? calculatePercentageChange(prevData?.totalHours || 0, currentData.totalHours)
              : 0;

          return (
              <div className="truncate max-w-[160px] font-bold">
                {currentData.totalHours.toFixed(2)} SP/Hour
                <PercentageChange indexColumn={index} value={percentageChangesSLA} />
              </div>
          );
        }

        if (row.original.type_data === "Breakdown Hours") {
          const percentageChangesHours = index > 0
              ? calculatePercentageChange(prevData?.totalHours || 0, currentData.totalHours)
              : 0;

          return (
              <div className="truncate max-w-[160px] font-bold">
                {currentData.totalHours.toFixed(2)} Hours
                <PercentageChange indexColumn={index} value={percentageChangesHours} />
              </div>
          );
        }

        const percentageChangesStoryPoint = index > 0
            ? calculatePercentageChange(prevData?.totalStoryPoint || 0, currentData.totalStoryPoint)
            : 0;

        return (
            <div className="truncate max-w-[160px] font-bold">
              {currentData.totalEpic} ({currentData.totalStoryPoint} SP)
              <PercentageChange indexColumn={index} value={percentageChangesStoryPoint} />
            </div>
        );
      },
      enableSorting: false,
      enableColumnFilter: true,
    });

    return {
      start_date: week.start_date,
      end_date: week.end_date,
      data: epicData.epics,
    };
  });

  function calculateRemainingEpicAndStoryPoint(weeklyReport) {
    return {
      totalEpic: getThursdayFridayEpics(weeklyReport.data, weeklyReport).length + getMondayWednesdayEpics(weeklyReport.data, weeklyReport).length + getBacklogEpics(weeklyReport.data, weeklyReport).length - (getDoneBacklogEpics(weeklyReport.data, weeklyReport).length + getDoneWeeklyEpics(getMondayWednesdayEpics(weeklyReport.data, weeklyReport)).length + getDoneWeeklyEpics(getThursdayFridayEpics(weeklyReport.data, weeklyReport)).length),
      totalStoryPoint: getThursdayFridayStoryPoints(weeklyReport.data, weeklyReport) + getMondayWednesdayStoryPoints(weeklyReport.data, weeklyReport) + getTotalStoryPointsFromBacklog(weeklyReport.data, weeklyReport) - (getCalculateDoneStoryPoints(getThursdayFridayEpics(weeklyReport.data, weeklyReport), weeklyReport) + getCalculateDoneStoryPoints(getMondayWednesdayEpics(weeklyReport.data, weeklyReport), weeklyReport) + getCalculateDoneStoryPoints(getBacklogEpics(weeklyReport.data, weeklyReport), weeklyReport)),
    }
  }
  const weeklyReportData = [
    {
      type_data: "Backlog Epic",
      data: weeklyReportRawData.map((weeklyReport) => {
        return {
          totalEpic: getBacklogEpics(weeklyReport.data, weeklyReport).length,
          totalStoryPoint: getTotalStoryPointsFromBacklog(weeklyReport.data, weeklyReport),
        }
      }),
    },
    {
      type_data: "Backlog Epic Done",
      data: weeklyReportRawData.map((weeklyReport) => {
        return {
          totalEpic: getDoneBacklogEpics(weeklyReport.data, weeklyReport).length,
          totalStoryPoint: getCalculateDoneStoryPoints(getBacklogEpics(weeklyReport.data, weeklyReport), weeklyReport),
        }
      }),
    },
    {
      type_data: "Monday-Wednesday Epic",
      data: weeklyReportRawData.map((weeklyReport) => {
        return {
          totalEpic: getMondayWednesdayEpics(weeklyReport.data, weeklyReport).length,
          totalStoryPoint: getMondayWednesdayStoryPoints(weeklyReport.data, weeklyReport),
        }
      })
    },
    {
      type_data: "Monday-Wednesday Epic Done",
      data: weeklyReportRawData.map((weeklyReport) => {
        return {
          totalEpic: getDoneWeeklyEpics(getMondayWednesdayEpics(weeklyReport.data, weeklyReport)).length,
          totalStoryPoint: getCalculateDoneStoryPoints(getMondayWednesdayEpics(weeklyReport.data, weeklyReport), weeklyReport),
        }
      })
    },
    {
      type_data: "Thursday-Friday Epic",
      data: weeklyReportRawData.map((weeklyReport) => {
        return {
          totalEpic: getThursdayFridayEpics(weeklyReport.data, weeklyReport).length,
          totalStoryPoint: getThursdayFridayStoryPoints(weeklyReport.data, weeklyReport),
        }
      })
    },
    {
      type_data: "Thursday-Friday Epic Done",
      data: weeklyReportRawData.map((weeklyReport) => {
        return {
          totalEpic: getDoneWeeklyEpics(getThursdayFridayEpics(weeklyReport.data, weeklyReport)).length,
          totalStoryPoint: getCalculateDoneStoryPoints(getThursdayFridayEpics(weeklyReport.data, weeklyReport), weeklyReport),
        }
      })
    },
    {
      type_data: "Total Epic Done",
      data: weeklyReportRawData.map((weeklyReport) => {
        return {
          totalEpic: getDoneBacklogEpics(weeklyReport.data, weeklyReport).length + getDoneWeeklyEpics(getMondayWednesdayEpics(weeklyReport.data, weeklyReport)).length + getDoneWeeklyEpics(getThursdayFridayEpics(weeklyReport.data, weeklyReport)).length,
          totalStoryPoint: getCalculateDoneStoryPoints(getThursdayFridayEpics(weeklyReport.data, weeklyReport), weeklyReport) + getCalculateDoneStoryPoints(getMondayWednesdayEpics(weeklyReport.data, weeklyReport), weeklyReport) + getCalculateDoneStoryPoints(getBacklogEpics(weeklyReport.data, weeklyReport), weeklyReport),
        }
      })
    },
    {
      type_data: "Breakdown Hours",
      data: weeklyReportRawData.map((weeklyReport) => {
        return {
          totalHours: getTotalBreakdownHours(weeklyReport.data, weeklyReport)
        }
      })
    },
    {
      type_data: "Breakdown SLA (SP/Hours)",
      data: weeklyReportRawData.map((weeklyReport) => {
        return {
          totalHours: getTotalBreakdownHours(weeklyReport.data, weeklyReport) > 0 ? (getCalculateDoneStoryPoints(weeklyReport.data, weeklyReport) / getTotalBreakdownHours(weeklyReport.data, weeklyReport)) : 0
        }
      })
    },
    {
      type_data: "Remaining Epic",
      data: weeklyReportRawData.map((weeklyReport) => {
        return calculateRemainingEpicAndStoryPoint(weeklyReport)
      })
    },
  ];

  function compareBetweenEpic(a, b) {
    const epicBefore = a.flatMap(epic => epic.epic_link);
    const epicAfter = b.flatMap(epic => epic.epic_link);
    const onlyInFirst = epicBefore.filter(
        (item) => !epicAfter.includes(item)
    );
    const onlyInSecond = epicAfter.filter(
        (item) => !epicBefore.includes(item)
    );
    return {
      onlyInFirst,
      onlyInSecond
    }
  }
  function checkAnomaliesData(weeklyReportDataBefore, weeklyReportDataCurrent) {
    const allBacklogEpicCurrent = getBacklogEpics(weeklyReportDataCurrent.data, weeklyReportDataCurrent);
    const allRemainingEpicBefore = getNotDoneWeeklyEpics(weeklyReportDataBefore.data);
    const totalRemainingEpicAndStoryPointDataBefore = calculateRemainingEpicAndStoryPoint(weeklyReportDataBefore);
    const backlogEpicAndStoryPointDataCurrent = {
      totalEpic:allBacklogEpicCurrent.length,
      totalStoryPoint: getTotalStoryPointsFromBacklog(weeklyReportDataCurrent.data, weeklyReportDataCurrent),
    }
    if (totalRemainingEpicAndStoryPointDataBefore.totalEpic !== backlogEpicAndStoryPointDataCurrent.totalEpic || totalRemainingEpicAndStoryPointDataBefore.totalStoryPoint !== backlogEpicAndStoryPointDataCurrent.totalStoryPoint) {
      const anomalyEpic = compareBetweenEpic(allRemainingEpicBefore, allBacklogEpicCurrent);
      if (anomalyEpic.onlyInFirst.length > 0) {
        console.log(`anomaly not carry over ${weeklyReportDataBefore.start_date} - ${weeklyReportDataBefore.end_date}`, anomalyEpic.onlyInFirst);
      }
      if (anomalyEpic.onlyInSecond.length > 0) {
        console.log(`anomaly not exist before ${weeklyReportDataCurrent.start_date} - ${weeklyReportDataCurrent.end_date}`, anomalyEpic.onlyInSecond);
      }
    }
  }
  weeklyReportRawData.forEach((weeklyReport, index) => {
    if (index > 0) {
      checkAnomaliesData(weeklyReportRawData[index - 1], weeklyReport);
    }
  })

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          Breakdown Report
        </CardTitle>
      </CardHeader>
      <CardContent>
        <DataTable
            columns={(weeklyReportColumns as any)}
            data={(weeklyReportData as any)}
        />
      </CardContent>
    </Card>
  );
}
