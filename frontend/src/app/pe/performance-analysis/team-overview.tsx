"use client";

import React, {Suspense} from "react";
import axios from "axios";
import { RootState } from "@/app/store";
import { useSuspenseQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import {usePathname, useRouter, useSearchParams} from "next/navigation";
import { useSelector } from "react-redux";
import { WorkLogTable } from "@/components/work-log-table";
import { ActivityChart } from "@/components/performance-analysis/activity-chart";
// import { PerformanceMetricsCards } from "@/components/performance-analysis/performance-metrics-cards";
import { TimeDistributionChart } from "@/components/performance-analysis/time-distribution-chart";
import { TeamEpics } from "@/components/performance-analysis/team-epics";
import { TeamBugs } from "@/components/performance-analysis/team-bugs";
import { LPLoading } from "@/components/lp-loading";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON><PERSON>rigger, TabsContent } from "@/components/ui/tabs";
import { API_BASE_URL } from "@/constants";
import {WeeklyReport} from "@/app/pe/performance-analysis/components/weekly-report";
import {TeamMembers} from "@/app/pe/performance-analysis/components/team-members";
import {SomethingWentWrong} from "@/components/something-went-wrong";
import {BackendPE, FrontendPE} from "@/lib/utils";

export function TeamOverview({ jql }: { jql?: string }) {
  const router = useRouter();
  const pathname = usePathname();
  const role = pathname.split("/")[1] || "pe";
  const searchParams = useSearchParams();
  const tabValueParams = searchParams.get("tab") || "members";

  function onSelectTabs(index: any) {
    router.push(`${pathname}?tab=${index}`);
  }

  const { startDate, endDate } = useSelector(
    (state: RootState) => state.dateFilter
  );

  const { data, isError } = useSuspenseQuery({
    queryKey: ["worklog", startDate, endDate, role],
    queryFn: async () => {
      const res = await axios.get(API_BASE_URL + `/api/jira/report`, {
        headers: {
          Accept: "application/json",
        },
        params: {
          start_date: startDate
            ? format(new Date(startDate), "yyyy-MM-dd")
            : undefined,
          end_date: endDate
            ? format(new Date(endDate), "yyyy-MM-dd")
            : undefined,
          jql,
        },
      });
      return res.data.issues.filter((e) => !e.name.includes("Bian") && !e.name.includes("andrean") && !e.name.includes("Adam Nasrudin") && !e.name.includes("Tedja"));
    },
  });

  if (isError) {
    return (
        <SomethingWentWrong />
    );
  }

    const uniqueUserIds: string[] = Array.from(
        new Set([...FrontendPE, ...BackendPE].map((item) => item.id))
    );

  return (
    <div className="space-y-6">
      {/* Team Metrics */}
      {/*<PerformanceMetricsCards data={data} />*/}

      {/* Tabs for team views */}
      <Tabs defaultValue={tabValueParams} onValueChange={(index) => onSelectTabs(index)} className="w-full">
        <TabsList className="grid w-full md:w-2/3 grid-cols-4 mb-4">
          <TabsTrigger value="members">Team Members</TabsTrigger>
          <TabsTrigger value="epics">Team Epics</TabsTrigger>
          <TabsTrigger value="bugs">Team Bugs</TabsTrigger>
          <TabsTrigger value="weekly_report">Weekly Report</TabsTrigger>
        </TabsList>

        {/* Members Tab */}
        <TabsContent value="members" className="space-y-4">
          <TeamMembers dataWorklog={data} />
          {/* Analytics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <TimeDistributionChart data={data} role="pe" />
            <ActivityChart data={data} role="pe" />
          </div>

          {/* Work Log Table */}
          <WorkLogTable data={data} key="1" />
        </TabsContent>

        {/* Epics & Bugs Tabs with Suspense loading fallback */}
        <TabsContent value="epics" className="space-y-4">
          <Suspense
            fallback={
              <div className="flex items-center justify-center h-60">
                <LPLoading />
              </div>
            }
          >
            <TeamEpics uniqueUserIds={uniqueUserIds} />
          </Suspense>
        </TabsContent>

        <TabsContent value="bugs" className="space-y-4">
          <Suspense
            fallback={
              <div className="flex items-center justify-center h-60">
                <LPLoading />
              </div>
            }
          >
            <TeamBugs />
          </Suspense>
        </TabsContent>

        {/* Weekly Report Platform Engineer */}
        <TabsContent value="weekly_report" className="space-y-4">
          <Suspense
              fallback={
                <div className="flex items-center justify-center h-60">
                  <LPLoading />
                </div>
              }
          >
            <WeeklyReport uniqueUserIds={uniqueUserIds} />
          </Suspense>
        </TabsContent>
      </Tabs>
    </div>
  );
}
