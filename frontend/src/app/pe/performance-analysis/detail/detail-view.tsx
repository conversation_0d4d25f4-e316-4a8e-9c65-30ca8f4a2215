"use client";

import { DateFilter } from "@/components/date-filter";
import { LPLoading } from "@/components/lp-loading";
import { EngineerBugs } from "@/components/performance-analysis/engineer-bugs";
import { EngineerDetailView } from "@/app/pe/performance-analysis/detail/engineer-detail-view";
import { Suspense } from "react";

export function DetailView() {
  return (
    <>
      <DateFilter />
      <Suspense fallback={<LPLoading />}>
        <div className="space-y-6 bg-back">
          <EngineerDetailView />
          <Suspense fallback={<LPLoading />}>
            <EngineerBugs />
          </Suspense>
        </div>
      </Suspense>
    </>
  );
}
