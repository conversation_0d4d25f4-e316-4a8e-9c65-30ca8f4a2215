@import "tailwindcss";
@import "tw-animate-css";
@plugin "@tailwindcss/typography";

@custom-variant dark (&:is(.dark *));

/* Enhanced smooth transitions for theme switching */
* {
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

:root {
  --radius: 0.625rem;
  --background: oklch(0.99 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.527 0.224 29.6);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.985 0 0);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.1577 0.0099 255.6869);
  --foreground: oklch(0.9761 0 0);
  --card: oklch(0.1998 0.0086 264.36);
  --card-foreground: oklch(0.9761 0 0);
  --popover: oklch(0.1998 0.0086 264.36);
  --popover-foreground: oklch(0.9761 0 0);
  --primary: oklch(0.4006 0.1843 27.5917);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.6201 0.1565 53.4649);
  --secondary-foreground: oklch(0.9761 0 0);
  --muted: oklch(0.3867 0 0);
  --muted-foreground: oklch(0.9761 0 0);
  --accent: oklch(0.4006 0.1843 27.5917);
  --accent-foreground: oklch(0.9761 0 0);
  --destructive: oklch(0.5308 0.2178 29.2339);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.3211 0 0);
  --input: oklch(0.2998 0.0086 164.36);
  --ring: oklch(0.5006 0.1843 27.5917);
  --chart-1: oklch(0.5006 0.1843 27.5917);
  --chart-2: oklch(0.6201 0.1565 53.4649);
  --chart-3: oklch(0.5875 0.2119 344.8041);
  --chart-4: oklch(0.5271 0.1723 258.9756);
  --chart-5: oklch(0.4824 0.2178 291.1318);
  --sidebar: oklch(0.1577 0.0099 255.6869);
  --sidebar-foreground: oklch(0.9761 0 0);
  --sidebar-primary: oklch(0.5006 0.1843 27.5917);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.5006 0.1843 27.5917);
  --sidebar-accent-foreground: oklch(0.9761 0 0);
  --sidebar-border: oklch(0.3211 0 0);
  --sidebar-ring: oklch(0.5006 0.1843 27.5917);
  --font-sans: Outfit, sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    "Liberation Mono", "Courier New", monospace;
  --radius: 8px;
  --shadow-2xs: 0px 2px 8px 0px hsl(0 0% 0% / 0.2);
  --shadow-xs: 0px 2px 8px 0px hsl(0 0% 0% / 0.2);
  --shadow-sm: 0px 2px 8px 0px hsl(0 0% 0% / 0.4),
    0px 1px 2px -1px hsl(0 0% 0% / 0.4);
  --shadow: 0px 2px 8px 0px hsl(0 0% 0% / 0.4),
    0px 1px 2px -1px hsl(0 0% 0% / 0.4);
  --shadow-md: 0px 2px 8px 0px hsl(0 0% 0% / 0.4),
    0px 2px 4px -1px hsl(0 0% 0% / 0.4);
  --shadow-lg: 0px 2px 8px 0px hsl(0 0% 0% / 0.4),
    0px 4px 6px -1px hsl(0 0% 0% / 0.4);
  --shadow-xl: 0px 2px 8px 0px hsl(0 0% 0% / 0.4),
    0px 8px 10px -1px hsl(0 0% 0% / 0.4);
  --shadow-2xl: 0px 2px 8px 0px hsl(0 0% 0% / 1);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-normal: var(--tracking-normal);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

body {
  letter-spacing: var(--tracking-normal);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
