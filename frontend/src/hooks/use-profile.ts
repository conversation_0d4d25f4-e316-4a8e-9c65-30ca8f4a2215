"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../lib/api-client";

interface User {
  id: number;
  username: string;
  display_name: string;
  role: string;
  is_approved: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface UpdateProfileRequest {
  display_name: string;
}

interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
}

export function useProfile() {
  return useQuery<User>({
    queryKey: ["profile"],
    queryFn: async () => {
      const response = await apiClient.get<User>("/api/auth/profile");

      if (!response.ok) {
        throw new Error(response.error || "Failed to fetch profile");
      }

      return response.data!;
    },
  });
}

export function useUpdateProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateProfileRequest) => {
      const response = await apiClient.put<User>("/api/auth/profile", data);

      if (!response.ok) {
        throw new Error(response.error || "Failed to update profile");
      }

      return response.data!;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["profile"] });
    },
  });
}

export function useChangePassword() {
  return useMutation({
    mutationFn: async (data: ChangePasswordRequest) => {
      const response = await apiClient.post("/api/auth/change-password", data);

      if (!response.ok) {
        throw new Error(response.error || "Failed to change password");
      }

      return response.data;
    },
  });
}
