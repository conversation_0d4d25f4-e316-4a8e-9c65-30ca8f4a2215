import * as React from "react";

const MOBILE_BREAKPOINT = 768;

export function useIsMobile() {
  // Initialize with false for SSR consistency
  const [isMobile, setIsMobile] = React.useState<boolean>(false);
  // Track if we're on the client to prevent hydration mismatches
  const [isClient, setIsClient] = React.useState<boolean>(false);

  React.useEffect(() => {
    // Mark that we're now on the client
    setIsClient(true);

    // Create media query list
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);

    // Debounce timeout ref
    let timeoutId: NodeJS.Timeout;

    // Handler function that uses the media query result directly
    const onChange = (event: MediaQueryListEvent) => {
      // Clear any pending updates
      clearTimeout(timeoutId);

      // Debounce the state update to prevent rapid re-renders
      timeoutId = setTimeout(() => {
        setIsMobile(event.matches);
      }, 100); // 100ms debounce
    };

    // Set initial value based on media query (no debounce for initial)
    setIsMobile(mql.matches);

    // Use the newer addEventListener API
    mql.addEventListener("change", onChange);

    // Cleanup function
    return () => {
      clearTimeout(timeoutId);
      mql.removeEventListener("change", onChange);
    };
  }, []);

  // Return false during SSR, actual value after hydration
  return isClient ? isMobile : false;
}
