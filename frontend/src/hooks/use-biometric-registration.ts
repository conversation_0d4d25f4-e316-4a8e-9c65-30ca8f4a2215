"use client";

import { useState } from "react";
import { useWebAuthn } from "./use-webauthn";
import {
  parseCredentialCreationOptions,
  formatCredentialForServer,
} from "../lib/webauthn";
import { apiClient } from "../lib/api-client";

interface BiometricRegistrationOptions {
  deviceName?: string;
}

export function useBiometricRegistration() {
  const [isRegistering, setIsRegistering] = useState(false);
  const [registrationError, setRegistrationError] = useState<string | null>(
    null
  );
  const { checkWebAuthnSupport, checkPlatformAuthenticator, accessToken } =
    useWebAuthn();

  const registerBiometric = async (
    options: BiometricRegistrationOptions = {}
  ) => {
    if (!accessToken) {
      throw new Error(
        "User must be logged in to register biometric authentication"
      );
    }

    // Check WebAuthn support
    if (!checkWebAuthnSupport()) {
      throw new Error("WebAuthn is not supported on this device");
    }

    // Check if platform authenticator is available
    const platformAvailable = await checkPlatformAuthenticator();
    if (!platformAvailable) {
      throw new Error(
        "Platform authenticator (biometric) is not available on this device"
      );
    }

    setIsRegistering(true);
    setRegistrationError(null);

    try {
      const deviceName =
        options.deviceName ||
        `${navigator.platform} - ${new Date().toLocaleDateString()}`;

      // Step 1: Begin device registration
      const beginResponse = await apiClient.post("/api/auth/devices/register", {
        device_name: deviceName,
      });

      if (!beginResponse.ok) {
        throw new Error(
          beginResponse.error || "Failed to begin biometric registration"
        );
      }

      const registrationOptions = beginResponse.data;

      // Step 2: Create credential using the same parsing logic as webauthn.ts
      const parsedOptions = parseCredentialCreationOptions(registrationOptions);
      const credential = (await navigator.credentials.create(
        parsedOptions
      )) as PublicKeyCredential;

      if (!credential) {
        throw new Error("Failed to create biometric credential");
      }

      // Step 3: Finish device registration
      const credentialData = formatCredentialForServer(credential);
      const finishResponse = await apiClient.post(
        "/api/auth/devices/register/finish",
        {
          device_name: deviceName,
          credential: credentialData,
        },
        {
          headers: {
            "X-Session-ID": registrationOptions.sessionId || "",
          },
        }
      );

      if (!finishResponse.ok) {
        throw new Error(
          finishResponse.error || "Failed to complete biometric registration"
        );
      }

      return finishResponse.data;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Biometric registration failed";
      setRegistrationError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsRegistering(false);
    }
  };

  const clearError = () => {
    setRegistrationError(null);
  };

  return {
    registerBiometric,
    isRegistering,
    registrationError,
    clearError,
  };
}
