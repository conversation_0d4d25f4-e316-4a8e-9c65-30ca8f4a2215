"use client";

import { useState } from "react";
import { useWebAuthn } from "./use-webauthn";
import {
  parseCredentialCreationOptions,
  formatCredentialForServer,
} from "../lib/webauthn";

interface BiometricRegistrationOptions {
  deviceName?: string;
}

export function useBiometricRegistration() {
  const [isRegistering, setIsRegistering] = useState(false);
  const [registrationError, setRegistrationError] = useState<string | null>(
    null
  );
  const { checkWebAuthnSupport, checkPlatformAuthenticator, accessToken } =
    useWebAuthn();

  const registerBiometric = async (
    options: BiometricRegistrationOptions = {}
  ) => {
    if (!accessToken) {
      throw new Error(
        "User must be logged in to register biometric authentication"
      );
    }

    // Check WebAuthn support
    if (!checkWebAuthnSupport()) {
      throw new Error("WebAuthn is not supported on this device");
    }

    // Check if platform authenticator is available
    const platformAvailable = await checkPlatformAuthenticator();
    if (!platformAvailable) {
      throw new Error(
        "Platform authenticator (biometric) is not available on this device"
      );
    }

    setIsRegistering(true);
    setRegistrationError(null);

    try {
      const deviceName =
        options.deviceName ||
        `${navigator.platform} - ${new Date().toLocaleDateString()}`;

      // Step 1: Begin device registration
      const beginResponse = await fetch("/api/auth/devices/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({ device_name: deviceName }),
      });

      if (!beginResponse.ok) {
        const error = await beginResponse.json();
        throw new Error(
          error.error || "Failed to begin biometric registration"
        );
      }

      const registrationOptions = await beginResponse.json();

      // Step 2: Create credential using the same parsing logic as webauthn.ts
      const parsedOptions = parseCredentialCreationOptions(registrationOptions);
      const credential = (await navigator.credentials.create(
        parsedOptions
      )) as PublicKeyCredential;

      if (!credential) {
        throw new Error("Failed to create biometric credential");
      }

      // Step 3: Finish device registration
      const credentialData = formatCredentialForServer(credential);
      const finishResponse = await fetch("/api/auth/devices/register/finish", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
          "X-Session-ID": registrationOptions.sessionId || "",
        },
        body: JSON.stringify({
          device_name: deviceName,
          credential: credentialData,
        }),
      });

      if (!finishResponse.ok) {
        const error = await finishResponse.json();
        throw new Error(
          error.error || "Failed to complete biometric registration"
        );
      }

      const result = await finishResponse.json();
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Biometric registration failed";
      setRegistrationError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsRegistering(false);
    }
  };

  const clearError = () => {
    setRegistrationError(null);
  };

  return {
    registerBiometric,
    isRegistering,
    registrationError,
    clearError,
  };
}
