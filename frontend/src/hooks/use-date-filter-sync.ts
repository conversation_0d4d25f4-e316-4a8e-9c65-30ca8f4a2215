import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { checkAndUpdateDateFilters } from "@/app/dateFilterSlice";
import { RootState } from "@/app/store";

/**
 * Custom hook to synchronize date filter with current date
 * This ensures that all time-based filters (today, this-week, last-week, this-month, last-month)
 * always show the correct dates even when the app is left open across time boundaries
 */
export function useDateFilterSync() {
  const dispatch = useDispatch();
  const { quickFilter } = useSelector((state: RootState) => state.dateFilter);

  useEffect(() => {
    // Check on mount
    dispatch(checkAndUpdateDateFilters());

    // Check when window regains focus (user comes back to the app)
    const handleFocus = () => {
      dispatch(checkAndUpdateDateFilters());
    };

    // Set up different intervals based on filter type
    let interval: NodeJS.Timeout;

    if (quickFilter === "today") {
      // Check every 10 minutes for "today" filter
      interval = setInterval(() => {
        dispatch(checkAndUpdateDateFilters());
      }, 10 * 60 * 1000); // 10 minutes
    } else if (quickFilter === "this-week" || quickFilter === "last-week") {
      // Check every hour for week-based filters
      interval = setInterval(() => {
        dispatch(checkAndUpdateDateFilters());
      }, 60 * 60 * 1000); // 1 hour
    } else if (quickFilter === "this-month" || quickFilter === "last-month") {
      // Check every 6 hours for month-based filters
      interval = setInterval(() => {
        dispatch(checkAndUpdateDateFilters());
      }, 6 * 60 * 60 * 1000); // 6 hours
    }

    window.addEventListener("focus", handleFocus);

    return () => {
      window.removeEventListener("focus", handleFocus);
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [dispatch, quickFilter]);
}
