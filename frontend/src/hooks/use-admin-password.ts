"use client";

import { useMutation } from "@tanstack/react-query";

interface ChangeUserPasswordRequest {
  userId: number;
  new_password: string;
}

export function useAdminChangeUserPassword() {
  return useMutation({
    mutationFn: async (data: ChangeUserPasswordRequest) => {
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`/api/users/${data.userId}/change-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          new_password: data.new_password,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to change password');
      }

      return response.json();
    },
  });
}