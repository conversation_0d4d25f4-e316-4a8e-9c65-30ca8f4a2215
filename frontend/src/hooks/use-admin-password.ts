"use client";

import { useMutation } from "@tanstack/react-query";
import { apiClient } from "../lib/api-client";

interface ChangeUserPasswordRequest {
  userId: number;
  new_password: string;
}

export function useAdminChangeUserPassword() {
  return useMutation({
    mutationFn: async (data: ChangeUserPasswordRequest) => {
      const response = await apiClient.post(
        `/api/users/${data.userId}/change-password`,
        {
          new_password: data.new_password,
        }
      );

      if (!response.ok) {
        throw new Error(response.error || "Failed to change password");
      }

      return response.data;
    },
  });
}
