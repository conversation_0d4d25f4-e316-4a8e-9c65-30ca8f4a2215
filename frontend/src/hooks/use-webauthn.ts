/**
 * Custom hook for WebAuthn authentication operations
 * Integrates WebAuthn functionality with Redux state management
 */

import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../app/store";
import {
  loginStart,
  loginSuccess,
  loginFailure,
  logout as logoutAction,
  refreshTokenSuccess,
  clearError,
  initializeAuth,
} from "../app/authSlice";
// Helper function to map backend user response to Redux user format
const mapUserResponse = (backendUser: any) => ({
  id: backendUser.id,
  username: backendUser.username,
  displayName: backendUser.display_name,
  role: backendUser.role,
});

import {
  registerWithWebAuthn,
  loginWithWebAuthn,
  loginWithPassword as passwordLogin,
  discoverableLogin,
  refreshAccessToken,
  logout as logoutWebAuthn,
  getWebAuthnErrorMessage,
  isWebAuthnSupported,
  isPlatformAuthenticatorAvailable,
  getDeviceCapabilities,
  type WebAuthnRegistrationRequest,
  type WebAuthnLoginRequest,
  type PasswordLoginRequest,
} from "../lib/webauthn";

export function useWebAuthn() {
  const dispatch = useDispatch();
  const { isAuthenticated, user, accessToken, refreshToken, isLoading, error } =
    useSelector((state: RootState) => state.auth);

  // Initialize authentication state from localStorage
  const initialize = useCallback(() => {
    dispatch(initializeAuth());
  }, [dispatch]);

  // Register a new user with WebAuthn
  const register = useCallback(
    async (request: WebAuthnRegistrationRequest) => {
      dispatch(loginStart());
      try {
        const response = await registerWithWebAuthn(request);
        dispatch(
          loginSuccess({
            user: mapUserResponse(response.user),
            accessToken: response.accessToken,
            refreshToken: response.refreshToken,
          })
        );
        return response;
      } catch {
        const errorMessage = "Registration with passkey failed. Please try again.";
        dispatch(loginFailure(errorMessage));
        throw new Error(errorMessage);
      }
    },
    [dispatch]
  );

  // Login with WebAuthn (username required)
  const login = useCallback(
    async (request: WebAuthnLoginRequest) => {
      dispatch(loginStart());
      try {
        const response = await loginWithWebAuthn(request);
        dispatch(
          loginSuccess({
            user: mapUserResponse(response.user),
            accessToken: response.accessToken,
            refreshToken: response.refreshToken,
          })
        );
        return response;
      } catch {
        const errorMessage = "Sign in with passkey failed. Please try again.";
        dispatch(loginFailure(errorMessage));
        throw new Error(errorMessage);
      }
    },
    [dispatch]
  );

  // Login with password
  const loginWithPassword = useCallback(
    async (request: PasswordLoginRequest) => {
      dispatch(loginStart());
      try {
        const response = await passwordLogin(request);
        dispatch(
          loginSuccess({
            user: mapUserResponse(response.user),
            accessToken: response.accessToken,
            refreshToken: response.refreshToken,
          })
        );
        return response;
      } catch (error) {
        const errorMessage = getWebAuthnErrorMessage(error);
        dispatch(loginFailure(errorMessage));
        throw new Error(errorMessage);
      }
    },
    [dispatch]
  );

  // Discoverable login with WebAuthn (usernameless)
  const loginDiscoverable = useCallback(async () => {
    dispatch(loginStart());
    try {
      const response = await discoverableLogin();
      dispatch(
        loginSuccess({
          user: mapUserResponse(response.user),
          accessToken: response.accessToken,
          refreshToken: response.refreshToken,
        })
      );
      return response;
    } catch {
      const errorMessage = "Sign in with passkey failed. Please try again.";
      dispatch(loginFailure(errorMessage));
      throw new Error(errorMessage);
    }
  }, [dispatch]);

  // Refresh access token
  const refreshTokenAsync = useCallback(async () => {
    if (!refreshToken) {
      dispatch(logoutAction());
      throw new Error("No refresh token available");
    }

    try {
      const response = await refreshAccessToken(refreshToken);
      dispatch(refreshTokenSuccess({ accessToken: response.accessToken }));
      return response.accessToken;
    } catch {
      dispatch(logoutAction());
      throw new Error("Failed to refresh token");
    }
  }, [dispatch, refreshToken]);

  // Logout user
  const logout = useCallback(async () => {
    if (accessToken) {
      try {
        await logoutWebAuthn(accessToken);
      } catch (err) {
        // Continue with logout even if server call fails
        console.warn("Failed to logout on server:", err);
      }
    }
    dispatch(logoutAction());
  }, [dispatch, accessToken]);

  // Clear any authentication errors
  const clearAuthError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Check WebAuthn capabilities
  const checkCapabilities = useCallback(async () => {
    return await getDeviceCapabilities();
  }, []);

  // Check if WebAuthn is supported
  const checkWebAuthnSupport = useCallback(() => {
    return isWebAuthnSupported();
  }, []);

  // Check if platform authenticator is available
  const checkPlatformAuthenticator = useCallback(async () => {
    return await isPlatformAuthenticatorAvailable();
  }, []);

  return {
    // State
    isAuthenticated,
    user,
    accessToken,
    refreshToken,
    isLoading,
    error,

    // Actions
    initialize,
    register,
    login,
    loginWithPassword,
    loginDiscoverable,
    refreshTokenAsync,
    logout,
    clearAuthError,

    // Capabilities
    checkCapabilities,
    checkWebAuthnSupport,
    checkPlatformAuthenticator,
  };
}
