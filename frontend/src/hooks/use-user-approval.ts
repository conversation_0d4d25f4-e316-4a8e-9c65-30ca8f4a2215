"use client";

import { useQuery } from "@tanstack/react-query";

interface ApprovalResponse {
  is_approved: boolean;
  is_rejected: boolean;
  username: string;
}

interface UseUserApprovalOptions {
  username: string;
  enabled?: boolean;
  refetchInterval?: number;
}

export function useUserApproval({ 
  username, 
  enabled = true, 
  refetchInterval = 5000 
}: UseUserApprovalOptions) {
  return useQuery<ApprovalResponse>({
    queryKey: ['user-approval', username],
    queryFn: async () => {
      const response = await fetch('/api/auth/check-approval', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username }),
      });

      if (!response.ok) {
        throw new Error('Failed to check approval status');
      }

      return response.json();
    },
    enabled: enabled && !!username,
    refetchInterval,
  });
}