name: golangci-lint

on:
  pull_request:

permissions:
  contents: read
  pull-requests: read

jobs:
   golangci:
    name: lint
    runs-on: runner
    steps:
      - uses: actions/checkout@v4

      - name: Configure Git for private modules
        run: |
          git config --global url."https://${LP_GITHUB_USERNAME}:${LP_GITHUB_TOKEN}@github.com/".insteadOf "https://github.com/"
        env:
          LP_GITHUB_TOKEN: ${{ secrets.LP_GITHUB_TOKEN }}
          LP_GITHUB_USERNAME: ${{ secrets.LP_GITHUB_USERNAME }}

      - uses: actions/setup-go@v5
        with:
          go-version-file: "go.mod"
          cache: false

      - name: golangci-lint
        uses: golangci/golangci-lint-action@v8
        with:
          version: v2.1
          only-new-issues: true