[app]
port = 8080
env = 'local'
name = 'pentools'

[auth]
jwt_secret = "your-super-secret-jwt-key-change-this-in-production"
jwt_expiration_hours = 24
jwt_refresh_expiration_hours = 168 # 7 days

[webauthn]
# WebAuthn Relying Party Configuration
rp_display_name = "Pentools"
rp_id = "localhost"
rp_origins = ["http://localhost:8080", "https://localhost:8080"]
# rp_icon = ""

# Authentication Configuration
authenticator_attachment = "platform"  # platform, cross-platform, or empty
user_verification = "preferred"        # required, preferred, discouraged
resident_key = "preferred"             # required, preferred, discouraged

# Timeout Configuration
timeout_ms = 300000                    # 5 minutes in milliseconds
registration_timeout_ms = 300000       # 5 minutes in milliseconds
authentication_timeout_ms = 300000     # 5 minutes in milliseconds

# Credential Selection
attestation_preference = "none"        # none, indirect, direct
transport_types = ["usb", "nfc", "ble", "internal"]

# Advanced Settings
require_resident_key = false
debug = false

[jira]
default_jql_get_reporting = 'worklogAuthor IN (712020:b6ff5117-0224-4899-a5fc-7499e7a23188, 5fc60f0b0dd553006fc2a25a, 712020:7727b678-7a39-4627-a7ca-62d9a0da12e1, 712020:fdb9158c-f15a-448d-aa56-61d177b2f510,712020:3b3b163c-7449-42d9-8722-a8b825e1410d,712020:781a66ca-5cf6-40ea-9593-035ba947fd40,712020:e6324922-858c-4a85-a3a2-d6fe359325f7) and work_log_date >= %s and work_log_date <= %s'
secret_key = 'ea'
default_field = 'worklog,issuetype,timetracking,customfield_10080,customfield_10024,customfield_10101,summary,created,creator,customfield_10033'

[sdet]

[sdet.database_master]
host = 'localhost'
user = 'root'
password = 'root'
database = 'pentools'
port = 3306
max_idle_connection = 10
max_open_connection = 100
connection_life_time_in_second = 60

[sdet.database_slave]
host = 'localhost'
user = 'root'
password = 'root'
database = 'pentools'
port = 3306
max_idle_connection = 10
max_open_connection = 100
connection_life_time_in_second = 60

[opensearch]
addresses = ['http://localhost:9200']
username = ''
password = ''
index = 'releases'
# {{ with secret 'config/dev/pentools' }}
# JIRA.SECRET_KEY={{ .Data.JIRA_SECRET_KEY }}
# {{ end }}
